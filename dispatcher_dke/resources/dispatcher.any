# Each farm configures a set of load balanced renders (i.e. remote servers)
/farms
  {
  # First farm entry
  /website
    {
    # Request headers that should be forwarded to the remote server.
    /clientheaders
      {
      # Forward all request headers that are end-to-end. If you want
      # to forward a specific set of headers, you'll have to list
      # them here.
      "*"
      }

    # Hostname globbing for farm selection (virtual domain addressing)
    /virtualhosts
      {
      # Entries will be compared against the "Host" request header
      # and an optional request URL prefix.
      #
      # Examples:
      #
      #   www.company.com
      #   intranet.*
      #   myhost:8888/mysite
      "*"
      }

    # The load will be balanced among these render instances
    /renders
      {
      /rend01
        {
        # Hostname or IP of the render
        /hostname "publisher"
        # Port of the render
        /port "4503"
        # Connect timeout in milliseconds, 0 to wait indefinitely
        # /timeout "0"
        }
      }

    # The filter section defines the requests that should be handled by the dispatcher.
    #
    # Entries can be either specified using globs, or elements of the request line:
    #
    # (1) globs will be compared against the entire request line, e.g.:
    #
    #       /0001 { /type "deny" /glob "* /index.html *" }
    #
    #     denies request "GET /index.html HTTP/1.1" but not "GET /index.html?a=b HTTP/1.1".
    #
    # (2) method/url/query/protocol/path/selectors/extension/suffix will be compared
    #     against the respective elements of  the request line, e.g.:
    #
    #       /0001 { /type "deny" /method "GET" /url "/index.html" }
    #
    #     denies both "GET /index.html" and "GET /index.html?a=b HTTP/1.1".
    #
    # (3) all elements of the request line can also be specified as regular expressions,
    #     which are identified by using single quotes, e.g.
    #
    #       /0001 { /type "allow" /method '(GET|HEAD)' }
    #
    #     allows GET or HEAD requests, or:
    #
    #       /0002 { /type "deny" /extension '()' }
    #
    #     denies requests having no extension.
    #
    # Note: specifying elements of the request line is the preferred method.
    #
  /filter
    {
      /0001 { /type "allow" /glob "*" }
      /0002 { /type "deny" /method "POST" /url "/content/eon-de-showcase/de/start.html" }
      /0003 { /type "deny" /url "*.json" }
      /0004 { /type "deny" /url "*.xml" }
      /0010 { /type "deny" /url "/etc*" }
      /0012 { /type "allow" /url "/etc/designs/*" }
      /0013 { /type "allow" /url "/etc/clientlibs/*" }
      /0014 { /type "allow" /url "/etc/segmentation*.js" }
      /0016 { /type "allow" /url "/etc/tags/*" }
      /0040 { /type "deny" /url "/admin/*" }
      /0041 { /type "deny" /url "/crx/*" }
      /0042 { /type "deny" /url "/system/*" }
      /0043 { /type "allow" /url "/system/sling/logout*" }
      /0051 { /type "deny" /url "/bin/*" }
      /0052 { /type "deny" /url "/apps/*" }
      /0053 { /type "deny" /url "/libs/*" }
      /0054 { /type "allow" /url "/libs/cq/i18n/dict*" }
      /0055 { /type "allow" /url "/libs/cq/security/userinfo.json*" }
      /0056 { /type "deny" /url "/var/*" }
      /0057 { /type "deny" /url "/favicon*" }
      /0058 { /type "deny" /url "/tmp/*" }
      /0059 { /type "deny" /url "/home/<USER>" }
      /0064 { /type "allow" /glob "GET /libs/granite/csrf/token.json*" }
      /0065 { /type "allow" /url "/libs/wcm/stats/tracker*" }
      /0066 { /type "allow" /url "/libs/wcm/mvt/tracker*" }
      /0067 { /type "allow" /url "/bin/statistics/tracker/*" }
      /0070 { /type "allow" /url "/libs/cq/personalization/*" }
      /0080 { /type "deny" /url "*.feed*" }
      /0081 { /type "deny" /url "*.infinity.*" }
      /0082 { /type "deny" /url "*.tidy.*" }
      /0083 { /type "deny" /url "*.sysview.*" }
      /0084 { /type "deny" /url "*.docview.*" }
      /0085 { /type "deny" /url "*.*[0-9].json" }
      /0086 { /type "allow" /url "/etc/eon-de/getnavigation.*.json*" }
      /0090 { /type "deny" /url "*.query*" }
      /0091 { /type "allow" /url "/bin/eon-scg_services/*" }
      /0092 { /type "allow" /url "/bin/digitalsales/*" }
      /0094 { /type "allow" /url "/bin/editFeature*" }
      /0095 { /type "allow" /url "/bin/EditProductTable*" }
      /0096 { /type "allow" /url "/bin/addFeature*" }
      /0097 { /type "allow" /url "/bin/dynamic/featureDropdown*" }
      /0098 { /type "allow" /url "/bin/addproduct*" }
      /0099 { /type "allow" /url "/bin/eon-se/*" }
      /0100 { /type "allow" /url "/bin/servlets/posttoget*" }
      /0101 { /type "allow" /url "*/producttable/jcr:content/par/*.infinity.json" }
      /0102 { /type "allow" /url "/bin/eon_services/mw_api_service*" }
      /0103 { /type "allow" /url "/bin/eon-scg_services/global_text_service" }
      /0104 { /type "allow" /url "/bin/eon-se/alertform.json" }
      /0105 { /type "allow" /url "/bin/eon-se/contactusemail.json" }
      /0106 { /type "allow" /url "/bin/eon-se/editorialList.json" }
      /0107 { /type "allow" /url "/bin/eon-se/getchatURL.json" }
      /0108 { /type "allow" /url "/bin/eon-se/glinksdropdown.json" }
      /0109 { /type "allow" /url "/bin/eon-se/installationlogin.json" }
      /0110 { /type "allow" /url "/bin/eon-se/kundosearchList" }
      /0111 { /type "allow" /url "/bin/eon-se/makeanappointment.json" }
      /0112 { /type "allow" /url "/bin/eon-se/newsList.json" }
      /0113 { /type "allow" /url "/bin/eon-se/Ownersupdation.json" }
      /0114 { /type "allow" /url "/bin/eon-se/searchList.json" }
      /0115 { /type "allow" /url "/bin/eon-se/sendersdropdown.json" }
      /0116 { /type "allow" /url "/bin/eon-se/svgimagedropdown.json" }
      /0117 { /type "allow" /url "/bin/eon-se/informationOwnersGroup.json" }
      /0118 { /type "allow" /url "/bin/eon-se/waiting-time.json" }
      /0119 { /type "allow" /url "*/sitemap.xml" }
      /0120 { /type "allow" /url "*/en_US/sitemap.xml" }
      /0121 { /type "allow" /url "/bin/eon/servlets/*" }
      /0122 { /type "deny" /url "*sitemap.txt" }
      /0123 { /type "deny" /url "debug=*" }
      /0124 { /type "deny" /url "wcmmode=*" }
      /0125 { /type "deny" /url "debugClientLibs=*" }
      /0126 { /type "allow" /url "*/smeproducttable/jcr:content/par/.infinity.json" }
      /0127 { /type "allow" /url "*/smeproducttable/jcr:content/par/energysourceIDs.infinity.json" }
      /0128 { /type "allow" /url "*/smeproducttable/jcr:content/par/agreementTypeIDs.infinity.json" }
      /0129 { /type "allow" /url "*/smeproducttable/jcr:content/par/contractLengthIDs.infinity.json" }
      /0132 { /type "allow" /url "/bin/eon-de/*" }
      /0133 { /type "allow" /url "/etc/eon-de/getproductdetailpages.*.json*" }
      /0134 { /type "allow" /url "/content/dam/eon-de/*/json/*.json" }
      /0135 { /type "allow" /url "/content/eon-de/*/sitemapxml.xml" }
      /0136 { /type "deny" /url "/content/eon-de/*/sitemap.xml" }
      /0137 { /type "allow" /url "*.middleware-api-config.json" }
      /0138 { /type "allow" /url "*.product-details.*.json" }
      /0140 { /type "allow" /method "GET" /url "*/bin/eon-com/*" }
      /0141 { /type "allow" /method "POST" /url "*/bin/eon-com/*" }
      /0142 { /type "allow" /glob "/apps/eon/eon-com/*.ico" }
      /0152 { /type "allow" /url "/content/eon-com/*/*sitemap.xml" }
      /0153 { /type "allow" /url "/libs/granite/dispatcher/content/vanityUrls.html" }
      /0154 { /type "allow" /url "/content/dam/eon/*/data/*.json" }
      /0155 { /type "allow" /url "/content/revu-global/*/imagegallery.ajax.json" }
      /0156 { /type "allow" /url "/content/revu-global/*/*sitemap.xml" }
      /0157 { /type "allow" /method "GET" /url "*/bin/revu-global/*" }
      /0158 { /type "allow" /method "POST" /url "*/bin/revu-global/*" }
      /0159 { /type "allow" /method "GET" /url "/bin/edis/prognose*" }
      /0160 { /type "allow" /selectors "sendThermo" /method "POST" /extension "json" /url "/content/eon-energia*" }
      /0161 { /type "allow" /selectors "sendThermoDownload" /method "POST" /extension "json" /url "/content/eon-energia*" }
      /0162 { /type "allow" /selectors "sendManutenzione" /method "POST" /extension "json" /url "/content/eon-energia*" }
      /0163 { /type "allow" /selectors "sendManutenzioneDownload" /method "POST" /extension "json" /url "/content/eon-energia*" }
      /0164 { /type "allow" /selectors "insertProspect" /method "POST" /extension "json" /url "/content/eon-energia*" }
      /0165 { /type "allow" /selectors "insertProspectDownload" /method "POST" /extension "json" /url "/content/eon-energia*" }
      /0166 { /type "allow" /selectors "contactUs" /method "POST" /extension "json" /url "/content/eon-energia*" }
      /0167 { /type "allow" /selectors "getProduct" /method "GET" /extension "json" /url "/content/eon-energia*" }
      /0168 { /type "allow" /selectors "getRule" /method "GET" /extension "json" /url "/content/eon-energia*" }
      /0169 { /type "allow" /selectors "getPaymentMethod" /method "GET" /extension "json" /url "/content/eon-energia*" }
      /0170 { /type "allow" /selectors "getOffer" /method "GET" /extension "json" /url "/content/eon-energia*" }
      /0171 { /type "allow" /selectors "getCharacteristic" /method "GET" /extension "json" /url "/content/eon-energia*" }
      /0172 { /type "allow" /selectors "searchResidentialClient" /method "POST" /extension "json" /url "/content/eon-energia*" }
      /0173 { /type "allow" /selectors "sendOffer" /method "POST" /extension "json" /url "/content/eon-energia*" }
      /0174 { /type "allow" /selectors "goToStep2" /method "POST" /extension "json" /url "/content/eon-energia*" }
      /0175 { /type "allow" /selectors "goToStep3" /method "POST" /extension "json" /url "/content/eon-energia*" }
      /0176 { /type "allow" /selectors "goToStep4" /method "POST" /extension "json" /url "/content/eon-energia*" }
      /0177 { /type "allow" /selectors "goToStep5" /method "POST" /extension "json" /url "/content/eon-energia*" }
      /0178 { /type "allow" /selectors "saveLead" /method "POST" /extension "json" /url "/content/eon-energia*" }
      /0179 { /type "allow" /selectors "retrieveProspect" /method "GET" /extension "json" /url "/content/eon-energia*" }
      /0180 { /type "allow" /selectors "retrieveClientCode" /method "GET" /extension "json" /url "/content/eon-energia*" }
      /0181 { /type "allow" /selectors "connection" /method "GET" /extension "json" /url "/content/eon-energia*" }
      /0182 { /type "allow" /selectors "user" /method "GET" /extension "json" /url "/content/eon-energia*" }
      /0183 { /type "allow" /selectors "sendScore" /method "POST" /extension "json" /url "/content/eon-energia*" }
      /0184 { /type "allow" /selectors "sendReport" /method "POST" /extension "json" /url "/content/eon-energia*" }
      /0185 { /type "allow" /url "*eonde-*.json*" }
      /0186 { /type "allow" /url "/content/dam/eon-dk/tankkort-data/data.json" }
      /0187 { /type "allow" /url "/etc/projects/eon-dk/apps/tankkort/*" }
      /0188 { /type "allow" /url "/bin/eon-foundation/*" }
      /0189 { /type "allow" /url "/content/eon-arvisualizer/*.app.json" }
      /0190 { /type "allow" /url "*BingSiteAuth.xml*" }
      /0191 { /type "allow" /selectors "retrieveDataAccount" /method "GET" /extension "json" /url "/content/eon-scsi*" }
      /0192 { /type "deny" /url "/content/geometrixx/*" }
    }
    # The cache section regulates what responses will be cached and where.
    /cache
      {
      # The docroot must be equal to the document root of the webserver. The
      # dispatcher will store files relative to this directory and subsequent
      # requests may be "declined" by the dispatcher, allowing the webserver
      # to deliver them just like static files.
      /docroot "/var/www/html"

      # Sets the level upto which files named ".stat" will be created in the
      # document root of the webserver. When an activation request for some
      # page is received, only files within the same subtree are affected
      # by the invalidation.
      #/statfileslevel "0"

      # Flag indicating whether to cache responses to requests that contain
      # authorization information.
      #/allowAuthorized "0"

      # Flag indicating whether the dispatcher should serve stale content if
      # no remote server is available.
      #/serveStaleOnError "0"

      # The rules section defines what responses should be cached based on
      # the requested URL. Please note that only the following requests can
      # lead to cacheable responses:
      #
      # - HTTP method is GET
      # - URL has an extension
      # - Request has no query string
      # - Request has no "Authorization" header (unless allowAuthorized is 1)
      /rules
        {
        /0000
          {
          # the globbing pattern to be compared against the url
          # example: *             -> everything
          #        : /foo/bar.*    -> only the /foo/bar documents
          #        : /foo/bar/*    -> all pages below /foo/bar
          #        : /foo/bar[./]* -> all pages below and /foo/bar itself
          #        : *.html        -> all .html files
          /glob "*"
          /type "deny"
          }
        }

      # The invalidate section defines the pages that are "invalidated" after
      # any activation. Please note that the activated page itself and all
      # related documents are flushed on an modification. For example: if the
      # page /foo/bar is activated, all /foo/bar.* files are removed from the
      # cache.
      /invalidate
        {
        /0000
          {
          /glob "*"
          /type "allow"
          }
        }

      # The allowedClients section restricts the client IP addresses that are
      # allowed to issue activation requests.
      /allowedClients
        {
        # Uncomment the following to restrict activation requests to originate
        # from "localhost" only.
        #
        #/0000
        #  {
        #  /glob "*"
        #  /type "deny"
        #  }
        #/0001
        #  {
        #  /glob "127.0.0.1"
        #  /type "allow"
        #  }
        }

      # The ignoreUrlParams section contains query string parameter names that
      # should be ignored when determining whether some request's output can be
      # cached or delivered from cache.
      #
      # In this example configuration, the "q" parameter will be ignored.
      #/ignoreUrlParams
      #  {
      #  /0001 { /glob "*" /type "deny" }
      #  /0002 { /glob "q" /type "allow" }
      #  }

      # Cache response headers next to a cached file. On the first request to
      # an uncached resource, all headers matching one of the values found here
      # are stored in a separate file, next to the cache file. On subsequent
      # requests to the cached resource, the stored headers are added to the
      # response.
      #
      # Note, that file globbing characters are not allowed here.
      #
      #/headers
      #  {
      #  "Cache-Control"
      #  "Content-Disposition"
      #  "Content-Type"
      #  "Expires"
      #  "Last-Modified"
      #  "X-Content-Type-Options"
      #  }

      # A grace period defines the number of seconds a stale, auto-invalidated
      # resource may still be served from the cache after the last activation
      # occurring. Auto-invalidated resources are invalidated by any activation,
      # when their path matches the /invalidate section above. This setting
      # can be used in a setup, where a batch of activations would otherwise
      # repeatedly invalidate the entire cache.
      #
      #/gracePeriod "2"

      # Enable TTL evaluates the response headers from the backend, and if they
      # contain a Cache-Control max-age or Expires date, an auxiliary, empty file
      # next to the cache file is created, with modification time equal to the
      # expiry date. When the cache file is requested past the modification time
      # it is automatically re-requested from the backend.
      #
      # /enableTTL "1"

      }

    # The statistics sections dictates how the load should be balanced among the
    # renders according to the media-type.
    /statistics
      {
      /categories
        {
        /html
          {
          /glob "*.html"
          }
        /others
          {
          /glob "*"
          }
        }
      }

    # Authorization checker: before a page in the cache is delivered, a HEAD
    # request is sent to the URL specified in /url with the query string
    # '?uri='. If the response status is 200 (OK), the page is returned
    # from the cache. Otherwise, the request is forwarded to the render and
    # its response returned.
    #
    # Only pages matching the /filter section below are checked, all other pages
    # get delivered unchecked.
    #
    # All header lines returned from the auth_checker's HEAD request that match
    # the /headers section will be returned as well.
    #
    #/auth_checker
    #  {
    #  /url "/bin/permissioncheck.html"
    #  /filter
    #    {
    #    /0000
    #      {
    #      /glob "*"
    #      /type "deny"
    #      }
    #    /0001
    #      {
    #      /glob "*.html"
    #      /type "allow"
    #      }
    #    }
    #  /headers
    #    {
    #    /0000
    #      {
    #      /glob "*"
    #      /type "deny"
    #      }
    #    /0001
    #      {
    #      /glob "Set-Cookie:*"
    #      /type "allow"
    #      }
    #    }
    #  }

    }
  }