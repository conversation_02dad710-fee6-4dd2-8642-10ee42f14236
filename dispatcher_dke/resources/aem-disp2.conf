Listen *:8000

<VirtualHost *:80 *:8000>
    ServerName dispatcher
    ServerAlias *

    LogLevel info rewrite:trace1
    LogLevel info

    DocumentRoot /var/www/html
    DirectoryIndex index.html

    LoadModule dispatcher_module  modules/mod_dispatcher.so

    <IfModule disp_apache2.c>
        DispatcherConfig          conf/dispatcher.any
        DispatcherLog             /dev/stdout
        DispatcherLogLevel        3
        DispatcherDeclineRoot     0
        DispatcherUseProcessedURL 1
        DispatcherPassError       1
    </IfModule>

    RewriteEngine On
    RewriteMap tolower int:tolower

    SetEnvIf X-Forwarded-Proto "https" HTTPS=on

    RewriteCond %{REQUEST_URI} !^/content/dam/revu-global/bayernwerk/images/local-docker(.*)$ [NC]
    RewriteCond %{REQUEST_URI} !^/content/dam/revu-global/bayernwerk/images/produkt-pk/docker/(.*) [NC]
    RewriteRule ^/content/dam/revu-global/bayernwerk/(.*)$ https://qa.bayernwerk.de/content/dam/revu-global/bayernwerk/$1 [R=302,NE,L]


    RewriteRule ^/de.html$ /content/revu-global/bayernwerk/de.html [PT,NE,L]
    RewriteRule ^/$ /de.html [R=301,NE,L]

    RewriteRule ^/content$ /de.html [R=301,NE,L]
    RewriteRule ^/content/revu-global/bayernwerk/(.*).html$ /$1.html [R=301,NE,L]
    RewriteRule ^/content/revu-global/bayernwerk/(.*)$ /$1 [R=301,NE,L]

    RewriteCond %{REQUEST_URI} !^/apps/(.*) [NC]
    RewriteCond %{REQUEST_URI} !^/etc(.*) [NC]
    RewriteCond %{REQUEST_URI} !^/libs(.*) [NC]
    RewriteCond %{REQUEST_URI} !^/content(.*) [NC]
    RewriteCond %{REQUEST_URI} !^/system(.*) [NC]
    RewriteCond %{REQUEST_URI} !^/dam(.*) [NC]
    RewriteCond %{REQUEST_URI} !^/bin(.*) [NC]
    RewriteCond %{REQUEST_URI} !^/home(.*) [NC]


    RewriteRule ^/(.*) /content/revu-global/bayernwerk/$1 [PT,NE]
    RewriteRule ^/(.*)\?(.*) /content/revu-global/bayernwerk/$1 [PT,NE]


    <Directory />
        <IfModule disp_apache2.c>
            ModMimeUsePathInfo On
            SetHandler dispatcher-handler
        </IfModule>

        Options FollowSymLinks
        AllowOverride None
    </Directory>

</VirtualHost>
