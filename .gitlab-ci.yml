default:
  image: repo.eon-cds.de/aem-docker-local/jdk11-node-v14:latest
  interruptible: true
  tags:
    - esp-autoscale-runner-docker

stages:
  - buildDockerImage
  - BuildnTest
  - UploadCorePackage
  - Dispatcher Build
  - Deployment
  - secure

include:
  - template: Security/SAST.gitlab-ci.yml
  - template: Security/Dependency-Scanning.gitlab-ci.yml
  - template: Security/Secret-Detection.gitlab-ci.yml

variables:
    PROJECT_NAME:                 "dke-dist-aem-global"
    APPS_NAME:                    "${PROJECT_NAME}"
    PROJECT_SHORTNAME:            "dke-dist"
    DISPATCHER_BRAND:             "dke-fix"
    BASE_DOMAIN:                  "aem-aks-fun.eon.com"
    ARTIFACT_ID:                  "dke-dist-aem-global.core"
    GROUP_ID:                     "com.eon.dist.dke"
    REPOSITORY:                   "${ARTIFACTORY_URL}/artifactory/ext-release-local"
    DYN_ENV_NAMESPACE:            "funke-aem-devel"
    TILLER_NAMESPACE:             "funke-aem-devel"
    TILLER_SERVICE_ACCOUNT:       "funke-aem-devel-sa"
    ENV_SLUG:                     "${CI_ENVIRONMENT_SLUG}-${PROJECT_SHORTNAME}"
    AUTHOR_URL:                    https://${CI_ENVIRONMENT_SLUG}-${PROJECT_SHORTNAME}-author.${BASE_DOMAIN}
    PUBLISH_URL:                   https://${CI_ENVIRONMENT_SLUG}-${PROJECT_SHORTNAME}-publish.${BASE_DOMAIN}
    DISPATCHER_URL:                https://${CI_ENVIRONMENT_SLUG}-${PROJECT_SHORTNAME}-dispatcher.${BASE_DOMAIN}
    G_DISPATCHER_VERSION:         "4.2.3"
    GOOGLE_IMAGE_BASE:            "eonglobalacr.azurecr.io"
    GOOGLE_CLOUD_REGISTRY:        "eonglobalacr.azurecr.io"
    GOOGLE_DOCKER_IMAGE_NAME:     "${GOOGLE_IMAGE_BASE}/dispatcher:${G_DISPATCHER_VERSION}-${DISPATCHER_BRAND}"
    DISPATCHER_DOCKER_CONTEXT:    "dispatcher_${DISPATCHER_BRAND}"
    BUILD_DISPATCHER:             "false"
    SONAR_PROJECT_NAME:           "${ARTIFACT_ID} ${CI_COMMIT_REF_NAME}"
    MAVEN_COMMON_FULL_OPTS:       " -Dmaven.repo.local=/root/.m2/repository -Dbnd.baseline.skip=true -U -B -s k8s/settings.xml"
    MAVEN_COMMON_OPTS:            " -Dmaven.repo.local=/root/.m2/repository -Dbnd.baseline.skip=true -B -s k8s/settings.xml"
    QA_MAVEN_SONAR_CLI_OPTS:      " -Dsonar.host.url=https://qa-sonar.eonaura.de -Dsonar.login=${QA_SONAR_AUTH_TOKEN} -DsonarRunner.aemVersion=6.4 -Dsonar.projectBaseDir=${CI_PROJECT_DIR} -Dsonar.coverage.jacoco.xmlReportPaths=${CI_PROJECT_DIR}/core/target/site/jacoco/jacoco.xml"
    MAVEN_SONAR_CLI_OPTS:         " -Dsonar.host.url=https://sonar.eon-cds.de/ -Dsonar.login=${SONAR_AUTH_TOKEN}    -DsonarRunner.aemVersion=6.4 -Dsonar.projectBaseDir=${CI_PROJECT_DIR} -Dsonar.coverage.jacoco.xmlReportPaths=${CI_PROJECT_DIR}/core/target/site/jacoco/jacoco.xml"
    
    DOCKER_SONAR_CLI_OPTS:        "-Dsonar.host.url=https://qa-sonar.eonaura.de -Dsonar.login=${QA_SONAR_AUTH_TOKEN} -Dsonar.projectName=${PROJECT_NAME}-docker -Dsonar.projectBaseDir=/opt -Dsonar.coverage.jacoco.xmlReportPaths=/opt/core/target/site/jacoco/jacoco.xml"
    ARTIFACTS_EXPORTS_PATH:       "$CI_PROJECT_DIR/artifacts_export"
    DOCKER_IMAGE_REG_URL:         "$DOCKER_REGISTRY/$CI_PROJECT_ID/$CI_PROJECT_NAME-jdk11:latest"

buildDockerImage :
  image: docker:latest
  stage: buildDockerImage
  tags:
   - docker
  script:
    - docker login $DOCKER_REGISTRY -u $DOCKER_REGISTRY_USER -p $DOCKER_REGISTRY_PASSWORD
    - echo DOCKER_IMAGE_REG_URL $DOCKER_IMAGE_REG_URL
    - docker version
    - docker build --no-cache --build-arg ARTIFACTORY_NPM_USER="$ARTIFACTORY_NPM_USER" --build-arg ARTIFACTORY_NPM_TOKEN="$ARTIFACTORY_NPM_TOKEN" --build-arg versionPatternlab="$versionPatternlab" --build-arg versionDkereact="$versionDkereact" --build-arg MAVEN_COMMON_FULL_OPTS="$MAVEN_COMMON_FULL_OPTS" --build-arg MAVEN_COMMON_OPTS="$MAVEN_COMMON_OPTS" --build-arg DOCKER_SONAR_CLI_OPTS="$DOCKER_SONAR_CLI_OPTS" --build-arg ARTIFACTORY_USER="$ARTIFACTORY_USER" --build-arg ARTIFACTORY_URL="$ARTIFACTORY_URL" --build-arg ARTIFACTORY_PASSWORD="$ARTIFACTORY_PASSWORD" -t "$DOCKER_IMAGE_REG_URL" .
    - docker push "$DOCKER_IMAGE_REG_URL"
    - echo "$DOCKER_IMAGE_REG_URL"
  only:
    - schedules


.only_branches: &only_branches
  services:
    - name: docker:18.09-dind
      alias: docker
  variables:
        DOCKER_HOST: "tcp://127.0.0.1:2375"
  only:
    - /^feature-dyn\/.*$/

Build:
  stage: BuildnTest
  script:
    - date
    - mkdir ${ARTIFACTS_EXPORTS_PATH}
    - rm .npmrc
    - npm config set progress=false 
    - npm config set registry ${ARTIFACTORY_URL}/artifactory/api/npm/funke-npm-virtual 
    - curl -sS -u ${ARTIFACTORY_NPM_USER}:${ARTIFACTORY_NPM_TOKEN} ${ARTIFACTORY_URL}/artifactory/api/npm/auth > .npmrc
    #- versionDkereact=$(npm view react-aem-project-bundler version)
    #- versionPatternlab=$(npm view aem-dso-patternlab version)
    - echo "versionPatternlab ${versionPatternlab}"
    - echo "versionDkereact ${versionDkereact}"
    - mvn ${MAVEN_COMMON_OPTS} build-helper:parse-version versions:set -DnewVersion="\${parsedVersion.majorVersion}.\${parsedVersion.minorVersion}.\${parsedVersion.incrementalVersion}-${CI_PIPELINE_ID}"
    - mvn ${MAVEN_COMMON_OPTS} -DskipTests -Dmaven.test.skip=true install -Dversion.patternlab=https://repo.eon-cds.de/artifactory/funke-npm-virtual/aem-dso-patternlab/-/aem-dso-patternlab-${versionPatternlab}.tgz -Dversion.dkereact=https://repo.eon-cds.de/artifactory/funke-npm-virtual/react-aem-project-bundler/-/react-aem-project-bundler-${versionDkereact}.tgz
    - cp all/target/*.zip ${ARTIFACTS_EXPORTS_PATH}/.
    #- cp core/target/dke-dist-aem-global.core* ${ARTIFACTS_EXPORTS_PATH}/.
    - cp k8s/.npmrc.tmpl .npmrc
    - cp -rf /root/.m2 ${CI_PROJECT_DIR}
    - date
  artifacts:
    name: "$CI_BUILD_ID"
    expire_in: 120 mins
    when: on_success
    paths:
      - ${ARTIFACTS_EXPORTS_PATH}
      - ${CI_PROJECT_DIR}/.m2/repository

  after_script:
    - date

# SonarProd-Test:
#   stage: BuildnTest
#   script:
#     - date
#     - mvn ${MAVEN_COMMON_OPTS} test jacoco:report
#     - mvn ${MAVEN_COMMON_OPTS} sonar:sonar $MAVEN_SONAR_CLI_OPTS
#     - cp k8s/.npmrc.tmpl .npmrc
#     - date

# SonarQA-Test:
#   stage: BuildnTest
#   when: manual
#   script:
#     - date
#     - mvn ${MAVEN_COMMON_OPTS} test jacoco:report
#     - mvn ${MAVEN_COMMON_OPTS} sonar:sonar $QA_MAVEN_SONAR_CLI_OPTS
#     - cp k8s/.npmrc.tmpl .npmrc
#     - date


Upload to repo:
  stage:  UploadCorePackage
  script:
    - WORK_DIR=`pwd`
    - JAR=$(find ${ARTIFACTS_EXPORTS_PATH} -type f -name ${ARTIFACT_ID}*.jar)
    - APP_VERSION=$(echo ${JAR##*core-}|sed -e 's/\.jar//g')
    - echo "JAR $JAR ARTIFACT_ID  ${ARTIFACT_ID} APP_VERSION ${APP_VERSION} " 
    - echo mvn ${MAVEN_COMMON_OPTS} -DskipTests=true deploy:deploy-file -Durl="${REPOSITORY}" -DrepositoryId="central" -DgroupId="${GROUP_ID}" -DartifactId="${ARTIFACT_ID}" -Dversion="${APP_VERSION}" -Dfile="${JAR}"
    - mvn ${MAVEN_COMMON_OPTS} -DskipTests=true deploy:deploy-file -Durl="${REPOSITORY}" -DrepositoryId="central" -DgroupId="${GROUP_ID}" -DartifactId="${ARTIFACT_ID}" -Dversion="${APP_VERSION}" -Dfile="${JAR}"

  only:
    variables:
      - $UPLOAD_CORE == "true" 


build dispatcher :
  stage: Dispatcher Build
  <<: *only_branches
  only:
    variables:
      - $BUILD_DISPATCHER == "true"

  before_script:
    - docker login -u _json_key -p "$(echo ${GOOGLE_SERVICE_KEY})" https://${GOOGLE_CLOUD_REGISTRY}
    - docker info
    - date

  script:
    - echo ${GOOGLE_DOCKER_IMAGE_NAME}
    - |-
        docker build \
          --tag ${GOOGLE_DOCKER_IMAGE_NAME} \
          --pull \
          --build-arg CODE_BASE=${GOOGLE_IMAGE_BASE}/dispatcher:${G_DISPATCHER_VERSION} \
          ${DISPATCHER_DOCKER_CONTEXT}
    - docker images
    - date
    - docker push ${GOOGLE_DOCKER_IMAGE_NAME}
  after_script:
    - date
    - docker history ${GOOGLE_DOCKER_IMAGE_NAME}


Provisioning Dynamic Environment:
  image: eonglobalacr.azurecr.io/aux/kubectl:latest
  stage: Deployment
  <<: *only_branches
  tags:
   - aem-funke-k8s-runner-az


  environment:
    name: dyn/${CI_COMMIT_REF_NAME}
    url: https://${CI_ENVIRONMENT_SLUG}-${PROJECT_SHORTNAME}-author.${BASE_DOMAIN}/libs/cq/core/content/welcome.html
    on_stop: Removal Dynamic Environment


  before_script:
    - echo ${KUBE_CONFIG_AKS_NEW} | base64 -d  > ./kube_config
    - export KUBECONFIG=${PWD}/kube_config

##  - curl -sS -f -L https://storage.googleapis.com/kubernetes-helm/helm-${VERSION_HELM}-linux-amd64.tar.gz -o /tmp/helm.tar.gz
##  - tar -zxvf /tmp/helm.tar.gz -C /tmp && mv /tmp/linux-amd64/helm /bin/helm && chmod 0755 /bin/helm && rm -rfv /var/cache/apk/* && rm -rfv /tmp/*
  script:
    - echo "AUTHOR_HOST_NAME=${AUTHOR_URL}" | tee ENV
    - echo "PUBLISH_HOST_NAME=${PUBLISH_URL}" | tee -a ENV
    - echo "DISPATCHER_HOST_NAME=${DISPATCHER_URL}" | tee -a ENV
    - kubectl version
    - helm package k8s/aem64Installer
    - kubectl config current-context
    - echo " helm upgrade --install --wait --timeout 600s --namespace ${DYN_ENV_NAMESPACE} --set slug=${ENV_SLUG} --set baseDomain=${BASE_DOMAIN} --set dispatcherBrand=${DISPATCHER_BRAND} --set name=${ENV_SLUG} ${ENV_SLUG} aem64Installer-1.0.0.tgz"
    - helm upgrade --install --wait --timeout 600s --namespace ${DYN_ENV_NAMESPACE} --set slug=${ENV_SLUG} --set baseDomain=${BASE_DOMAIN} --set dispatcherBrand=${DISPATCHER_BRAND} --set name=${ENV_SLUG} ${ENV_SLUG} aem64Installer-1.0.0.tgz
    - kubectl rollout status deployment/${ENV_SLUG} --namespace ${DYN_ENV_NAMESPACE}
    #Install permission package for anonymous user
    - bash k8s/install_package.sh -F k8s/packages/anonymous-acls-1.0.0.zip -S 5
    #Install sling-mapping
    - bash k8s/install_package.sh -I publish -F k8s/packages/k8s-sling-mapping-${PROJECT_SHORTNAME}-1.0.0.zip -S 5
    - |
        for proto in http https ; do
          for host in ${PUBLISH_HOST_NAME} ${DISPATCHER_HOST_NAME} ; do
            curl -sS -k -L -u ${DYN_ADMIN_USER}:${DYN_ADMIN_PASSWORD} -X COPY -H "Destination: /crx/server/crx.default/jcr:root/etc/map/${proto}/${host}" -H "Overwrite: T" https://${PUBLISH_HOST_NAME}/crx/server/crx.default/jcr:root/etc/map/${proto}/XXX__GENERIC_MAP__XXX
          done
        done

    - bash k8s/install_package.sh


  artifacts:
    expire_in: 120 mins
    paths:
      - ENV
  dependencies:
    - Build

Removal Dynamic Environment:
  image: eonglobalacr.azurecr.io/aux/kubectl:latest
  stage: Deployment
  <<: *only_branches
  tags:
   - aem-funke-k8s-runner-az

  variables:
      GIT_STRATEGY: none

  before_script:
    - echo ${KUBE_CONFIG_AKS_NEW} | base64 -d  > ./kube_config
    - export KUBECONFIG=${PWD}/kube_config

  script:
    - helm delete ${ENV_SLUG}

  # Delete PVCs matching the release label or name pattern
    - echo "Deleting PVCs with release=${ENV_SLUG}"
    - |
      kubectl get pvc -l release=${ENV_SLUG} -o name | while read pvc; do
        echo "Deleting $pvc"
        kubectl delete $pvc || echo "$pvc already deleted or not found"
      done

  environment:
    name: dyn/${CI_COMMIT_REF_NAME}
    action: stop

  dependencies: []
  when: manual

#Security checks
sast:
  stage: secure
  rules:
    - when: never

spotbugs-sast:
  dependencies:
    - Build
  allow_failure: true
  variables:
    MAVEN_REPO_PATH: ${CI_PROJECT_DIR}/.m2/repository
    COMPILE: "false"
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
      when: always
    - when: never

nodejs-scan-sast:
  stage: secure
  allow_failure: true
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
      when: always
    - when: never

semgrep-sast:
  stage: secure
  allow_failure: true
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
      when: always
    - when: never

gemnasium-dependency_scanning:
  stage: secure
  allow_failure: true
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
      when: always
    - when: never

dependency_scanning:
  stage: secure
  rules:
    - when: never

gemnasium-maven-dependency_scanning:
  stage: secure
  rules:
    - when: never

gemnasium-python-dependency_scanning:
  stage: secure
  rules:
    - when: never

secret_detection:
  stage: secure
  allow_failure: true
  variables:
    GIT_DEPTH: 100
    SECRET_DETECTION_HISTORIC_SCAN: "true"
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
      when: always
    - when: never
