<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!--
 |  Copyright 2015 Adobe Systems Incorporated
 |
 |  Licensed under the Apache License, Version 2.0 (the "License");
 |  you may not use this file except in compliance with the License.
 |  You may obtain a copy of the License at
 |
 |      http://www.apache.org/licenses/LICENSE-2.0
 |
 |  Unless required by applicable law or agreed to in writing, software
 |  distributed under the License is distributed on an "AS IS" BASIS,
 |  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 |  See the License for the specific language governing permissions and
 |  limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.eon.aemcloud</groupId>
		<artifactId>eon-aem-parent</artifactId>
		<version>2.5</version>
	</parent>

	<groupId>com.eon.dist.dke</groupId>
	<artifactId>dke-dist-aem-global</artifactId>
	<packaging>pom</packaging>
	<version>${version.artifact}</version>
	<name>E.ON Revu Global - Reactor</name>
	<description>E.ON Revu Global - Reactor</description>

	<modules>
		<module>all</module>
		<module>core</module>
		<module>ui.apps</module>
		<module>ui.apps.structure</module>
		<module>ui.config</module>
		<module>ui.content</module>
		<module>it.tests</module>
		<module>dispatcher</module>
	</modules>

	<properties>
		<version.artifact>10.7.21</version.artifact>
		<aem.host>localhost</aem.host>
		<aem.port>4502</aem.port>
		<aem.publish.host>localhost</aem.publish.host>
		<aem.publish.port>4503</aem.publish.port>
		<sling.user>admin</sling.user>
		<sling.password>admin</sling.password>
		<vault.user>admin</vault.user>
		<vault.password>admin</vault.password>
		<frontend-maven-plugin.version>1.12.0</frontend-maven-plugin.version>
		<core.wcm.components.version>2.19.0</core.wcm.components.version>

		<bnd.version>5.1.2</bnd.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<aem.sdk.api>2023.4.11873.20230421T153841Z-230200</aem.sdk.api>
		<aemanalyser.version>1.4.10</aemanalyser.version>
		<componentGroupName>E.ON Revu Global - Reactor</componentGroupName>
		<sonar.exclusions>
			**/src/main/java/com/eon/dist/dke/aem/content/fragments/*.java,
			**/src/main/java/com/eon/dist/dke/aem/core/integration/*.java,
			**/src/main/java/com/eon/dist/dke/aem/core/bean/*.java,
			**/src/main/java/com/eon/dist/dke/aem/core/constants/*.java,
			**/src/main/java/com/eon/dist/dke/aem/core/exceptions/*.java,
			**/src/main/java/com/eon/dist/dke/aem/core/models/table/*.java,
			**/src/main/java/com/eon/dist/dke/aem/core/bean/services/googlemaps/model/geocoding/*.java
			**/src/main/java/com/eon/dist/dke/aem/core/models/CFInvalidLinkModel.java,
			**/src/main/java/com/eon/dist/dke/aem/core/services/impl/ContactSearchServiceImpl.java,
			**/src/main/java/com/eon/dist/dke/aem/core/workflow/DamNormalizerProcess.java,
			**/src/main/java/com/eon/dist/dke/aem/core/servlets/OptionsProvider.java,
			**/src/main/java/com/eon/dist/dke/aem/core/servlets/SolrOptionsProvider.java,
			**/src/main/java/com/eon/dist/dke/aem/core/services/SystemUser.java,
			**/src/main/java/com/eon/dist/dke/aem/core/bean/services/googlemaps/model/geocoding/Location.java,
			**/src/main/java/com/eon/dist/dke/aem/core/bean/services/googlemaps/model/geocoding/Geometry.java,
			**/src/main/java/com/eon/dist/dke/aem/core/bean/services/googlemaps/model/geocoding/GeocodeResponse.java,
			**/src/main/java/com/eon/dist/dke/aem/core/bean/services/googlemaps/model/geocoding/Geocode.java,
			**/src/main/java/com/eon/dist/dke/aem/core/util/AxisBundleUtil.java,
			**/src/main/java/com/eon/dist/dke/aem/core/servlets/ImageGalleryServlet.java,
			**/src/main/java/com/eon/dist/dke/aem/core/servlets/AdaptiveImageServlet.java,
			**/src/main/java/com/eon/dist/dke/aem/core/services/impl/FunkeMailServiceImpl.java,
			**/src/main/java/com/eon/dist/dke/aem/core/servlets/RecaptchaFormServlet.java,
			**/src/main/java/com/eon/dist/dke/aem/core/services/impl/EmailTemplateSenderImpl.java,
			**/src/main/java/com/eon/dist/dke/aem/core/servlets/JobDataToJsonServlet.java,
			**/src/main/java/com/eon/dist/dke/aem/core/servlets/FormServlet.java
		</sonar.exclusions>
		<sonar.jacoco.excludes>
			**/src/main/java/com/eon/dist/dke/aem/core/integration/*.java
			**/src/main/java/com/eon/dist/dke/aem/core/bean/*.java,
			**/src/main/java/com/eon/dist/dke/aem/core/constants/*.java,
			**/src/main/java/com/eon/dist/dke/aem/core/exceptions/*.java,
			**/src/main/java/com/eon/dist/dke/aem/core/bean/services/googlemaps/model/geocoding/*.java,
			**/src/main/java/com/eon/dist/dke/aem/core/util/AxisBundleUtil.java,
			**/src/main/java/com/eon/dist/dke/aem/core/models/CFInvalidLinkModel.java,
			**/src/main/java/com/eon/dist/dke/aem/core/services/impl/ContactSearchServiceImpl.java,
			**/src/main/java/com/eon/dist/dke/aem/core/workflow/DamNormalizerProcess.java,
			**/src/main/java/com/eon/dist/dke/aem/core/servlets/OptionsProvider.java,
			**/src/main/java/com/eon/dist/dke/aem/core/servlets/SolrOptionsProvider.java,
			**/src/main/java/com/eon/dist/dke/aem/core/services/SystemUser.java,
			**/src/main/java/com/eon/dist/dke/aem/core/bean/services/googlemaps/model/geocoding/Location.java,
			**/src/main/java/com/eon/dist/dke/aem/core/bean/services/googlemaps/model/geocoding/Geometry.java,
			**/src/main/java/com/eon/dist/dke/aem/core/bean/services/googlemaps/model/geocoding/GeocodeResponse.java,
			**/src/main/java/com/eon/dist/dke/aem/core/bean/services/googlemaps/model/geocoding/Geocode.java,
			**/src/main/java/com/eon/dist/dke/aem/core/util/AxisBundleUtil.java,
			**/src/main/java/com/eon/dist/dke/aem/core/util/ResolverUtil.java,
			**/src/main/java/com/eon/dist/dke/aem/core/servlets/ImageGalleryServlet.java,
			**/src/main/java/com/eon/dist/dke/aem/core/util/JcrUtil.java,
			**/src/main/java/com/eon/dist/dke/aem/core/servlets/AdaptiveImageServlet.java,
			**/src/main/java/com/eon/dist/dke/aem/core/services/impl/FunkeMailServiceImpl.java,
			**/src/main/java/com/eon/dist/dke/aem/core/servlets/RecaptchaFormServlet.java,
			**/src/main/java/com/eon/dist/dke/aem/core/services/impl/EmailTemplateSenderImpl.java,
			**/src/main/java/com/eon/dist/dke/aem/core/servlets/JobDataToJsonServlet.java,
			**/src/main/java/com/eon/dist/dke/aem/core/servlets/FormServlet.java
		</sonar.jacoco.excludes>
	</properties>

	<build>
		<plugins>
			<plugin>
				<groupId>com.github.eirslett</groupId>
				<artifactId>frontend-maven-plugin</artifactId>
				<inherited>false</inherited>
				<executions>
					<execution>
						<id>install node and npm</id>
						<goals>
							<goal>install-node-and-npm</goal>
						</goals>
						<phase>validate</phase>
						<configuration>
							<nodeVersion>v8.12.0</nodeVersion>
							<npmVersion>6.4.1</npmVersion>
						</configuration>
					</execution>
					<execution>
						<id>npm install</id>
						<goals>
							<goal>npm</goal>
						</goals>
						<phase>validate</phase>
						<!-- Optional configuration which provides for running any npm command -->
						<configuration>
							<arguments>${npm.install.arguments}</arguments>
						</configuration>
					</execution>
					<execution>
						<id>npm build</id>
						<goals>
							<goal>npm</goal>
						</goals>
						<phase>compile</phase>
						<!-- Optional configuration which provides for running any npm command -->
						<configuration>
							<arguments>${npm.build.arguments}</arguments>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<!-- This Plugin will pull the Component from the Artifactory -->
				<!-- Artifactory Configuration happens in the npmrc File -->
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>exec-maven-plugin</artifactId>
				<version>1.6.0</version>
				<inherited>false</inherited>
				<executions>
					<execution>
						<id>exec-npm-pull-patternlab</id>
						<phase>generate-sources</phase>
						<configuration>
							<executable>npm</executable>
							<arguments>
								<argument>install</argument>
								<!-- Define which Artifact should be downloaded , to include version 
									add @version -->
								<argument>${version.patternlab}</argument>
								<!-- <argument>https://artifacts.dev.eon.com/artifactory/funke-npm-virtual/aem-dso-patternlab/-/aem-dso-patternlab-1.0.5.tgz</argument> -->
								<!-- Perhaps we move all Dependencies into optionalDependencys, so 
									they will not be downloaded -->
								<argument>--no-optional</argument>
							</arguments>
						</configuration>
						<goals>
							<goal>exec</goal>
						</goals>
					</execution>
					<execution>
						<id>exec-npm-pull-dke-cscn-web</id>
						<phase>generate-sources</phase>
						<configuration>
							<executable>npm</executable>
							<arguments>
								<argument>install</argument>
								<!-- Define which Artifact should be downloaded , to include version 
									add @version -->
								<argument>${version.dkereact}</argument>
								<!-- Perhaps we move all Dependencies into optionalDependencys, so 
									they will not be downloaded -->
								<argument>--no-optional</argument>
							</arguments>
						</configuration>
						<goals>
							<goal>exec</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<!-- This Plugin can move the downloaded Component to a new Destination -->
			<plugin>
				<artifactId>maven-resources-plugin</artifactId>
				<!-- Overrided the version number to copy resource to the structure. -->
				<version>3.0.1</version>
				<inherited>false</inherited>
				<executions>
					<execution>
						<id>copy-patternlab-js-resources</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<!-- Copy JS file from Pattern lab -->
							<outputDirectory>./ui.apps/src/main/content/jcr_root/apps/revu-global/clientlibs/clientlib/js</outputDirectory>
							<resources>
								<resource>
									<!-- Source Location -->
									<directory>./node_modules/aem-dso-patternlab/build</directory>
									<filtering>true</filtering>
									<includes>
										<include>component.min.js</include>
									</includes>
								</resource>
							</resources>
						</configuration>
					</execution>
					<execution>
						<id>copy-patternlab-css-resources</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<!-- Copy CSS file from Pattern lab -->
							<outputDirectory>./ui.apps/src/main/content/jcr_root/apps/revu-global/clientlibs/clientlib/css</outputDirectory>
							<resources>
								<resource>
									<!-- Source Location -->
									<directory>./node_modules/aem-dso-patternlab/build</directory>
									<filtering>true</filtering>
									<includes>
										<include>styles.min.css</include>
									</includes>
								</resource>
							</resources>
						</configuration>
					</execution>
					<execution>
						<id>copy-patternlab-vendor-resources</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<!-- Copy vendor JS file from Pattern lab -->
							<outputDirectory>./ui.apps/src/main/content/jcr_root/apps/revu-global/clientlibs/clientlib-patternlab-vendors/js</outputDirectory>
							<resources>
								<resource>
									<!-- Source Location -->
									<directory>./node_modules/aem-dso-patternlab/build</directory>
									<filtering>true</filtering>
									<includes>
										<include>vendor.min.js</include>
									</includes>
								</resource>
							</resources>
						</configuration>
					</execution>
					<execution>
						<id>copy-author-resources</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<!-- Output Directory -->
							<outputDirectory>./ui.apps/src/main/content/jcr_root/apps/revu-global/clientlibs/clientlib-webpack-author</outputDirectory>
							<resources>
								<resource>
									<!-- Component Location -->
									<directory>./node_modules/react-aem-project-bundler/build/author</directory>
									<filtering>true</filtering>
								</resource>
							</resources>
						</configuration>
					</execution>
					<execution>
						<id>copy-publish-resources</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<!-- Output Directory -->
							<outputDirectory>./ui.apps/src/main/content/jcr_root/apps/revu-global/clientlibs/clientlib-webpack-publish</outputDirectory>
							<resources>
								<resource>
									<!-- Component Location -->
									<directory>./node_modules/react-aem-project-bundler/build/publish</directory>
									<filtering>true</filtering>
								</resource>
							</resources>
						</configuration>
					</execution>
					<execution>
						<id>copy-style-resources</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<!-- Output Directory -->
							<outputDirectory>./ui.apps/src/main/content/jcr_root/apps/revu-global/clientlibs/clientlib-react-styles/css</outputDirectory>
							<resources>
								<resource>
									<!-- Component Location -->
									<directory>./node_modules/react-shared-ui-elements/dist/styles</directory>
									<filtering>true</filtering>
									<includes>
										<include>styles.css</include>
									</includes>
								</resource>
							</resources>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!-- Maven Release Plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-release-plugin</artifactId>
				<version>2.5.3</version>
				<configuration>
					<scmCommentPrefix>[maven-scm] :</scmCommentPrefix>
					<preparationGoals>clean install</preparationGoals>
					<goals>install</goals>
					<releaseProfiles>release</releaseProfiles>
				</configuration>
			</plugin>
			<!-- Maven Source Plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.0.1</version>
				<inherited>true</inherited>
			</plugin>
			<!-- Maven Enforcer Plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<executions>
					<execution>
						<id>enforce-maven</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<rules>
								<requireMavenVersion>
									<version>[3.3.9,)</version>
								</requireMavenVersion>
								<requireJavaVersion>
									<message>Maven must be executed with a Java 8 JRE or higher.</message>
									<version>1.8.0</version>
								</requireJavaVersion>
							</rules>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!-- Maven Compiler Plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
		</plugins>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-assembly-plugin</artifactId>
					<version>3.3.0</version>
					<configuration>
						<tarLongFileMode>posix</tarLongFileMode>
					</configuration>
				</plugin>
				<!-- Maven Jar Plugin -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-jar-plugin</artifactId>
					<version>3.1.2</version>
				</plugin>
				<!-- Maven Clean Plugin -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-clean-plugin</artifactId>
					<version>3.0.0</version>
				</plugin>
				<!-- Apache Felix SCR Plugin -->
				<plugin>
					<groupId>org.apache.felix</groupId>
					<artifactId>maven-scr-plugin</artifactId>
					<version>1.20.0</version>
					<executions>
						<execution>
							<id>generate-scr-descriptor</id>
							<goals>
								<goal>scr</goal>
							</goals>
						</execution>
					</executions>
				</plugin>

				<!-- BND Maven Plugin  -->
				<plugin>
					<groupId>biz.aQute.bnd</groupId>
					<artifactId>bnd-maven-plugin</artifactId>
					<version>${bnd.version}</version>
					<executions>
						<execution>
							<id>bnd-process</id>
							<goals>
								<goal>bnd-process</goal>
							</goals>
							<configuration>
								<bnd>
									<![CDATA[
Bundle-Category: ${componentGroupName}

# export all versioned packages except for conditional ones (https://github.com/bndtools/bnd/issues/3721#issuecomment-579026778)
-exportcontents: ${removeall;${packages;VERSIONED};${packages;CONDITIONAL}}

# reproducible builds (https://github.com/bndtools/bnd/issues/3521)
-noextraheaders: true
-snapshot: SNAPSHOT

Bundle-DocURL:
-plugin org.apache.sling.caconfig.bndplugin.ConfigurationClassScannerPlugin
-plugin org.apache.sling.bnd.models.ModelsScannerPlugin
                                ]]>
								</bnd>
							</configuration>
						</execution>
					</executions>
					<dependencies>
						<dependency>
							<groupId>org.apache.sling</groupId>
							<artifactId>org.apache.sling.caconfig.bnd-plugin</artifactId>
							<version>1.0.2</version>
						</dependency>
						<dependency>
							<groupId>org.apache.sling</groupId>
							<artifactId>org.apache.sling.bnd.models</artifactId>
							<version>1.0.0</version>
						</dependency>
						<dependency>
							<groupId>org.apache.sling</groupId>
							<artifactId>scriptingbundle-maven-plugin</artifactId>
							<version>0.5.0</version>
						</dependency>
					</dependencies>
				</plugin>
				<plugin>
					<groupId>biz.aQute.bnd</groupId>
					<artifactId>bnd-baseline-maven-plugin</artifactId>
					<version>${bnd.version}</version>
				</plugin>
				<!-- Maven Resources Plugin -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-resources-plugin</artifactId>
					<version>3.0.2</version>
				</plugin>
				<!-- Maven Compiler Plugin -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>3.8.1</version>
				</plugin>
				<!-- Maven Installer Plugin -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-install-plugin</artifactId>
					<version>2.5.2</version>
				</plugin>
				<!-- Maven Surefire Plugin -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-surefire-plugin</artifactId>
					<version>2.22.1</version>
					<configuration>
						<useSystemClassLoader>false</useSystemClassLoader>
						<inputEncoding>UTF-8</inputEncoding>
						<outputEncoding>UTF-8</outputEncoding>
						<argLine>-Dfile.encoding=UTF-8 ${surefireArgLine}</argLine>
						<skipTests>false</skipTests>

						<argLine>${jacocoArgLine} -Xmx2048m</argLine>
						<includes>
							<include>**/**UnitTest*.java</include>
						</includes>
					</configuration>
				</plugin>
				<plugin>
					<groupId>org.jacoco</groupId>
					<artifactId>jacoco-maven-plugin</artifactId>
					<version>${eon-parent-pom.jacocoVersion}</version>
					<executions>
						<!--
                            Prepares the property pointing to the JaCoCo runtime agent which
                            is passed as VM argument when Maven the Surefire plugin is executed.
                        -->
						<execution>
							<id>pre-unit-test</id>
							<goals>
								<goal>prepare-agent</goal>
							</goals>
							<configuration>
								<!-- Sets the path to the file which contains the execution data. -->
								<destFile>${jacoco.ut.execution.data.file}</destFile>
								<!--
                                    Sets the name of the property containing the settings
                                    for JaCoCo runtime agent.
                                -->
								<propertyName>surefireArgLine</propertyName>
							</configuration>
						</execution>
						<!--
                            Ensures that the code coverage report for unit tests is created after
                            unit tests have been run.
                        -->
						<execution>
							<id>post-unit-test</id>
							<phase>test</phase>
							<goals>
								<goal>report</goal>
							</goals>
							<configuration>
								<!-- Sets the path to the file which contains the execution data. -->
								<dataFile>${jacoco.ut.execution.data.file}</dataFile>
								<!-- Sets the output directory for the code coverage report. -->
								<outputDirectory>${jacoco.ut.outputDirectory}</outputDirectory>
							</configuration>
						</execution>
						<!--
                            Prepares the property pointing to the JaCoCo runtime agent which
                            is passed as VM argument when Maven the Failsafe plugin is executed.
                        -->

						<execution>
							<id>pre-integration-test</id>
							<phase>pre-integration-test</phase>
							<goals>
								<goal>prepare-agent</goal>
							</goals>
							<configuration>

								<!-- Sets the path to the file which contains the execution data. -->
								<destFile>${jacoco.it.execution.data.file}</destFile>
								<!--
                                    Sets the name of the property containing the settings
                                    for JaCoCo runtime agent.
                                -->
								<propertyName>failsafeArgLine</propertyName>
							</configuration>
						</execution>
						<!--
                            Ensures that the code coverage report for integration tests after
                            integration tests have been run.
                        -->
						<execution>
							<id>post-integration-test</id>
							<phase>post-integration-test</phase>
							<goals>
								<goal>report</goal>
							</goals>
							<configuration>
								<!-- Sets the path to the file which contains the execution data. -->
								<dataFile>${jacoco.it.execution.data.file}</dataFile>
								<!-- Sets the output directory for the code coverage report. -->
								<outputDirectory>${project.reporting.outputDirectory}/jacoco-it</outputDirectory>
							</configuration>
						</execution>
						<execution>
							<id>prepare-agent</id>
							<goals>
								<goal>prepare-agent</goal>
							</goals>
							<configuration>
								<propertyName>jacocoArgLine</propertyName>
							</configuration>
						</execution>
						<execution>
							<id>check</id>
							<goals>
								<goal>check</goal>
							</goals>
							<configuration>
								<rules></rules>
							</configuration>
						</execution>
					</executions>
				</plugin>
				<!-- Maven Failsafe Plugin -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-failsafe-plugin</artifactId>
					<version>2.22.1</version>
				</plugin>
				<!-- Maven Deploy Plugin -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-deploy-plugin</artifactId>
					<version>2.8.2</version>
				</plugin>
				<!-- Apache Sling Plugin -->
				<plugin>
					<groupId>org.apache.sling</groupId>
					<artifactId>sling-maven-plugin</artifactId>
					<version>2.4.0</version>
					<configuration>
						<slingUrl>http://${aem.host}:${aem.port}/system/console</slingUrl>
						<deploymentMethod>WebConsole</deploymentMethod>
					</configuration>
				</plugin>
				<!-- HTL Maven Plugin -->
				<plugin>
					<groupId>org.apache.sling</groupId>
					<artifactId>htl-maven-plugin</artifactId>
					<version>2.0.2-1.4.0</version>
					<configuration>
						<failOnWarnings>true</failOnWarnings>
					</configuration>
				</plugin>
				<!-- Jackrabbit FileVault Package Plugin -->
				<plugin>
					<groupId>org.apache.jackrabbit</groupId>
					<artifactId>filevault-package-maven-plugin</artifactId>
					<extensions>true</extensions>
					<version>1.1.6</version>
					<configuration>
						<filterSource>src/main/content/META-INF/vault/filter.xml</filterSource>
						<validatorsSettings>
							<jackrabbit-nodetypes>
								<options>
									<!-- use the nodetypes and namespaces from the aem-nodetypes.jar provided in the plugin dependencies -->
									<cnds>tccl:aem.cnd</cnds>
								</options>
							</jackrabbit-nodetypes>
						</validatorsSettings>
					</configuration>
					<dependencies>
						<dependency>
							<groupId>biz.netcentric.aem</groupId>
							<artifactId>aem-nodetypes</artifactId>
							<version>6.5.7.0</version>
						</dependency>
					</dependencies>
				</plugin>
				<!-- AEM Analyser Plugin -->
				<plugin>
					<groupId>com.adobe.aem</groupId>
					<artifactId>aemanalyser-maven-plugin</artifactId>
					<version>${aemanalyser.version}</version>
					<extensions>true</extensions>
				</plugin>
				<!-- Content Package Plugin -->
				<plugin>
					<groupId>com.day.jcr.vault</groupId>
					<artifactId>content-package-maven-plugin</artifactId>
					<version>1.0.2</version>
					<configuration>
						<targetURL>http://${aem.host}:${aem.port}/crx/packmgr/service.jsp</targetURL>
						<failOnError>true</failOnError>
						<userId>${vault.user}</userId>
						<password>${vault.password}</password>
					</configuration>
				</plugin>
				<!-- Maven Enforcer Plugin -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-enforcer-plugin</artifactId>
					<version>3.0.0</version>
				</plugin>
				<!-- Maven Dependency Plugin -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-dependency-plugin</artifactId>
					<version>3.0.0</version>
				</plugin>
				<!-- Build Helper Maven Plugin -->
				<plugin>
					<groupId>org.codehaus.mojo</groupId>
					<artifactId>build-helper-maven-plugin</artifactId>
					<version>3.0.0</version>
				</plugin>
				<!--This plugin's configuration is used to store Eclipse
                    m2e settings only. It has no influence on the Maven build itself. -->
				<plugin>
					<groupId>org.eclipse.m2e</groupId>
					<artifactId>lifecycle-mapping</artifactId>
					<version>1.0.0</version>
					<configuration>
						<lifecycleMappingMetadata>
							<pluginExecutions>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>org.apache.maven.plugins</groupId>
										<artifactId>maven-enforcer-plugin</artifactId>
										<versionRange>[1.0.0,)</versionRange>
										<goals>
											<goal>enforce</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore />
									</action>
								</pluginExecution>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>
                                            org.apache.maven.plugins
                                        </groupId>
										<artifactId>
                                            maven-dependency-plugin
                                        </artifactId>
										<versionRange>
                                            [2.2,)
                                        </versionRange>
										<goals>
											<goal>copy-dependencies</goal>
											<goal>unpack</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore />
									</action>
								</pluginExecution>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>
                                            org.codehaus.mojo
                                        </groupId>
										<artifactId>
                                            build-helper-maven-plugin
                                        </artifactId>
										<versionRange>
                                            [1.5,)
                                        </versionRange>
										<goals>
											<goal>
                                                reserve-network-port
                                            </goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore />
									</action>
								</pluginExecution>
							</pluginExecutions>
						</lifecycleMappingMetadata>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

	<profiles>
		<!-- Development profile: install only the bundle -->
		<profile>
			<id>autoInstallBundle</id>
			<!--
                To enable this feature for a bundle, the sling-maven-plugin
                (without configuration) needs to be included:

                <plugin>
                    <groupId>org.apache.sling</groupId>
                    <artifactId>sling-maven-plugin</artifactId>
                 </plugin>
            -->
			<activation>
				<activeByDefault>false</activeByDefault>
			</activation>
			<build>
				<pluginManagement>
					<plugins>
						<plugin>
							<groupId>org.apache.sling</groupId>
							<artifactId>sling-maven-plugin</artifactId>
							<executions>
								<execution>
									<id>install-bundle</id>
									<goals>
										<goal>install</goal>
									</goals>
								</execution>
							</executions>
						</plugin>
					</plugins>
				</pluginManagement>
			</build>
		</profile>

		<profile>
			<id>autoInstallPackage</id>
			<activation>
				<activeByDefault>false</activeByDefault>
			</activation>
			<build>
				<pluginManagement>
					<plugins>
						<plugin>
							<groupId>org.apache.jackrabbit</groupId>
							<artifactId>filevault-package-maven-plugin</artifactId>
							<executions>
								<execution>
									<id>create-package</id>
									<goals>
										<goal>package</goal>
									</goals>
								</execution>
							</executions>
						</plugin>
						<plugin>
							<groupId>com.day.jcr.vault</groupId>
							<artifactId>content-package-maven-plugin</artifactId>
							<executions>
								<execution>
									<id>install-package</id>
									<goals>
										<goal>install</goal>
									</goals>
									<configuration>
										<targetURL>http://${aem.host}:${aem.port}/crx/packmgr/service.jsp</targetURL>
									</configuration>
								</execution>
							</executions>
						</plugin>
					</plugins>
				</pluginManagement>
			</build>
		</profile>

		<profile>
			<id>autoInstallPackagePublish</id>
			<activation>
				<activeByDefault>false</activeByDefault>
			</activation>
			<build>
				<pluginManagement>
					<plugins>
						<plugin>
							<groupId>org.apache.jackrabbit</groupId>
							<artifactId>filevault-package-maven-plugin</artifactId>
							<executions>
								<execution>
									<id>create-package</id>
									<goals>
										<goal>package</goal>
									</goals>
								</execution>
							</executions>
						</plugin>
						<plugin>
							<groupId>com.day.jcr.vault</groupId>
							<artifactId>content-package-maven-plugin</artifactId>
							<executions>
								<execution>
									<id>install-package-publish</id>
									<goals>
										<goal>install</goal>
									</goals>
									<configuration>
										<targetURL>http://${aem.publish.host}:${aem.publish.port}/crx/packmgr/service.jsp</targetURL>
									</configuration>
								</execution>
							</executions>
						</plugin>
					</plugins>
				</pluginManagement>
			</build>
		</profile>
	</profiles>


	<!-- ====================================================================== -->
	<!-- D E P E N D E N C I E S -->
	<!-- ====================================================================== -->
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.adobe.aem</groupId>
				<artifactId>aem-sdk-api</artifactId>
				<version>${aem.sdk.api}</version>
				<scope>provided</scope>
			</dependency>
			<dependency>
				<groupId>com.adobe.cq</groupId>
				<artifactId>core.wcm.components.core</artifactId>
				<version>${core.wcm.components.version}</version>
			</dependency>
			<!-- Testing -->
			<dependency>
				<groupId>org.junit</groupId>
				<artifactId>junit-bom</artifactId>
				<version>5.8.2</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.mockito</groupId>
				<artifactId>mockito-core</artifactId>
				<version>4.1.0</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.mockito</groupId>
				<artifactId>mockito-junit-jupiter</artifactId>
				<version>4.1.0</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>junit-addons</groupId>
				<artifactId>junit-addons</artifactId>
				<version>1.4</version>
				<scope>test</scope>
			</dependency>
			<!--<dependency>
				<groupId>io.wcm</groupId>
				<artifactId>io.wcm.testing.aem-mock.junit5</artifactId>
				<version>4.1.8</version>
				<scope>test</scope>
			</dependency>-->
			<dependency>
				<groupId>org.apache.sling</groupId>
				<artifactId>org.apache.sling.testing.caconfig-mock-plugin</artifactId>
				<version>1.3.6</version>
			</dependency>
			<dependency>
				<groupId>com.adobe.cq</groupId>
				<artifactId>core.wcm.components.testing.aem-mock-plugin</artifactId>
				<version>${core.wcm.components.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>uk.org.lidalia</groupId>
				<artifactId>slf4j-test</artifactId>
				<version>1.0.1</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.apache.felix</groupId>
				<artifactId>org.apache.felix.scr</artifactId>
				<version>1.6.0</version>
				<scope>provided</scope>
			</dependency>
			<dependency>
				<groupId>org.apache.felix</groupId>
				<artifactId>org.apache.felix.scr.annotations</artifactId>
				<version>1.9.6</version>
				<scope>provided</scope>
			</dependency>
			<dependency>
				<artifactId>gson</artifactId>
				<groupId>com.google.code.gson</groupId>
				<scope>provided</scope>
				<version>2.8.2</version>
			</dependency>

		</dependencies>
	</dependencyManagement>

</project>
