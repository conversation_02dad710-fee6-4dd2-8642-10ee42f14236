<?xml version='1.0' encoding='UTF-8'?>
<profile>
	<name>E.ON Default</name>
	<language>java</language>
	<rules>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>CN_IMPLEMENTS_CLONE_BUT_NOT_CLONEABLE</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NM_FUTURE_KEYWORD_USED_AS_IDENTIFIER</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NM_FUTURE_KEYWORD_USED_AS_MEMBER_IDENTIFIER</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DE_MIGHT_DROP</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DE_MIGHT_IGNORE</key>
			<priority>MAJ<PERSON></priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DP_DO_INSIDE_DO_PRIVILEGED</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DP_CREATE_CLASSLOADER_INSIDE_DO_PRIVILEGED</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>JCIP_FIELD_ISNT_FINAL_IN_IMMUTABLE_CLASS</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DMI_THREAD_PASSED_WHERE_RUNNABLE_EXPECTED</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DMI_COLLECTION_OF_URLS</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DMI_BLOCKING_METHODS_ON_URL</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DMI_ANNOTATION_IS_NOT_VISIBLE_TO_REFLECTION</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DM_EXIT</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DM_RUN_FINALIZERS_ON_EXIT</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DM_STRING_CTOR</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DMI_UNSUPPORTED_METHOD</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DMI_EMPTY_DB_PASSWORD</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DMI_CONSTANT_DB_PASSWORD</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>HRS_REQUEST_PARAMETER_TO_COOKIE</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>HRS_REQUEST_PARAMETER_TO_HTTP_HEADER</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>XSS_REQUEST_PARAMETER_TO_SERVLET_WRITER</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>XSS_REQUEST_PARAMETER_TO_SEND_ERROR</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>XSS_REQUEST_PARAMETER_TO_JSP_WRITER</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SW_SWING_METHODS_INVOKED_IN_SWING_THREAD</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IL_INFINITE_LOOP</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IL_INFINITE_RECURSIVE_LOOP</key>
			<priority>BLOCKER</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IL_CONTAINER_ADDED_TO_ITSELF</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>VO_VOLATILE_REFERENCE_TO_ARRAY</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>UI_INHERITANCE_UNSAFE_GETRESOURCE</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_BOOLEAN_RETURN_NULL</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_SYNC_AND_NULL_CHECK_FIELD</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RpC_REPEATED_CONDITIONAL_TEST</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>AM_CREATES_EMPTY_ZIP_FILE_ENTRY</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>AM_CREATES_EMPTY_JAR_FILE_ENTRY</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IMSE_DONT_CATCH_IMSE</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>CN_IDIOM</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_ARGUMENT_MIGHT_BE_NULL</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_EQUALS_SHOULD_HANDLE_NULL_ARGUMENT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>CO_SELF_NO_OBJECT</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>HE_USE_OF_UNHASHABLE_CLASS</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>HE_HASHCODE_USE_OBJECT_EQUALS</key>
			<priority>BLOCKER</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EQ_COMPARETO_USE_OBJECT_EQUALS</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>HE_INHERITS_EQUALS_USE_HASHCODE</key>
			<priority>BLOCKER</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>HE_EQUALS_NO_HASHCODE</key>
			<priority>BLOCKER</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DL_SYNCHRONIZATION_ON_UNSHARED_BOXED_PRIMITIVE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DL_SYNCHRONIZATION_ON_BOXED_PRIMITIVE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>ESync_EMPTY_SYNC</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>ML_SYNC_ON_FIELD_TO_GUARD_CHANGING_THAT_FIELD</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>ML_SYNC_ON_UPDATED_FIELD</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>MS_OOI_PKGPROTECT</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>MS_FINAL_PKGPROTECT</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>MS_PKGPROTECT</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>MS_MUTABLE_HASHTABLE</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>MS_MUTABLE_ARRAY</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>ES_COMPARING_PARAMETER_STRING_WITH_EQ</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>CO_ABSTRACT_SELF</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IS_FIELD_NOT_GUARDED</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>MSF_MUTABLE_SERVLET_FIELD</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IS2_INCONSISTENT_SYNC</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NN_NAKED_NOTIFY</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>MS_EXPOSE_REP</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EI_EXPOSE_REP</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EI_EXPOSE_REP2</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EI_EXPOSE_STATIC_REP2</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RU_INVOKE_RUN</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SP_SPIN_ON_FIELD</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NS_DANGEROUS_NON_SHORT_CIRCUIT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NS_NON_SHORT_CIRCUIT</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>TLW_TWO_LOCK_WAIT</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>UW_UNCOND_WAIT</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>UR_UNINIT_READ</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>UG_SYNC_SET_UNSYNC_GET</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IC_INIT_CIRCULARITY</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IC_SUPERCLASS_USES_SUBCLASS_DURING_INITIALIZATION</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IT_NO_SUCH_ELEMENT</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DL_SYNCHRONIZATION_ON_SHARED_CONSTANT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DL_SYNCHRONIZATION_ON_BOOLEAN</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DM_STRING_VOID_CTOR</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DM_STRING_TOSTRING</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DM_GC</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DM_BOOLEAN_CTOR</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DM_NUMBER_CTOR</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DM_FP_NUMBER_CTOR</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DM_CONVERT_CASE</key>
			<priority>INFO</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>BX_UNBOXED_AND_COERCED_FOR_TERNARY_OPERATOR</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>BX_BOXING_IMMEDIATELY_UNBOXED</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>BX_BOXING_IMMEDIATELY_UNBOXED_TO_PERFORM_COERCION</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DM_BOXED_PRIMITIVE_TOSTRING</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DM_NEW_FOR_GETCLASS</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DM_MONITOR_WAIT_ON_CONDITION</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RV_01_TO_INT</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DM_NEXTINT_VIA_NEXTDOUBLE</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SQL_NONCONSTANT_STRING_PASSED_TO_EXECUTE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SQL_PREPARED_STATEMENT_GENERATED_FROM_NONCONSTANT_STRING</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DM_USELESS_THREAD</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DC_DOUBLECHECK</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>FI_FINALIZER_NULLS_FIELDS</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>FI_FINALIZER_ONLY_NULLS_FIELDS</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>FI_PUBLIC_SHOULD_BE_PROTECTED</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>FI_EMPTY</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>FI_NULLIFY_SUPER</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>FI_USELESS</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>FI_MISSING_SUPER_CALL</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>FI_EXPLICIT_INVOCATION</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EQ_OTHER_USE_OBJECT</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EQ_OVERRIDING_EQUALS_NOT_SYMMETRIC</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EQ_GETCLASS_AND_CLASS_CONSTANT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EQ_UNUSUAL</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EQ_COMPARING_CLASS_NAMES</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EQ_ALWAYS_TRUE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EQ_ALWAYS_FALSE</key>
			<priority>BLOCKER</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>HSC_HUGE_SHARED_STRING_CONSTANT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DMI_LONG_BITS_TO_DOUBLE_INVOKED_ON_INT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DMI_RANDOM_USED_ONLY_ONCE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RV_ABSOLUTE_VALUE_OF_RANDOM_INT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RV_ABSOLUTE_VALUE_OF_HASHCODE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RV_REM_OF_RANDOM_INT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RV_REM_OF_HASHCODE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>INT_BAD_COMPARISON_WITH_NONNEGATIVE_VALUE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>INT_BAD_COMPARISON_WITH_SIGNED_BYTE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>INT_VACUOUS_COMPARISON</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>INT_BAD_REM_BY_1</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>BIT_IOR_OF_SIGNED_BYTE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>MS_CANNOT_BE_FINAL</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IA_AMBIGUOUS_INVOCATION_OF_INHERITED_OR_OUTER_METHOD</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NM_SAME_SIMPLE_NAME_AS_SUPERCLASS</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NM_SAME_SIMPLE_NAME_AS_INTERFACE</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NM_VERY_CONFUSING</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NM_VERY_CONFUSING_INTENTIONAL</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NM_WRONG_PACKAGE</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NM_WRONG_PACKAGE_INTENTIONAL</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NM_CONFUSING</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NM_METHOD_CONSTRUCTOR_CONFUSION</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NM_CLASS_NOT_EXCEPTION</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RR_NOT_CHECKED</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SR_NOT_CHECKED</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SE_READ_RESOLVE_IS_STATIC</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SE_PRIVATE_READ_RESOLVE_NOT_INHERITED</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SE_READ_RESOLVE_MUST_RETURN_OBJECT</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SE_TRANSIENT_FIELD_OF_NONSERIALIZABLE_CLASS</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SE_TRANSIENT_FIELD_NOT_RESTORED</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SE_METHOD_MUST_BE_PRIVATE</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SE_NO_SUITABLE_CONSTRUCTOR_FOR_EXTERNALIZATION</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SE_NO_SUITABLE_CONSTRUCTOR</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SE_NO_SERIALVERSIONID</key>
			<priority>INFO</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SE_COMPARATOR_SHOULD_BE_SERIALIZABLE</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>WS_WRITEOBJECT_SYNC</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RS_READOBJECT_SYNC</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SE_NONSTATIC_SERIALVERSIONID</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SE_NONFINAL_SERIALVERSIONID</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SE_NONLONG_SERIALVERSIONID</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SE_BAD_FIELD_INNER_CLASS</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SE_INNER_CLASS</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SE_BAD_FIELD_STORE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SC_START_IN_CTOR</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SS_SHOULD_BE_STATIC</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>UUF_UNUSED_FIELD</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>URF_UNREAD_FIELD</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>QF_QUESTIONABLE_FOR_LOOP</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>UWF_NULL_FIELD</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>ST_WRITE_TO_STATIC_FROM_INSTANCE_METHOD</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_DEREFERENCE_OF_READLINE_VALUE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_IMMEDIATE_DEREFERENCE_OF_READLINE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SIC_INNER_SHOULD_BE_STATIC</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SIC_INNER_SHOULD_BE_STATIC_ANON</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SIC_INNER_SHOULD_BE_STATIC_NEEDS_THIS</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>WA_NOT_IN_LOOP</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>WA_AWAIT_NOT_IN_LOOP</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NO_NOTIFY_NOT_NOTIFYALL</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RV_CHECK_FOR_POSITIVE_INDEXOF</key>
			<priority>MINOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RV_DONT_JUST_NULL_CHECK_READLINE</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RV_RETURN_VALUE_IGNORED</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RV_EXCEPTION_NOT_THROWN</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_ALWAYS_NULL</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_STORE_INTO_NONNULL_FIELD</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_ALWAYS_NULL_EXCEPTION</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_NULL_ON_SOME_PATH</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_NULL_ON_SOME_PATH_MIGHT_BE_INFEASIBLE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_NULL_ON_SOME_PATH_EXCEPTION</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_NULL_PARAM_DEREF_NONVIRTUAL</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_NULL_PARAM_DEREF_ALL_TARGETS_DANGEROUS</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_NULL_PARAM_DEREF</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_NONNULL_PARAM_VIOLATION</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_NONNULL_RETURN_VIOLATION</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_CLONE_COULD_RETURN_NULL</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_TOSTRING_COULD_RETURN_NULL</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_GUARANTEED_DEREF</key>
			<priority>BLOCKER</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_GUARANTEED_DEREF_ON_EXCEPTION_PATH</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SI_INSTANCE_BEFORE_FINALS_ASSIGNED</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>OS_OPEN_STREAM</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>OS_OPEN_STREAM_EXCEPTION_PATH</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>PZLA_PREFER_ZERO_LENGTH_ARRAYS</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>UCF_USELESS_CONTROL_FLOW</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>UCF_USELESS_CONTROL_FLOW_NEXT_LINE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RCN_REDUNDANT_COMPARISON_TWO_NULL_VALUES</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RCN_REDUNDANT_COMPARISON_OF_NULL_AND_NONNULL_VALUE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>UL_UNRELEASED_LOCK</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>UL_UNRELEASED_LOCK_EXCEPTION_PATH</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RC_REF_COMPARISON</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EC_UNRELATED_TYPES_USING_POINTER_EQUALITY</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EC_UNRELATED_TYPES</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EC_UNRELATED_INTERFACES</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EC_UNRELATED_CLASS_AND_INTERFACE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EC_NULL_ARG</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>MWN_MISMATCHED_WAIT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>MWN_MISMATCHED_NOTIFY</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SA_LOCAL_SELF_ASSIGNMENT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SA_FIELD_SELF_ASSIGNMENT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SA_FIELD_DOUBLE_ASSIGNMENT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SA_LOCAL_DOUBLE_ASSIGNMENT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SA_FIELD_SELF_COMPUTATION</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SA_LOCAL_SELF_COMPUTATION</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SA_FIELD_SELF_COMPARISON</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SA_LOCAL_SELF_COMPARISON</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>UM_UNNECESSARY_MATH</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RI_REDUNDANT_INTERFACES</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>MTIA_SUSPECT_STRUTS_INSTANCE_FIELD</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>MTIA_SUSPECT_SERVLET_INSTANCE_FIELD</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>ICAST_INTEGER_MULTIPLY_CAST_TO_LONG</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>ICAST_INT_CAST_TO_FLOAT_PASSED_TO_ROUND</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>ICAST_INT_CAST_TO_DOUBLE_PASSED_TO_CEIL</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>ICAST_IDIV_CAST_TO_DOUBLE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>J2EE_STORE_OF_NON_SERIALIZABLE_OBJECT_INTO_SESSION</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IM_BAD_CHECK_FOR_ODD</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DMI_HARDCODED_ABSOLUTE_FILENAME</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DMI_BAD_MONTH</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DMI_USELESS_SUBSTRING</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DMI_CALLING_NEXT_FROM_HASNEXT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SWL_SLEEP_WITH_LOCK_HELD</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DB_DUPLICATE_BRANCHES</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DB_DUPLICATE_SWITCH_CLAUSES</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>XFB_XML_FACTORY_BYPASS</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>CI_CONFUSED_INHERITANCE</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>QBA_QUESTIONABLE_BOOLEAN_ASSIGNMENT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>GC_UNRELATED_TYPES</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DMI_NONSERIALIZABLE_OBJECT_WRITTEN</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>VA_FORMAT_STRING_NO_PREVIOUS_ARGUMENT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>VA_FORMAT_STRING_EXTRA_ARGUMENTS_PASSED</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>VA_FORMAT_STRING_ILLEGAL</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>VA_FORMAT_STRING_MISSING_ARGUMENT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>VA_FORMAT_STRING_BAD_ARGUMENT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>VA_PRIMITIVE_ARRAY_PASSED_TO_OBJECT_VARARG</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>BC_EQUALS_METHOD_SHOULD_WORK_FOR_ALL_OBJECTS</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>BC_BAD_CAST_TO_ABSTRACT_COLLECTION</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>BC_IMPOSSIBLE_CAST</key>
			<priority>BLOCKER</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>NP_NULL_INSTANCEOF</key>
			<priority>BLOCKER</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>BC_IMPOSSIBLE_INSTANCEOF</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>BC_VACUOUS_INSTANCEOF</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>BC_UNCONFIRMED_CAST</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>BC_BAD_CAST_TO_CONCRETE_COLLECTION</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RE_POSSIBLE_UNINTENDED_PATTERN</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RE_BAD_SYNTAX_FOR_REGULAR_EXPRESSION</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>RE_CANT_USE_FILE_SEPARATOR_AS_REGULAR_EXPRESSION</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DLS_OVERWRITTEN_INCREMENT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>ICAST_QUESTIONABLE_UNSIGNED_RIGHT_SHIFT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>ICAST_BAD_SHIFT_AMOUNT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IM_MULTIPLYING_RESULT_OF_IREM</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DMI_INVOKING_TOSTRING_ON_ANONYMOUS_ARRAY</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IM_AVERAGE_COMPUTATION_COULD_OVERFLOW</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>BIT_AND</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>BIT_SIGNED_CHECK</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>BIT_SIGNED_CHECK_HIGH_BIT</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>BIT_AND_ZZ</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>BIT_IOR</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>LI_LAZY_INIT_STATIC</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>LI_LAZY_INIT_UPDATE_STATIC</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>JLM_JSR166_LOCK_MONITORENTER</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>UPM_UNCALLED_PRIVATE_METHOD</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>UMAC_UNCALLABLE_METHOD_OF_ANONYMOUS_CLASS</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>ODR_OPEN_DATABASE_RESOURCE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>ODR_OPEN_DATABASE_RESOURCE_EXCEPTION_PATH</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>ITA_INEFFICIENT_TO_ARRAY</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IJU_ASSERT_METHOD_INVOKED_FROM_RUN_METHOD</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IJU_SETUP_NO_SUPER</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IJU_TEARDOWN_NO_SUPER</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IJU_SUITE_NOT_STATIC</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IJU_BAD_SUITE_METHOD</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IJU_NO_TESTS</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>BOA_BADLY_OVERRIDDEN_ADAPTER</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SQL_BAD_RESULTSET_ACCESS</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SQL_BAD_PREPARED_STATEMENT_ACCESS</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>SIO_SUPERFLUOUS_INSTANCEOF</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EC_ARRAY_AND_NONARRAY</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>EC_BAD_ARRAY_COMPARE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>STI_INTERRUPTED_ON_CURRENTTHREAD</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>STI_INTERRUPTED_ON_UNKNOWNTHREAD</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IP_PARAMETER_IS_DEAD_BUT_OVERWRITTEN</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DLS_DEAD_LOCAL_STORE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DLS_DEAD_LOCAL_STORE_IN_RETURN</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>DLS_DEAD_STORE_OF_CLASS_LITERAL</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>MF_METHOD_MASKS_FIELD</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>MF_CLASS_MASKS_FIELD</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>WMI_WRONG_MAP_ITERATOR</key>
			<priority>MINOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>ISC_INSTANTIATE_STATIC_CLASS</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>REC_CATCH_EXCEPTION</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>FE_TEST_IF_EQUAL_TO_NOT_A_NUMBER</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>FE_FLOATING_POINT_EQUALITY</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>STCAL_STATIC_CALENDAR_INSTANCE</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>STCAL_INVOKE_ON_STATIC_CALENDAR_INSTANCE</key>
			<priority>BLOCKER</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>STCAL_STATIC_SIMPLE_DATE_FORMAT_INSTANCE</key>
			<priority>BLOCKER</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>STCAL_INVOKE_ON_STATIC_DATE_FORMAT_INSTANCE</key>
			<priority>BLOCKER</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>TQ_ALWAYS_VALUE_USED_WHERE_NEVER_REQUIRED</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>TQ_NEVER_VALUE_USED_WHERE_ALWAYS_REQUIRED</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>TQ_MAYBE_SOURCE_VALUE_REACHES_ALWAYS_SINK</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>TQ_MAYBE_SOURCE_VALUE_REACHES_NEVER_SINK</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>TQ_EXPLICIT_UNKNOWN_SOURCE_VALUE_REACHES_NEVER_SINK</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>TQ_EXPLICIT_UNKNOWN_SOURCE_VALUE_REACHES_ALWAYS_SINK</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>IO_APPENDING_TO_OBJECT_OUTPUT_STREAM</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>findbugs</repositoryKey>
			<key>WL_USING_GETCLASS_RATHER_THAN_CLASS_LITERAL</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>CommentedOutCodeLine</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>S1182</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>S1444</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>S1206</key>
			<priority>BLOCKER</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>S1201</key>
			<priority>CRITICAL</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>S00117</key>
			<priority>MAJOR</priority>
			<parameters>
				<parameter>
					<key>format</key>
					<value>^[a-z][a-zA-Z0-9]*$</value>
				</parameter>
			</parameters>
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>S00120</key>
			<priority>MAJOR</priority>
			<parameters>
				<parameter>
					<key>format</key>
					<value>^[a-z]+(\.[a-z][a-z0-9]*)*$</value>
				</parameter>
			</parameters>
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>S1194</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>S00108</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>S134</key>
			<priority>MINOR</priority>
			<parameters>
				<parameter>
					<key>max</key>
					<value>3</value>
				</parameter>
			</parameters>
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>S1226</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>EmptyStatementUsageCheck</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>S1125</key>
			<priority>MINOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>S1126</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>UselessImportCheck</key>
			<priority>MINOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>S1149</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>S1068</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>S1481</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>UnusedPrivateMethod</key>
			<priority>MAJOR</priority>
			<parameters />
		</rule>
		<rule>
			<repositoryKey>squid</repositoryKey>
			<key>S1188</key>
			<priority>MAJOR</priority>
			<parameters>
				<parameter>
					<key>Max</key>
					<value>20</value>
				</parameter>
			</parameters>
		</rule>
	</rules>
</profile>