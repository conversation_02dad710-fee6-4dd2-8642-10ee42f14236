apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  labels:
    app: {{ template "name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
  name: {{ template "name" . }}
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/proxy-body-size: "1024m"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/from-to-www-redirect: "true"
    nginx.ingress.kubernetes.io/add-base-url: "true"
    nginx.ingress.kubernetes.io/base-url-scheme: "https"
    # nginx.ingress.kubernetes.io/auth-type: "basic"
    # nginx.ingress.kubernetes.io/auth-realm: "Dynamic Playground Environment"
    # nginx.ingress.kubernetes.io/auth-secret: "eondeuser-basicauth-secret"
    kubernetes.io/tls-acme: "true"
    certmanager.k8s.io/cluster-issuer: letsencrypt-prod
    certmanager.k8s.io/acme-challenge-type: http01
spec:
  rules:
  - http:
      paths:
      - path: /
        backend:
          serviceName: {{ template "name" . }}
          servicePort: 4502
    host:  "{{.Values.slug}}-author.{{.Values.baseDomain}}"
  - http:
      paths:
      - path: /
        backend:
          serviceName: {{ template "name" . }}
          servicePort: 4503
    host:  "{{.Values.slug}}-publish.{{.Values.baseDomain}}"
  - http:
      paths:
      - path: /
        backend:
          serviceName: {{ template "name" . }}
          servicePort: 8000
    host:  "{{.Values.slug}}-dispatcher.{{.Values.baseDomain}}"
  tls:
    - hosts:
        - "{{.Values.slug}}-author.{{.Values.baseDomain}}"
      secretName: "{{.Values.slug}}-tls"
    - hosts:
        - "{{.Values.slug}}-publish.{{.Values.baseDomain}}"
      secretName: "{{.Values.slug}}-publish-tls"
    - hosts:
        - "{{.Values.slug}}-dispatcher.{{.Values.baseDomain}}"
      secretName: "{{.Values.slug}}-dispatcher-tls"
