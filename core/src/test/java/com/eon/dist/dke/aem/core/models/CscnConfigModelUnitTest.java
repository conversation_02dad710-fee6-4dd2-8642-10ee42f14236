package com.eon.dist.dke.aem.core.models;

import com.day.cq.wcm.api.Page;
import com.eon.dist.dke.aem.core.api.bean.SiteBean;
import com.eon.dist.dke.aem.core.api.service.SiteService;
import com.eon.dist.dke.aem.core.config.CicConfigFactory;
import com.eon.dist.dke.aem.core.config.CscnConfigFactory;
import com.eon.dist.dke.aem.core.constants.GlobalConstants;
import com.eon.dist.dke.aem.core.util.CommonUtil;
import junitx.util.PrivateAccessor;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.api.resource.ValueMap;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import javax.jcr.Property;
import javax.jcr.Value;
import java.lang.reflect.Field;
import java.util.*;

import static junit.framework.Assert.assertEquals;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */

@RunWith(MockitoJUnitRunner.class)
public class CscnConfigModelUnitTest {

    private static final int LEVEL = 4;
    private static final int HOME_PAGE_LEVEL = 3;
    private static final String SERVICE = "service";

    private CscnConfigModel cscnConfigModel;
    private CscnConfigFactory cscnConfigFactory;
    private CicConfigFactory cicConfigFactory;
    private CscnConfigModel cscnConfigModelLocal = new CscnConfigModel();
    private CommonUtil commonUtil = new CommonUtil();
    @Mock
    Page currentPage;

    @Mock
    Page page;

    @Mock
    Page rootPage;

    @Mock
    Page homePage, parentPage;

    @Mock
    Property property;

    @Mock
    Resource resource;

    @Mock
    Resource parsys;

    @Mock
    Iterator<Page> itr;

    @Mock
    Iterator<Resource> childResource;

    @Mock
    ValueMap rootPageData;

    @Mock
    ValueMap parsysData;

    @Mock
    ResourceResolver resourceResolver;

    @Mock
    Dictionary<?, ?> properties;

    @Mock
    Page parentpage;
    @Mock
    Resource header;

    @Mock
    ValueMap headerData;

    @Mock
    SlingHttpServletRequest request;


    @Mock
    Value expiredSessionURLvalue, fallBackGifLoader, sessionTimeOutValue, siteVariable, ssoSessionExpiredUrl,
            globalErrorFlag, errorPageFor500, errorMessageData, inactiveAccountPage, representativeInfoPage,
            loginBox, menuBorderTop, menuArrow, nameContainerBgColor, tileTitleLink, titleTitleLinkHover, registerTitle, registerTitleHover,
            clientId, pfidpadapterid, actionUrl, ssoLoginUrl, callCenterLandingPage, logoutUrl, unauthorizedErrorMessage, unauthorizedButtonLabel, kontactformloggin, kontactteasertitle, portalunlockredirection;


    String value1String = "";

    Map<String, String> flyoutColors;

    ObjectMapper objectMapper = new ObjectMapper();
    ObjectNode jsonConfig;

    @Mock
    SiteService siteService;

    @Mock
    SiteBean siteBean;

    @Before
    public void setUp() throws Exception {
        cscnConfigModel = mock(CscnConfigModel.class);
        cscnConfigFactory = mock(CscnConfigFactory.class);
        cicConfigFactory = mock(CicConfigFactory.class);
        currentPage = mock(Page.class);
        homePage = mock(Page.class);
        property = mock(Property.class);
        page = mock(Page.class);
        rootPageData = mock(ValueMap.class);
        itr = mock(Iterator.class);
        resource = mock(Resource.class);
        expiredSessionURLvalue = mock(Value.class);
        fallBackGifLoader = mock(Value.class);
        sessionTimeOutValue = mock(Value.class);
        siteVariable = mock(Value.class);
        ssoSessionExpiredUrl = mock(Value.class);
        globalErrorFlag = mock(Value.class);
        errorPageFor500 = mock(Value.class);
        errorMessageData = mock(Value.class);
        childResource = mock(Iterator.class);
        parsys = mock(Resource.class);
        parsysData = mock(ValueMap.class);
        inactiveAccountPage = mock(Value.class);
        representativeInfoPage = mock(Value.class);
        loginBox = mock(Value.class);
        menuBorderTop = mock(Value.class);
        menuArrow = mock(Value.class);
        nameContainerBgColor = mock(Value.class);
        tileTitleLink = mock(Value.class);
        titleTitleLinkHover = mock(Value.class);
        registerTitle = mock(Value.class);
        registerTitleHover = mock(Value.class);
        flyoutColors = new HashMap<String, String>();
        jsonConfig = objectMapper.createObjectNode();
        clientId = mock(Value.class);
        pfidpadapterid = mock(Value.class);
        actionUrl = mock(Value.class);
        logoutUrl = mock(Value.class);
        ssoLoginUrl = mock(Value.class);
        callCenterLandingPage = mock(Value.class);
        properties = mock(Dictionary.class);
        parentPage = mock(Page.class);
        unauthorizedErrorMessage = mock(Value.class);
        unauthorizedButtonLabel = mock(Value.class);
        kontactformloggin = mock(Value.class);
        header = mock(Resource.class);
        headerData = mock(ValueMap.class);
        parentpage = mock(Page.class);
        kontactteasertitle = mock(Value.class);
        portalunlockredirection = mock(Value.class);
        request = mock(SlingHttpServletRequest.class);
        value1String = "{\"expiredSessionURL\":\"/content/revu-global/bayernwerk-netz/de/service/gastzugang\",\"fallbackGifLoader\":\"/content/dam/revu-global/bayernwerk-netz/loader.gif\",\"sessionTimeOut\":\"60\",\"siteVariable\":\"EBY\",\"ssoSessionExpiredUrl\":\"/content/revu-global/bayernwerk-netz/de/service/dummylogin\",\"globalErrorFlag\":\"false\",\"globalErrorMessages\":\"{\"required\":\"asda\"}\"}";

        Field flyoutColorsField = CscnConfigModel.class.getDeclaredField("flyoutColors");
        flyoutColorsField.setAccessible(true);
        flyoutColorsField.set(cscnConfigModel, flyoutColors);

        Field propertiesField = CscnConfigFactory.class.getDeclaredField("properties");
        propertiesField.setAccessible(true);
        propertiesField.set(cscnConfigFactory, properties);

        Field cscPropertiesField = CicConfigFactory.class.getDeclaredField("properties");
        cscPropertiesField.setAccessible(true);
        cscPropertiesField.set(cicConfigFactory, properties);

        when(currentPage.getDepth()).thenReturn(LEVEL);
        when(currentPage.getPath()).thenReturn("/content/revu-global/bayernwerk-netz/de/service/gastzugang");
        when(currentPage.getAbsoluteParent(HOME_PAGE_LEVEL)).thenReturn(homePage);
        when(homePage.listChildren()).thenReturn(itr);
        when(itr.hasNext()).thenReturn(true, false);
        when(itr.next()).thenReturn(page);
        when(page.getName()).thenReturn(SERVICE);
        when(page.getProperties()).thenReturn(rootPageData);

        doCallRealMethod().when(cscnConfigModel).activate();
        doCallRealMethod().when(cscnConfigModel).getCscnConfig(page);
        doCallRealMethod().when(cscnConfigModel).getErrorMessages(page);
        doCallRealMethod().when(cscnConfigModel).fetchFlyoutColors(rootPageData);
        doCallRealMethod().when(cscnConfigModel).contactFormUrls(rootPageData, jsonConfig, page);
        doCallRealMethod().when(cscnConfigModel).getServiceDomain();
        doCallRealMethod().when(cscnConfigModel).processLogoutUrl(rootPageData, jsonConfig);

        siteService = mock(SiteService.class);
        siteBean = mock(SiteBean.class);
        when(siteService.getSiteBean(any(), any())).thenReturn(siteBean);
        BasePageModel bpm = mock(BasePageModel.class);
        when(bpm.getSiteBean()).thenReturn(siteBean);
        when(bpm.getSiteService()).thenReturn(siteService);
        when(request.getAttribute(anyString())).thenReturn(bpm);
        PrivateAccessor.setField(cscnConfigModelLocal, "request", request);
        PrivateAccessor.setField(cscnConfigModelLocal, "basePageModel", bpm);
        PrivateAccessor.setField(cscnConfigModelLocal, "currentPage", currentPage);

    }

    @Test
    public void testActivateLessDepth() throws Exception {
        when(currentPage.getDepth()).thenReturn(HOME_PAGE_LEVEL);
        BasePageModel bpm = mock(BasePageModel.class);
        when(bpm.getSiteBean()).thenReturn(siteBean);
        when(request.getAttribute(anyString())).thenReturn(bpm);
        PrivateAccessor.setField(cscnConfigModelLocal, "request", request);
        PrivateAccessor.setField(cscnConfigModelLocal, "basePageModel", bpm);
        PrivateAccessor.setField(cscnConfigModelLocal, "currentPage", currentPage);
        cscnConfigModelLocal.activate();
    }

    @Test
    public void testActivateForLevelDepth() throws Exception {
        when(currentPage.getDepth()).thenReturn(LEVEL);
        when(currentPage.getAbsoluteParent(HOME_PAGE_LEVEL)).thenReturn(homePage);
        when(homePage.listChildren()).thenReturn(itr);
        when(itr.hasNext()).thenReturn(true, false);
        when(itr.next()).thenReturn(page);
        when(page.getName()).thenReturn("any_page_name");
        when(siteBean.getHomePage()).thenReturn(homePage);
        BasePageModel bpm = mock(BasePageModel.class);
        when(bpm.getSiteBean()).thenReturn(siteBean);
        when(bpm.getSiteService()).thenReturn(siteService);
        when(request.getAttribute(anyString())).thenReturn(bpm);
        PrivateAccessor.setField(cscnConfigModelLocal, "request", request);
        PrivateAccessor.setField(cscnConfigModelLocal, "basePageModel", bpm);
        PrivateAccessor.setField(cscnConfigModelLocal, "currentPage", currentPage);
        cscnConfigModelLocal.activate();
    }

    @Test
    public void testActivateForServicePage() throws Exception {

        when(rootPageData.containsKey(GlobalConstants.EXPIRED_SESSION_URL)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.EXPIRED_SESSION_URL)).thenReturn(expiredSessionURLvalue);
        when(expiredSessionURLvalue.toString())
                .thenReturn("/content/revu-global/bayernwerk-netz/de/service/gastzugang");

        when(rootPageData.containsKey(GlobalConstants.FALLBACK_GIF_LOADER)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.FALLBACK_GIF_LOADER)).thenReturn(fallBackGifLoader);
        when(fallBackGifLoader.toString()).thenReturn("/content/dam/revu-global/bayernwerk-netz/loader.gif");

        when(rootPageData.containsKey(GlobalConstants.SESSION_TIME_OUT_VALUE)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.SESSION_TIME_OUT_VALUE)).thenReturn(sessionTimeOutValue);
        when(sessionTimeOutValue.toString()).thenReturn("10");

        when(rootPageData.containsKey(GlobalConstants.SITE_VARIABLE)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.SITE_VARIABLE)).thenReturn(siteVariable);
        when(siteVariable.toString()).thenReturn("EBY");

        when(rootPageData.containsKey(GlobalConstants.SSO_SESSION_EXPIRED_URL)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.SSO_SESSION_EXPIRED_URL)).thenReturn(ssoSessionExpiredUrl);
        when(ssoSessionExpiredUrl.toString()).thenReturn("/content/revu-global/bayernwerk-netz/de/service/dummylogin");

        when(rootPageData.containsKey(GlobalConstants.ERROR_PAGE_FOR_500)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.ERROR_PAGE_FOR_500)).thenReturn(errorPageFor500);
        when(errorPageFor500.toString()).thenReturn("/content/revu-global/bayernwerk-netz/de/service/errorpage");

        when(rootPageData.containsKey(GlobalConstants.GLOBAL_ERROR_FLAG)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.GLOBAL_ERROR_FLAG)).thenReturn(globalErrorFlag);
        when(globalErrorFlag.toString()).thenReturn("true");

        when(rootPageData.containsKey(GlobalConstants.ERROR_MESSAGE_DATA)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.ERROR_MESSAGE_DATA)).thenReturn(errorMessageData);
        when(errorMessageData.toString()).thenReturn("errorMessageData");

        when(page.getContentResource(GlobalConstants.PARSYSABSPATH)).thenReturn(parsys);
        when(parsys.listChildren()).thenReturn(childResource);
        when(childResource.hasNext()).thenReturn(true, true, false);
        when(childResource.next()).thenReturn(resource);
        when(resource.getValueMap()).thenReturn(parsysData);
        when(parsysData.get(GlobalConstants.ID, String.class)).thenReturn("errorCode");
        when(parsysData.get(GlobalConstants.MESSAGE, String.class)).thenReturn("errorMessage");

        // SSO data

        when(rootPageData.containsKey(GlobalConstants.CLIENT_ID)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.CLIENT_ID)).thenReturn(clientId);
        when(clientId.toString()).thenReturn("oic_bayernwerk_csc-netz");

        when(rootPageData.containsKey(GlobalConstants.PFID_ADAPTER_ID)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.PFID_ADAPTER_ID)).thenReturn(pfidpadapterid);
        when(pfidpadapterid.toString()).thenReturn("BayernwerkXID");

        when(rootPageData.containsKey(GlobalConstants.SSO_SESSION_EXPIRED_URL)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.SSO_SESSION_EXPIRED_URL)).thenReturn(ssoSessionExpiredUrl);
        when(ssoSessionExpiredUrl.toString()).thenReturn("/content/revu-global/bayernwerk-netz/de/service/login");

        // ACTION LOGOUT URLS
        when(rootPageData.containsKey(GlobalConstants.READ_SSO_FROM_CONTENT)).thenReturn(true);

        when(rootPageData.containsKey(GlobalConstants.ACTION_URL)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.ACTION_URL)).thenReturn(actionUrl);
        when(actionUrl.toString()).thenReturn("https://qa-login.bayernwerk-netz.de:443/as/authorization.oauth2");

        when(rootPageData.containsKey(GlobalConstants.SSO_LOGOUT)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.SSO_LOGOUT)).thenReturn(logoutUrl);
        when(logoutUrl.toString()).thenReturn("https://qa-login.bayernwerk-netz.de/ext/bayernwerk");

        // ACCOUNT SPECIFIC
        when(rootPageData.containsKey(GlobalConstants.INACTIVE_ACCOUNT_PAGE_URL)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.INACTIVE_ACCOUNT_PAGE_URL)).thenReturn(inactiveAccountPage);
        when(inactiveAccountPage.toString())
                .thenReturn("/content/revu-global/bayernwerk-netz/de/service/public/kundenportal.html");

        when(rootPageData.containsKey(GlobalConstants.URL_REPRESENTATIVE_INFO)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.URL_REPRESENTATIVE_INFO)).thenReturn(representativeInfoPage);
        when(representativeInfoPage.toString())
                .thenReturn("/content/revu-global/bayernwerk-netz/de/service/public/representativeinfo.html");

        when(rootPageData.containsKey(GlobalConstants.CALL_CENTER_LANDING_PAGE)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.CALL_CENTER_LANDING_PAGE)).thenReturn(callCenterLandingPage);
        when(callCenterLandingPage.toString())
                .thenReturn("/content/revu-global/bayernwerk-netz/de/service/dashbord.html");

        // CALL CENTER
        when(rootPageData.containsKey(GlobalConstants.UNAUTHORIZED_ERROR_MESSAGE)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.UNAUTHORIZED_ERROR_MESSAGE)).thenReturn(unauthorizedErrorMessage);
        when(unauthorizedErrorMessage.toString())
                .thenReturn("As a call center agent you are not allowed to change this Data.");

        when(rootPageData.containsKey(GlobalConstants.UNAUTHORIZED_BUTTON_LABEL)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.UNAUTHORIZED_BUTTON_LABEL)).thenReturn(unauthorizedButtonLabel);
        when(unauthorizedButtonLabel.toString()).thenReturn("Reload Page");

        // contact form
        String[] contactlist = {
                "{\"linkText\":\"Kontact-formular\",\"linkDefault\":\"/content/revu-global/bayernwerk-netz/de/bayernwerk-netz-gmbh/presse-kontakt/e-mail\"}"};

        when(page.getParent()).thenReturn(parentpage);
        when(parentpage.getContentResource(GlobalConstants.PARSYS_HEADER_PATH)).thenReturn(header);
        when(header.getValueMap()).thenReturn(headerData);
        when(headerData.containsKey(GlobalConstants.KONTACT_FORM_LIST)).thenReturn(false);
        when(rootPageData.get(GlobalConstants.KONTACT_FORM_TITLE_ROOT)).thenReturn(false);
        when(headerData.get(GlobalConstants.KONTACT_FORM_LIST, String[].class)).thenReturn(contactlist);

        when(rootPageData.containsKey(GlobalConstants.LOGGED_IN_URL)).thenReturn(false);
        when(rootPageData.get(GlobalConstants.LOGGED_IN_URL)).thenReturn(kontactformloggin);
        when(kontactformloggin.toString()).thenReturn("/content/revu-global/bayernwerk-netz/de/service/loggedinpage");

        when(parentpage.getContentResource(GlobalConstants.PARSYS_KONTACT_TEASER_PATH)).thenReturn(header);
        when(header.getValueMap()).thenReturn(headerData);
        when(headerData.containsKey(GlobalConstants.KONTACT_TEASER_TITLE)).thenReturn(false);
        when(headerData.get(GlobalConstants.KONTACT_TEASER_TITLE)).thenReturn(kontactteasertitle);
        when(kontactteasertitle.toString()).thenReturn("Kontakt");

        // PORTAL UNLOCK
        when(rootPageData.containsKey(GlobalConstants.PORTAL_UNLOCK_REDIRECT_URL)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.PORTAL_UNLOCK_REDIRECT_URL)).thenReturn(portalunlockredirection);
        when(portalunlockredirection.toString())
                .thenReturn("/content/revu-global/bayernwerk-netz/de/service/portalfreischaltung.html");

        when(currentPage.getDepth()).thenReturn(LEVEL);
        when(currentPage.getAbsoluteParent(HOME_PAGE_LEVEL)).thenReturn(homePage);
        when(siteBean.getHomePage()).thenReturn(homePage);
        when(currentPage.listChildren()).thenReturn(itr);
        when(homePage.listChildren()).thenReturn(itr);
        when(itr.hasNext()).thenReturn(true, false);
        when(itr.next()).thenReturn(page);
        when(page.getName()).thenReturn(SERVICE);
        when(page.getProperties()).thenReturn(rootPageData);

        cscnConfigModelLocal.activate();
    }

    @Test
    public void testActivateForServicePageEmptyValues() throws Exception {

        when(currentPage.getDepth()).thenReturn(LEVEL);
        when(currentPage.getAbsoluteParent(HOME_PAGE_LEVEL)).thenReturn(homePage);
        when(siteBean.getHomePage()).thenReturn(homePage);
        when(homePage.listChildren()).thenReturn(itr);
        when(currentPage.listChildren()).thenReturn(itr);
        when(itr.hasNext()).thenReturn(true, false);
        when(itr.next()).thenReturn(page);
        when(page.getName()).thenReturn(SERVICE);
        when(page.getProperties()).thenReturn(rootPageData);

        when(rootPageData.containsKey(GlobalConstants.EXPIRED_SESSION_URL)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.FALLBACK_GIF_LOADER)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.SESSION_TIME_OUT_VALUE)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.SITE_VARIABLE)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.SSO_SESSION_EXPIRED_URL)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.GLOBAL_ERROR_FLAG)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.ERROR_MESSAGE_DATA)).thenReturn(false);
        when(page.getContentResource(GlobalConstants.PARSYSABSPATH)).thenReturn(null);
        when(page.getContentResource(GlobalConstants.INACTIVE_ACCOUNT_PAGE_URL)).thenReturn(null);
        when(page.getContentResource(GlobalConstants.URL_REPRESENTATIVE_INFO)).thenReturn(null);

        // SSO
        when(rootPageData.containsKey(GlobalConstants.CLIENT_ID)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.PFID_ADAPTER_ID)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.ACTION_URL)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.REDIRECT_URI)).thenReturn(false);

        // ACTION LOGOUT URLS
        when(rootPageData.containsKey(GlobalConstants.READ_SSO_FROM_CONTENT)).thenReturn(true);
        when(rootPageData.containsKey(GlobalConstants.ACTION_URL)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.SSO_LOGOUT)).thenReturn(false);

        // ACCOUNT SPECIFIC
        when(rootPageData.containsKey(GlobalConstants.INACTIVE_ACCOUNT_PAGE_URL)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.URL_REPRESENTATIVE_INFO)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.CALL_CENTER_LANDING_PAGE)).thenReturn(false);

        // CALL CENTER
        when(rootPageData.containsKey(GlobalConstants.UNAUTHORIZED_ERROR_MESSAGE)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.UNAUTHORIZED_BUTTON_LABEL)).thenReturn(false);

        // PORTAL UNLOCK
        when(rootPageData.containsKey(GlobalConstants.PORTAL_UNLOCK_REDIRECT_URL)).thenReturn(false);


        cscnConfigModelLocal.activate();
    }

    @Test
    public void testGetErrorMessages() throws Exception {
        when(page.getContentResource(GlobalConstants.PARSYSABSPATH)).thenReturn(parsys);
        when(parsys.listChildren()).thenReturn(childResource);
        when(childResource.hasNext()).thenReturn(true, false);
        when(childResource.next()).thenReturn(resource);
        when(resource.getValueMap()).thenReturn(parsysData);
        when(parsysData.get(GlobalConstants.ID, String.class)).thenReturn("errorCode");
        when(parsysData.get(GlobalConstants.MESSAGE, String.class)).thenReturn("errorMessage");

        ObjectNode errorJson = cscnConfigModel.getErrorMessages(page);
        assertEquals(true, errorJson.toString().contains("errorCode"));
    }

    @Test
    public void testFlyoutColors() throws Exception {
        when(rootPageData.containsKey(GlobalConstants.LOGIN_BOX)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.LOGIN_BOX)).thenReturn(loginBox);
        when(loginBox.toString()).thenReturn("rgba(66, 176, 210, 1)");

        when(rootPageData.containsKey(GlobalConstants.MENU_BORDER_TOP)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.MENU_BORDER_TOP)).thenReturn(menuBorderTop);
        when(menuBorderTop.toString()).thenReturn("rgba(0, 145, 187, 1)");

        when(rootPageData.containsKey(GlobalConstants.MENU_ARROW)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.MENU_ARROW)).thenReturn(menuArrow);
        when(menuArrow.toString()).thenReturn("rgba(66, 176, 210, 1)");

        when(rootPageData.containsKey(GlobalConstants.NAME_CONTAINER_BG_COLOR)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.NAME_CONTAINER_BG_COLOR)).thenReturn(nameContainerBgColor);
        when(nameContainerBgColor.toString()).thenReturn("rgba(66, 176, 210, 1)");

        when(rootPageData.containsKey(GlobalConstants.TILE_TITLE_LINK)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.TILE_TITLE_LINK)).thenReturn(tileTitleLink);
        when(tileTitleLink.toString()).thenReturn("rgba(0, 145, 187, 1)");

        when(rootPageData.containsKey(GlobalConstants.TILE_TITLE_LINK_HOVER)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.TILE_TITLE_LINK_HOVER)).thenReturn(titleTitleLinkHover);
        when(titleTitleLinkHover.toString()).thenReturn("rgba(0, 145, 187, 1)");

        when(rootPageData.containsKey(GlobalConstants.REGISTER_TITLE)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.REGISTER_TITLE)).thenReturn(registerTitle);
        when(registerTitle.toString()).thenReturn("rgba(0, 145, 187, 1)");

        when(rootPageData.containsKey(GlobalConstants.REGISTER_TITLE_HOVER)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.REGISTER_TITLE_HOVER)).thenReturn(registerTitleHover);
        when(registerTitleHover.toString()).thenReturn("rgba(0, 145, 187, 1)");

        cscnConfigModel.fetchFlyoutColors(rootPageData);
    }

    @Test
    public void testFlyoutColorsNoValue() throws Exception {
        when(rootPageData.containsKey(GlobalConstants.LOGIN_BOX)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.MENU_BORDER_TOP)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.MENU_ARROW)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.NAME_CONTAINER_BG_COLOR)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.TILE_TITLE_LINK)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.TILE_TITLE_LINK_HOVER)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.REGISTER_TITLE)).thenReturn(false);
        when(rootPageData.containsKey(GlobalConstants.REGISTER_TITLE_HOVER)).thenReturn(false);

        cscnConfigModel.fetchFlyoutColors(rootPageData);
    }

    @Test
    public void testGetFlyoutColors() throws Exception {
        cscnConfigModel.getFlyoutColors();
    }

    @Test
    public void testGetCscnConfigValues() throws Exception {
        cscnConfigModel.getCscnConfigValues();
    }

    @Test
    public void testDefaultConstructor() throws Exception {
        CscnConfigModel spyOnCscnConfigModel = Mockito.spy(new CscnConfigModel());
    }

    @Test
    public void testGetServiceDomain() throws Exception {
        when(CicConfigFactory.getPropertyValue(GlobalConstants.MIDDLEWARE_CONFIG_CSCN_SERVICE_DOMAIN)).thenReturn("https://api.eon.com/gridger/misc/cscn-api/v1");
        assertEquals("https://api.eon.com/gridger/misc/cscn-api/v1", cscnConfigModel.getServiceDomain());
    }


    @Test
    public void testProcessActionLogoutUrlsForConfig() throws Exception {
        when(rootPageData.containsKey(GlobalConstants.READ_SSO_FROM_CONTENT)).thenReturn(true);
        when(rootPageData.containsKey(GlobalConstants.SSO_LOGOUT)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.SSO_LOGOUT)).thenReturn(logoutUrl);
        when(logoutUrl.toString()).thenReturn("https://qa-login.bayernwerk-netz.de/ext/bayernwerk");
        PrivateAccessor.setField(cscnConfigModel, "siteBean", siteBean);
        PrivateAccessor.setField(cscnConfigModel, "appSpecificConfigs", Arrays.asList("pwchange", "ssoLogout", "ssoLoginUrl"));
        when(cscnConfigModel.getCurrentPage()).thenReturn(currentPage);
        cscnConfigModel.processLogoutUrl(rootPageData, jsonConfig);
    }

    @Test
    public void testProcessActionLogoutUrlsForConfigWithNoValue() throws Exception {
        when(rootPageData.containsKey(GlobalConstants.READ_SSO_FROM_CONTENT)).thenReturn(true);
        when(rootPageData.containsKey(GlobalConstants.SSO_LOGOUT)).thenReturn(false);
        when(cicConfigFactory.getPropertyValue(any())).thenReturn("https://ft-ciam.bayernwerk-netz.de/services/oauth2/authorize/expid_403004?response_type=code");
        PrivateAccessor.setField(cscnConfigModel, "siteBean", siteBean);
        PrivateAccessor.setField(cscnConfigModel, "appSpecificConfigs", Arrays.asList("pwchange", "ssoLogout", "ssoLoginUrl"));
        when(cscnConfigModel.getCurrentPage()).thenReturn(currentPage);
        cscnConfigModel.processLogoutUrl(rootPageData, jsonConfig);
    }

    @Test
    public void testProcessActionLogoutUrlsForNoConfig() throws Exception {
        when(rootPageData.containsKey(GlobalConstants.READ_SSO_FROM_CONTENT)).thenReturn(false);
        when(currentPage.getAbsoluteParent(GlobalConstants.ROOT_PAGE_LEVEL)).thenReturn(parentPage);
        when(siteBean.getProjectRootPage()).thenReturn(parentPage);
        when(parentPage.getName()).thenReturn("bayernwerk-netz");
        when(cscnConfigFactory.getPropertyValue("bayernwerk-netz-logout")).thenReturn("https://qa-login.bayernwerk-netz.de/ext/bayernwerk");
        PrivateAccessor.setField(cscnConfigModel, "siteBean", siteBean);
        when(cicConfigFactory.getPropertyValue(any())).thenReturn("https://ft-ciam.bayernwerk-netz.de/services/oauth2/authorize/expid_403004?response_type=code");
        when(cscnConfigModel.getCurrentPage()).thenReturn(currentPage);
        cscnConfigModelLocal.processLogoutUrl(rootPageData, jsonConfig);

    }

    @Test
    public void testProcessActionLogoutUrlsForWrongHost() throws Exception {
        when(rootPageData.containsKey(GlobalConstants.READ_SSO_FROM_CONTENT)).thenReturn(false);
        when(currentPage.getAbsoluteParent(GlobalConstants.ROOT_PAGE_LEVEL)).thenReturn(parentPage);
        when(siteBean.getProjectRootPage()).thenReturn(parentPage);
        when(parentPage.getName()).thenReturn("bayernwerk-netz11");
        PrivateAccessor.setField(cscnConfigModel, "siteBean", siteBean);
        when(cscnConfigFactory.getPropertyValue("bayernwerk-netz-logout")).thenReturn("https://qa-login.bayernwerk-netz.de/ext/bayernwerk");
        cscnConfigModelLocal.processLogoutUrl(rootPageData, jsonConfig);
    }

    public static SlingHttpServletRequest mockSlingRequest(final StringBuffer requestUrl) {
        final SlingHttpServletRequest request = mock(SlingHttpServletRequest.class);
        when(request.getRequestURL()).thenReturn(requestUrl);
        return request;
    }

    @Test
    public void testActivateForcontactFromUrl() throws Exception {

        String[] contactlist = {
                "{\"linkText\":\"Kontact-formular\",\"linkDefault\":\"/content/revu-global/bayernwerk-netz/de/bayernwerk-netz-gmbh/presse-kontakt/e-mail\"}"};
        when(page.getParent()).thenReturn(parentpage);
        when(parentpage.getContentResource(GlobalConstants.PARSYS_HEADER_PATH)).thenReturn(header);
        when(header.getValueMap()).thenReturn(headerData);
        when(headerData.containsKey(GlobalConstants.KONTACT_FORM_LIST)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.KONTACT_FORM_TITLE_ROOT)).thenReturn(true);
        when(headerData.get(GlobalConstants.KONTACT_FORM_LIST, String[].class)).thenReturn(contactlist);

        when(rootPageData.containsKey(GlobalConstants.LOGGED_IN_URL)).thenReturn(true);
        when(rootPageData.get(GlobalConstants.LOGGED_IN_URL)).thenReturn(kontactformloggin);
        when(kontactformloggin.toString()).thenReturn("/content/revu-global/bayernwerk-netz/de/service/loggedinpage");

        when(parentpage.getContentResource(GlobalConstants.PARSYS_KONTACT_TEASER_PATH)).thenReturn(header);
        when(header.getValueMap()).thenReturn(headerData);
        when(headerData.containsKey(GlobalConstants.KONTACT_TEASER_TITLE)).thenReturn(true);
        when(headerData.get(GlobalConstants.KONTACT_TEASER_TITLE)).thenReturn(kontactteasertitle);
        when(kontactteasertitle.toString()).thenReturn("Kontakt");

        cscnConfigModelLocal.contactFormUrls(rootPageData, jsonConfig, page);
    }

    @Test
    public void testActivateForcontactFromUrls() throws Exception {

        String[] contactlist = {
                "{\"linkText\":\"Kontact-formular\",\"linkDefault\":\"/content/revu-global/bayernwerk-netz/de/bayernwerk-netz-gmbh/presse-kontakt/e-mail\"}"};

        when(page.getParent()).thenReturn(parentpage);
        when(parentpage.getContentResource(GlobalConstants.PARSYS_HEADER_PATH)).thenReturn(header);
        when(header.getValueMap()).thenReturn(headerData);
        when(headerData.containsKey(GlobalConstants.KONTACT_FORM_LIST)).thenReturn(false);
        when(rootPageData.get(GlobalConstants.KONTACT_FORM_TITLE_ROOT)).thenReturn(false);
        when(headerData.get(GlobalConstants.KONTACT_FORM_LIST, String[].class)).thenReturn(contactlist);

        when(rootPageData.containsKey(GlobalConstants.LOGGED_IN_URL)).thenReturn(false);
        when(rootPageData.get(GlobalConstants.LOGGED_IN_URL)).thenReturn(kontactformloggin);
        when(kontactformloggin.toString()).thenReturn("/content/revu-global/bayernwerk-netz/de/service/loggedinpage");

        when(parentpage.getContentResource(GlobalConstants.PARSYS_KONTACT_TEASER_PATH)).thenReturn(header);
        when(header.getValueMap()).thenReturn(headerData);
        when(headerData.containsKey(GlobalConstants.KONTACT_TEASER_TITLE)).thenReturn(false);
        when(headerData.get(GlobalConstants.KONTACT_TEASER_TITLE)).thenReturn(kontactteasertitle);
        when(kontactteasertitle.toString()).thenReturn("Kontakt");

        cscnConfigModelLocal.contactFormUrls(rootPageData, jsonConfig, page);
    }
}
