package com.eon.dist.dke.aem.core.models;

import com.day.cq.wcm.api.Page;
import com.eon.dist.dke.aem.core.api.caconfigs.CicCaConfig;
import com.eon.dist.dke.aem.core.config.CicConfigFactory;
import com.eon.dist.dke.aem.core.config.CscnConfigFactory;
import com.eon.dist.dke.aem.core.constants.GlobalConstants;
import com.eon.dist.dke.aem.core.util.CommonUtil;
import com.eon.dist.dke.aem.core.util.LinkUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.api.resource.ValueMap;
import org.apache.sling.caconfig.ConfigurationBuilder;
import org.apache.sling.commons.osgi.PropertiesUtil;
import org.apache.sling.models.annotations.Model;
import org.apache.sling.models.annotations.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import java.util.*;

/**
 * Model to fetch CSCN (Customer Service Configuration Network) configuration.
 * <p>
 * This Sling Model provides access to various CSCN configuration values including
 * SSO settings, error messages, contact form URLs, and other site-specific configurations.
 * It processes and returns configuration data as JSON for client-side consumption.
 * </p>
 *
 * <p>Key features:</p>
 * <ul>
 *   <li>Retrieves and processes CSCN configuration from AEM pages</li>
 *   <li>Handles SSO (Single Sign-On) configuration</li>
 *   <li>Manages error messages and flyout colors</li>
 *   <li>Provides contact form and portal unlock URLs</li>
 *   <li>Supports both CIC and CSC client library configurations</li>
 *   <li>Uses Jackson ObjectMapper for modern JSON processing</li>
 *   <li>Implements comprehensive null safety and error handling</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0
 */
@Model(adaptables = {SlingHttpServletRequest.class})
public class CscnConfigModel extends BaseModel {

    /** Logger instance for this class */
    private static final Logger log = LoggerFactory.getLogger(CscnConfigModel.class);

    private static final String LOGIN_BRIDGE_PAGE_URL = "/login/loginbridge";

    /** Jackson ObjectMapper for JSON processing - thread-safe and reusable */
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /** Map containing flyout color configurations
     * -- GETTER --
     *  Gets the flyout colors configuration map.
     *
     */
    @Getter
    private final Map<String, String> flyoutColors = new HashMap<>();

    @Inject
    @Optional
    private ResourceResolver resourceResolver;

    /**
     * -- GETTER --
     *  Gets the CSCN configuration values as JSON string.
     *
     */
    @Getter
    private String cscnConfigValues = "";

    private String dsoName = "";

    private List<String> appSpecificConfigs = Arrays.asList("pwchange", "ssoLogout", "ssoLoginUrl");

    @PostConstruct
    public void activate() {
        super.init();
        appSpecificConfigs = Arrays.asList("pwchange", "ssoLogout", "ssoLoginUrl");
        Page servicePage = CommonUtil.getServicePage(getCurrentPage(), getBasePageModel().getSiteService());
        if (servicePage != null) {
            dsoName = CommonUtil.getDSOAEMName(servicePage);
            log.debug(" : Method : activate() : service page available in this solution");
            cscnConfigValues = getCscnConfig(servicePage);
        }
        log.debug(" : Method : activate() : [Exit]");
    }

    public Boolean isCicPage() {
        return getCurrentPage().getContentResource().getResourceType().contains("revu-global-netz/cic/components/page/");
    }

    public Boolean isCicContentTree() {
        return CommonUtil.isCicContentTree(getCurrentPage());
    }

    /**
     * Retrieves and processes all CSCN configuration values into a JSON string.
     * <p>
     * This method collects various configuration parameters from the provided page
     * and assembles them into a comprehensive JSON configuration object. It handles
     * null values safely and provides detailed error logging. Uses Jackson ObjectMapper
     * for modern, efficient JSON processing.
     * </p>
     *
     * @param page the AEM page containing configuration properties (must not be null)
     * @return JSON string containing all CSCN configuration values, or empty string if error occurs
     * @throws IllegalArgumentException if page is null
     */
    public String getCscnConfig(Page page) {
        if (page == null) {
            log.error("Page parameter cannot be null");
            throw new IllegalArgumentException("Page parameter cannot be null");
        }

        log.debug("Starting getCscnConfig for page: {}", page.getPath());

        try {
            ObjectNode jsonConfig = OBJECT_MAPPER.createObjectNode();
            ValueMap rootPageData = page.getProperties();

            if (rootPageData == null) {
                log.warn("No properties found for page: {}", page.getPath());
                return "";
            }

            // Fetch flyout colors
            fetchFlyoutColors(rootPageData);

            // Process basic configuration values with null safety
            addConfigValueIfPresent(jsonConfig, rootPageData, GlobalConstants.EXPIRED_SESSION_URL, true);
            addConfigValueIfPresent(jsonConfig, rootPageData, GlobalConstants.FALLBACK_GIF_LOADER, false);
            addConfigValueIfPresent(jsonConfig, rootPageData, GlobalConstants.SESSION_TIME_OUT_VALUE, false);
            addConfigValueIfPresent(jsonConfig, rootPageData, GlobalConstants.SITE_VARIABLE, false);
            addConfigValueIfPresent(jsonConfig, rootPageData, GlobalConstants.ERROR_PAGE_FOR_500, true);

            // Handle global error flag with default value
            if (rootPageData.containsKey(GlobalConstants.GLOBAL_ERROR_FLAG)) {
                Object value = rootPageData.get(GlobalConstants.GLOBAL_ERROR_FLAG);
                jsonConfig.put(GlobalConstants.GLOBAL_ERROR_FLAG, value != null ? value.toString() : "false");
            } else {
                jsonConfig.put(GlobalConstants.GLOBAL_ERROR_FLAG, "false");
            }

            // Add CDN and client library configurations
            addClientLibraryConfigs(jsonConfig);

            // Add captcha configuration
            addCaptchaConfig(jsonConfig);

            // Process complex configuration sections
            ObjectNode errorMessages = getErrorMessages(page);
            if (errorMessages != null) {
                jsonConfig.set(GlobalConstants.GLOBAL_ERROR_MESSAGES, errorMessages);
            }

            processSSOData(rootPageData, jsonConfig);
            processAccountSpecificUrls(rootPageData, jsonConfig);
            processCallCenterValues(rootPageData, jsonConfig);
            contactFormUrls(rootPageData, jsonConfig, page);
            portalUnlockUrls(rootPageData, jsonConfig);
            getSalutationObj(page, jsonConfig);

            String jsonData = OBJECT_MAPPER.writeValueAsString(jsonConfig);
            log.debug("Generated CSCN config JSON with {} properties", jsonConfig.size());
            return jsonData;

        } catch (JsonProcessingException e) {
            log.error("JSON processing error in getCscnConfig: {}", e.getMessage(), e);
            return "";
        } catch (Exception e) {
            log.error("Unexpected error in getCscnConfig: {}", e.getMessage(), e);
            return "";
        }
    }

    /**
     * Safely adds a configuration value to the JSON object if present in the ValueMap.
     * <p>
     * This helper method checks if a key exists in the ValueMap and adds it to the JSON
     * configuration with proper null checking and optional link processing.
     * </p>
     *
     * @param jsonConfig the JSON object to add the value to
     * @param valueMap the ValueMap containing the configuration values
     * @param key the configuration key to check and add
     * @param processAsLink whether to process the value as a link using LinkUtil
     */
    private void addConfigValueIfPresent(ObjectNode jsonConfig, ValueMap valueMap, String key, boolean processAsLink) {
        if (valueMap != null && key != null && valueMap.containsKey(key)) {
            Object value = valueMap.get(key);
            if (value != null) {
                String stringValue = value.toString();
                if (processAsLink) {
                    stringValue = LinkUtil.createValidLinkFromAuthoredProperty(stringValue);
                }
                jsonConfig.put(key, stringValue);
            }
        }
    }

    /**
     * Adds client library configuration values to the JSON object.
     * <p>
     * This method adds CDN domains, URLs, versions, and environment type
     * for both CIC and CSC client libraries.
     * </p>
     *
     * @param jsonConfig the JSON object to add client library configs to
     */
    private void addClientLibraryConfigs(ObjectNode jsonConfig) {
        try {
            jsonConfig.put(GlobalConstants.CIC_CDN_DOMAIN, getCicClientLibDomain());
            jsonConfig.put(GlobalConstants.CSC_CDN_DOMAIN, getCscClientLibDomain());
            jsonConfig.put(GlobalConstants.CIC_CDN_JS_URL, getCicClientLibJsUrl());
            jsonConfig.put(GlobalConstants.CSC_CDN_JS_URL, getCscClientLibJsUrl());
            jsonConfig.put(GlobalConstants.CIC_ENV_TYPE, getEnvType());
            jsonConfig.put(GlobalConstants.CIC_CDN_FILE_VERSION, getCicClientLibVersion());
            jsonConfig.put(GlobalConstants.CIC_SHOULD_USE_CDN, getShouldUseCdn());
            jsonConfig.put(GlobalConstants.CSC_CDN_FILE_VERSION, getCicClientLibVersion());
        } catch (Exception e) {
            log.error("Error adding client library configurations: {}", e.getMessage(), e);
        }
    }

    /**
     * Adds captcha configuration to the JSON object.
     * <p>
     * This method retrieves the captcha site key from OSGi configuration
     * and adds it to the JSON configuration.
     * </p>
     *
     * @param jsonConfig the JSON object to add captcha config to
     */
    private void addCaptchaConfig(ObjectNode jsonConfig) {
        try {
            String captchaSiteKeyKey = getOsgiKey(GlobalConstants.CAPTCHA_SITEKEY);
            String captchaSiteKey = PropertiesUtil.toString(CicConfigFactory.getPropertyValue(captchaSiteKeyKey), "");
            jsonConfig.put(GlobalConstants.CAPTCHA_SITEKEY, captchaSiteKey);
        } catch (Exception e) {
            log.error("Error adding captcha configuration: {}", e.getMessage(), e);
        }
    }

    /**
     * Retrieves and processes salutation objects for the given page.
     * <p>
     * This method extracts salutation data from the page's content resource
     * and adds it to the JSON configuration as an array of salutation objects.
     * </p>
     *
     * @param page the AEM page containing salutation data
     * @param jsonConfig the JSON object to add salutation data to
     */
    public void getSalutationObj(Page page, ObjectNode jsonConfig) {
        if (page == null || jsonConfig == null) {
            log.warn("Page or jsonConfig is null in getSalutationObj");
            return;
        }

        try {
            ArrayNode salutationArray = OBJECT_MAPPER.createArrayNode();
            Resource componentResource = page.getContentResource();

            if (componentResource != null) {
                Resource salutationsList = componentResource.getChild(GlobalConstants.SALUTATIONS);

                if (salutationsList != null) {
                    Iterable<Resource> salutations = salutationsList.getChildren();

                    for (Resource salutationItemResource : salutations) {
                        if (salutationItemResource != null) {
                            ValueMap entries = salutationItemResource.getValueMap();
                            if (entries != null) {
                                ObjectNode salutationItem = OBJECT_MAPPER.createObjectNode();

                                String content = entries.get(GlobalConstants.CONTENT, String.class);
                                String value = entries.get(GlobalConstants.VALUE, String.class);

                                if (content != null) {
                                    salutationItem.put(GlobalConstants.CONTENT, content);
                                }
                                if (value != null) {
                                    salutationItem.put(GlobalConstants.VALUE, value);
                                }

                                salutationArray.add(salutationItem);
                            }
                        }
                    }
                }
            }

            jsonConfig.set(GlobalConstants.SALUTATIONS, salutationArray);
            log.debug("Added {} salutation objects to configuration", salutationArray.size());

        } catch (Exception e) {
            log.error("Error in getSalutationObj: {}", e.getMessage(), e);
        }
    }

    /**
     * Processes portal unlock URLs and adds them to the JSON configuration.
     * <p>
     * This method checks for portal unlock redirect URL in the page properties
     * and adds it to the configuration if present.
     * </p>
     *
     * @param rootPageData the ValueMap containing page properties
     * @param jsonConfig the JSON object to add portal unlock URLs to
     */
    private void portalUnlockUrls(ValueMap rootPageData, ObjectNode jsonConfig) {
        if (rootPageData == null || jsonConfig == null) {
            log.warn("RootPageData or jsonConfig is null in portalUnlockUrls");
            return;
        }

        log.debug("Processing portal unlock URLs");

        try {
            if (rootPageData.containsKey(GlobalConstants.PORTAL_UNLOCK_REDIRECT_URL)) {
                Object urlValue = rootPageData.get(GlobalConstants.PORTAL_UNLOCK_REDIRECT_URL);
                if (urlValue != null) {
                    String processedUrl = LinkUtil.createValidLinkFromAuthoredProperty(urlValue.toString());
                    jsonConfig.put("portalUnlockRedirectUrl", processedUrl);
                    log.debug("Added portal unlock redirect URL: {}", processedUrl);
                }
            }
        } catch (Exception e) {
            log.error("Error processing portal unlock URLs: {}", e.getMessage(), e);
        }
    }

    /**
     * Processes contact form URLs and adds them to the JSON configuration.
     * <p>
     * This method extracts contact form configuration from the page hierarchy
     * and processes various contact-related URLs and titles.
     * </p>
     *
     * @param rootPageData the ValueMap containing root page properties
     * @param jsonConfig the JSON object to add contact form data to
     * @param rootPage the root page containing contact form configuration
     */
    public void contactFormUrls(ValueMap rootPageData, ObjectNode jsonConfig, Page rootPage) {
        if (rootPageData == null || jsonConfig == null || rootPage == null) {
            log.warn("One or more parameters are null in contactFormUrls");
            return;
        }

        log.debug("Processing contact form URLs for page: {}", rootPage.getPath());

        try {
            ObjectNode contactFormConfig = OBJECT_MAPPER.createObjectNode();

            // Process contact form list from header parsys
            processContactFormList(rootPageData, contactFormConfig, rootPage);

            // Add logged-in redirect page if present
            if (rootPageData.containsKey(GlobalConstants.LOGGED_IN_URL)) {
                Object loggedInUrl = rootPageData.get(GlobalConstants.LOGGED_IN_URL);
                if (loggedInUrl != null) {
                    String processedUrl = LinkUtil.createValidLinkFromAuthoredProperty(loggedInUrl.toString());
                    contactFormConfig.put("LoggedInRedirectPage", processedUrl);
                    log.debug("Added logged-in redirect page: {}", processedUrl);
                }
            }

            // Process contact teaser information
            processContactTeaserInfo(contactFormConfig, rootPage);

            jsonConfig.set("contactForm", contactFormConfig);
            log.debug("Contact form configuration processed successfully");

        } catch (Exception e) {
            log.error("Error processing contact form URLs: {}", e.getMessage(), e);
        }
    }

    /**
     * Processes the contact form list from the header parsys.
     * <p>
     * This helper method extracts contact form configuration from the page's
     * header parsys and processes contact form titles and URLs.
     * </p>
     *
     * @param rootPageData the ValueMap containing root page properties
     * @param contactFormConfig the JSON object to add contact form data to
     * @param rootPage the root page containing contact form configuration
     */
    private void processContactFormList(ValueMap rootPageData, ObjectNode contactFormConfig, Page rootPage) {
        try {
            Page parentPage = rootPage.getParent();
            if (parentPage != null) {
                Resource headerParsys = parentPage.getContentResource(GlobalConstants.PARSYS_HEADER_PATH);
                if (headerParsys != null) {
                    ValueMap headerProperties = headerParsys.getValueMap();
                    String[] contactList = headerProperties.get(GlobalConstants.KONTACT_FORM_LIST, String[].class);

                    if (contactList != null) {
                        for (String contactItem : contactList) {
                            if (contactItem != null && !contactItem.trim().isEmpty()) {
                                processContactFormItem(rootPageData, contactFormConfig, contactItem);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error processing contact form list: {}", e.getMessage(), e);
        }
    }

    /**
     * Processes an individual contact form item.
     * <p>
     * This helper method parses a contact form item JSON string and extracts
     * the link text and default URL for contact form configuration.
     * </p>
     *
     * @param rootPageData the ValueMap containing root page properties
     * @param contactFormConfig the JSON object to add contact form data to
     * @param contactItem the JSON string representing a contact form item
     */
    private void processContactFormItem(ValueMap rootPageData, ObjectNode contactFormConfig, String contactItem) {
        try {
            JsonNode itemNode = OBJECT_MAPPER.readTree(contactItem);
            String linkText = itemNode.has("linkText") ? itemNode.get("linkText").asText() : null;
            String linkDefault = itemNode.has("linkDefault") ? itemNode.get("linkDefault").asText() : null;

            if (linkText != null && linkDefault != null) {
                boolean shouldAddContact = false;

                // Check against predefined contact form titles
                if (linkText.equalsIgnoreCase(GlobalConstants.KONTACT_FORM_TITLE) ||
                    linkText.equalsIgnoreCase(GlobalConstants.KONTACT_FORM_TITLE_NORDNETZ) ||
                    linkText.equalsIgnoreCase(GlobalConstants.KONTACT_FORM_TITLE_ENERGIEVERSORGUNG)) {
                    shouldAddContact = true;
                    log.debug("Found predefined contact form title: {}", linkText);
                }

                // Check against root page contact form title
                if (!shouldAddContact && rootPageData.containsKey(GlobalConstants.KONTACT_FORM_TITLE_ROOT)) {
                    Object rootTitleObj = rootPageData.get(GlobalConstants.KONTACT_FORM_TITLE_ROOT);
                    if (rootTitleObj != null && linkText.equalsIgnoreCase(rootTitleObj.toString())) {
                        shouldAddContact = true;
                        log.debug("Found root page contact form title: {}", linkText);
                    }
                }

                if (shouldAddContact) {
                    contactFormConfig.put("ContactLinkTitle", linkText);
                    String processedUrl = LinkUtil.createValidLinkFromAuthoredProperty(linkDefault);
                    contactFormConfig.put("NonLoggedInRedirectPage", processedUrl);
                }
            }
        } catch (Exception e) {
            log.error("Error processing contact form item: {}", e.getMessage(), e);
        }
    }

    /**
     * Processes contact teaser information.
     * <p>
     * This helper method extracts contact teaser title from the page's
     * contact teaser parsys and adds it to the configuration.
     * </p>
     *
     * @param contactFormConfig the JSON object to add contact teaser data to
     * @param rootPage the root page containing contact teaser configuration
     */
    private void processContactTeaserInfo(ObjectNode contactFormConfig, Page rootPage) {
        try {
            Page parentPage = rootPage.getParent();
            if (parentPage != null) {
                Resource contactTeaserParsys = parentPage.getContentResource(GlobalConstants.PARSYS_KONTACT_TEASER_PATH);
                if (contactTeaserParsys != null) {
                    ValueMap contactTeaserProperties = contactTeaserParsys.getValueMap();
                    if (contactTeaserProperties.containsKey(GlobalConstants.KONTACT_TEASER_TITLE)) {
                        String teaserTitle = contactTeaserProperties.get(GlobalConstants.KONTACT_TEASER_TITLE, String.class);
                        if (teaserTitle != null) {
                            contactFormConfig.put("ContactChannelLinkTitle", teaserTitle);
                            log.debug("Added contact channel link title: {}", teaserTitle);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error processing contact teaser info: {}", e.getMessage(), e);
        }
    }

    /**
     * Retrieves and processes error messages for the given page.
     * <p>
     * This method extracts error messages from the page's parsys and
     * returns them as a JSON object with error codes as keys.
     * </p>
     *
     * @param rootPage the root page containing error message configuration
     * @return ObjectNode containing error messages, or null if error occurs
     */
    public ObjectNode getErrorMessages(Page rootPage) {
        if (rootPage == null) {
            log.warn("Root page is null in getErrorMessages");
            return null;
        }

        log.debug("Processing error messages for page: {}", rootPage.getPath());

        try {
            ObjectNode errorMessagesConfig = OBJECT_MAPPER.createObjectNode();

            // Process error messages from parsys
            Resource parsys = rootPage.getContentResource(GlobalConstants.PARSYSABSPATH);
            if (parsys != null) {
                log.debug("Processing error messages from parsys");
                Iterator<Resource> childResources = parsys.listChildren();

                while (childResources.hasNext()) {
                    Resource resource = childResources.next();
                    if (resource != null) {
                        ValueMap properties = resource.getValueMap();
                        if (properties != null) {
                            String errorCode = properties.get(GlobalConstants.ID, String.class);
                            String errorMessage = properties.get(GlobalConstants.MESSAGE, String.class);

                            if (errorCode != null && errorMessage != null) {
                                errorMessagesConfig.put(errorCode, errorMessage);
                                log.debug("Added error message - Code: {}, Message: {}", errorCode, errorMessage);
                            }
                        }
                    }
                }
            } else {
                log.debug("No parsys found for error messages");
            }

            // Add general server error message
            ValueMap rootPageProps = rootPage.getProperties();
            if (rootPageProps != null && rootPageProps.containsKey(GlobalConstants.PN_GENERAL_SERVER_ERROR_MESSAGE)) {
                Object generalErrorMsg = rootPageProps.get(GlobalConstants.PN_GENERAL_SERVER_ERROR_MESSAGE);
                if (generalErrorMsg != null) {
                    errorMessagesConfig.put(GlobalConstants.PN_GENERAL_SERVER_ERROR_MESSAGE, generalErrorMsg.toString());
                } else {
                    errorMessagesConfig.put(GlobalConstants.PN_GENERAL_SERVER_ERROR_MESSAGE, GlobalConstants.DEFAULT_GENERAL_SERVER_ERROR_MESSAGE);
                }
            } else {
                errorMessagesConfig.put(GlobalConstants.PN_GENERAL_SERVER_ERROR_MESSAGE, GlobalConstants.DEFAULT_GENERAL_SERVER_ERROR_MESSAGE);
            }

            log.debug("Error messages configuration processed with {} entries", errorMessagesConfig.size());
            return errorMessagesConfig;

        } catch (Exception e) {
            log.error("Error processing error messages: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Processes SSO (Single Sign-On) configuration data and adds it to the JSON configuration.
     * <p>
     * This method retrieves SSO-related configuration from OSGi properties and page data,
     * including client IDs, login URLs, domains, and various SSO endpoints.
     * </p>
     *
     * @param rootPageData the ValueMap containing root page properties
     * @param jsonConfig the JSON object to add SSO configuration to
     */
    private void processSSOData(ValueMap rootPageData, ObjectNode jsonConfig) {
        if (rootPageData == null || jsonConfig == null) {
            log.warn("RootPageData or jsonConfig is null in processSSOData");
            return;
        }

        log.debug("Processing SSO data");

        try {
            ObjectNode ssoConfig = OBJECT_MAPPER.createObjectNode();

            // Retrieve OSGi configuration keys
            String clientIdKey = getOsgiKey(GlobalConstants.DSO_CLIENT_ID);
            String loginUrlKey = getOsgiKey(GlobalConstants.SSO_LOGIN_URL);
            String pwChangeUrlKey = getOsgiKey(GlobalConstants.PWCHANGE_URL);
            String dsoDomainKey = getOsgiKey(GlobalConstants.DSO_DOMAIN);
            String twoFaPageUrlKey = getOsgiKey(GlobalConstants.TWOFA_ENABLE_PAGE_URL);

            // Retrieve and process configuration values
            String dsoDomain = PropertiesUtil.toString(CicConfigFactory.getPropertyValue(dsoDomainKey), "");
            String ssoLoginUrl = PropertiesUtil.toString(CicConfigFactory.getPropertyValue(loginUrlKey), "");
            if (!ssoLoginUrl.isEmpty()) {
                ssoLoginUrl = LinkUtil.createValidLinkFromAuthoredProperty(ssoLoginUrl);
            }

            // Add basic SSO configuration
            ssoConfig.put(GlobalConstants.DSO_CLIENT_ID,
                PropertiesUtil.toString(CicConfigFactory.getPropertyValue(clientIdKey), ""));
            ssoConfig.put(GlobalConstants.SSO_LOGIN_URL, ssoLoginUrl);
            ssoConfig.put(GlobalConstants.PWCHANGE_URL,
                PropertiesUtil.toString(CicConfigFactory.getPropertyValue(pwChangeUrlKey), ""));
            ssoConfig.put(GlobalConstants.DSO_DOMAIN, dsoDomain);

            String twoFaUrl = PropertiesUtil.toString(CicConfigFactory.getPropertyValue(twoFaPageUrlKey), "");
            if (!twoFaUrl.isEmpty()) {
                twoFaUrl = LinkUtil.createValidLinkFromAuthoredProperty(twoFaUrl);
            }
            ssoConfig.put(GlobalConstants.TWOFA_ENABLE_PAGE_URL, twoFaUrl);

            // Process page-specific SSO URLs
            processPageSpecificSSOUrls(rootPageData, jsonConfig, ssoConfig, dsoDomain);

            // Process logout and exchange URLs
            processLogoutUrl(rootPageData, ssoConfig);
            processExchangeUrl(rootPageData, ssoConfig);

            // Add SSO session expired URL
            if (rootPageData.containsKey(GlobalConstants.SSO_SESSION_EXPIRED_URL)) {
                Object expiredUrl = rootPageData.get(GlobalConstants.SSO_SESSION_EXPIRED_URL);
                if (expiredUrl != null) {
                    String processedUrl = LinkUtil.createValidLinkFromAuthoredProperty(expiredUrl.toString());
                    ssoConfig.put(GlobalConstants.SSO_SESSION_EXPIRED_URL, processedUrl);
                }
            }

            jsonConfig.set("SSO", ssoConfig);
            log.debug("SSO configuration processed successfully");

        } catch (Exception e) {
            log.error("Error processing SSO data: {}", e.getMessage(), e);
        }
    }

    /**
     * Processes page-specific SSO URLs and adds them to the configuration.
     * <p>
     * This helper method handles login success/failure URLs and login bridge path
     * configuration from the page properties.
     * </p>
     *
     * @param rootPageData the ValueMap containing root page properties
     * @param jsonConfig the main JSON configuration object
     * @param ssoConfig the SSO-specific JSON configuration object
     * @param dsoDomain the DSO domain for URL construction
     */
    private void processPageSpecificSSOUrls(ValueMap rootPageData, ObjectNode jsonConfig,
                                           ObjectNode ssoConfig, String dsoDomain) {
        try {
            // Login success URL
            if (rootPageData.containsKey(GlobalConstants.LOGIN_SUCCESS_URL)) {
                Object successUrl = rootPageData.get(GlobalConstants.LOGIN_SUCCESS_URL);
                if (successUrl != null) {
                    String processedUrl = LinkUtil.createValidLinkFromAuthoredProperty(successUrl.toString());
                    ssoConfig.put(GlobalConstants.LOGIN_SUCCESS_URL, processedUrl);
                }
            }

            // Login failure URL
            if (rootPageData.containsKey(GlobalConstants.LOGIN_FAILURE_URL)) {
                Object failureUrl = rootPageData.get(GlobalConstants.LOGIN_FAILURE_URL);
                if (failureUrl != null) {
                    String processedUrl = LinkUtil.createValidLinkFromAuthoredProperty(failureUrl.toString());
                    jsonConfig.put(GlobalConstants.LOGIN_FAILURE_URL, processedUrl);
                }
            }

            // Login SAP failure URL
            if (rootPageData.containsKey(GlobalConstants.LOGIN_SAP_FAILURE_URL)) {
                Object sapFailureUrl = rootPageData.get(GlobalConstants.LOGIN_SAP_FAILURE_URL);
                if (sapFailureUrl != null) {
                    String processedUrl = LinkUtil.createValidLinkFromAuthoredProperty(sapFailureUrl.toString());
                    jsonConfig.put(GlobalConstants.LOGIN_SAP_FAILURE_URL, processedUrl);
                }
            }

            // Login bridge path
            if (rootPageData.containsKey(GlobalConstants.LOGIN_BRIDGE_PATH)) {
                Object bridgePath = rootPageData.get(GlobalConstants.LOGIN_BRIDGE_PATH);
                if (bridgePath != null && dsoDomain != null && !dsoDomain.isEmpty()) {
                    String relativeBridgePath = LinkUtil.createValidLinkFromAuthoredProperty(bridgePath.toString());
                    String shortenedDispatcherUrl = CommonUtil.getShortenedDispatcherUrl(relativeBridgePath);
                    ssoConfig.put(GlobalConstants.LOGIN_BRIDGE_PATH, dsoDomain + shortenedDispatcherUrl);
                }
            }
        } catch (Exception e) {
            log.error("Error processing page-specific SSO URLs: {}", e.getMessage(), e);
        }
    }

    /**
     * Processes call center configuration values and adds them to the JSON configuration.
     * <p>
     * This method extracts call center-related configuration such as unauthorized
     * error messages and button labels from the page properties.
     * </p>
     *
     * @param rootPageData the ValueMap containing root page properties
     * @param jsonConfig the JSON object to add call center configuration to
     */
    private void processCallCenterValues(ValueMap rootPageData, ObjectNode jsonConfig) {
        if (rootPageData == null || jsonConfig == null) {
            log.warn("RootPageData or jsonConfig is null in processCallCenterValues");
            return;
        }

        log.debug("Processing call center values");

        try {
            ObjectNode callCenterConfig = OBJECT_MAPPER.createObjectNode();

            // Unauthorized error message
            if (rootPageData.containsKey(GlobalConstants.UNAUTHORIZED_ERROR_MESSAGE)) {
                Object errorMessage = rootPageData.get(GlobalConstants.UNAUTHORIZED_ERROR_MESSAGE);
                if (errorMessage != null) {
                    callCenterConfig.put(GlobalConstants.UNAUTHORIZED_ERROR_MESSAGE, errorMessage.toString());
                }
            }

            // Unauthorized button label
            if (rootPageData.containsKey(GlobalConstants.UNAUTHORIZED_BUTTON_LABEL)) {
                Object buttonLabel = rootPageData.get(GlobalConstants.UNAUTHORIZED_BUTTON_LABEL);
                if (buttonLabel != null) {
                    callCenterConfig.put(GlobalConstants.UNAUTHORIZED_BUTTON_LABEL, buttonLabel.toString());
                }
            }

            jsonConfig.set("callCenter", callCenterConfig);
            log.debug("Call center configuration processed with {} properties", callCenterConfig.size());

        } catch (Exception e) {
            log.error("Error processing call center values: {}", e.getMessage(), e);
        }
    }

    /**
     * Processes logout URL configuration and adds it to the SSO configuration.
     * <p>
     * This method retrieves the SSO logout URL from OSGi configuration
     * and adds it to the SSO configuration object.
     * </p>
     *
     * @param rootPageData the ValueMap containing root page properties
     * @param ssoConfig the SSO JSON configuration object
     */
    public void processLogoutUrl(ValueMap rootPageData, ObjectNode ssoConfig) {
        if (ssoConfig == null) {
            log.warn("SSO config is null in processLogoutUrl");
            return;
        }

        log.debug("Processing logout URL configuration");

        try {
            String logoutUrlKey = getOsgiKey(GlobalConstants.SSO_LOGOUT);
            String logoutUrl = PropertiesUtil.toString(CicConfigFactory.getPropertyValue(logoutUrlKey), "");
            ssoConfig.put(GlobalConstants.SSO_LOGOUT, logoutUrl);
            log.debug("Added logout URL: {}", logoutUrl);
        } catch (Exception e) {
            log.error("Error processing logout URL: {}", e.getMessage(), e);
        }
    }

    /**
     * Processes exchange URL configuration and adds it to the SSO configuration.
     * <p>
     * This method retrieves the SSO exchange URL from OSGi configuration
     * and adds it to the SSO configuration object.
     * </p>
     *
     * @param rootPageData the ValueMap containing root page properties
     * @param ssoConfig the SSO JSON configuration object
     */
    private void processExchangeUrl(ValueMap rootPageData, ObjectNode ssoConfig) {
        if (ssoConfig == null) {
            log.warn("SSO config is null in processExchangeUrl");
            return;
        }

        log.debug("Processing exchange URL configuration");

        try {
            String exchangeUrl = getCiamExchangeUrl();
            ssoConfig.put(GlobalConstants.SSO_EXCHANGE_URL, exchangeUrl);
            log.debug("Added exchange URL: {}", exchangeUrl);
        } catch (Exception e) {
            log.error("Error processing exchange URL: {}", e.getMessage(), e);
        }
    }
    public String getCiamExchangeUrl() {
        return PropertiesUtil.toString(CicConfigFactory.getPropertyValue(getOsgiKey(GlobalConstants.SSO_EXCHANGE_URL)), "");
    }
    /**
     */
    public void fetchFlyoutColors(ValueMap rootPageData) {
        if (rootPageData.containsKey(GlobalConstants.LOGIN_BOX)) {
            flyoutColors.put(GlobalConstants.LOGIN_BOX, rootPageData.get(GlobalConstants.LOGIN_BOX).toString());
        }
        if (rootPageData.containsKey(GlobalConstants.MENU_BORDER_TOP)) {
            flyoutColors.put(GlobalConstants.MENU_BORDER_TOP, rootPageData.get(GlobalConstants.MENU_BORDER_TOP).toString());
        }
        if (rootPageData.containsKey(GlobalConstants.MENU_ARROW)) {
            flyoutColors.put(GlobalConstants.MENU_ARROW, rootPageData.get(GlobalConstants.MENU_ARROW).toString());
        }
        if (rootPageData.containsKey(GlobalConstants.NAME_CONTAINER_BG_COLOR)) {
            flyoutColors.put(GlobalConstants.NAME_CONTAINER_BG_COLOR, rootPageData.get(GlobalConstants.NAME_CONTAINER_BG_COLOR).toString());
        }
        if (rootPageData.containsKey(GlobalConstants.TILE_TITLE_LINK)) {
            flyoutColors.put(GlobalConstants.TILE_TITLE_LINK, rootPageData.get(GlobalConstants.TILE_TITLE_LINK).toString());
        }
        if (rootPageData.containsKey(GlobalConstants.TILE_TITLE_LINK_HOVER)) {
            flyoutColors.put(GlobalConstants.TILE_TITLE_LINK_HOVER, rootPageData.get(GlobalConstants.TILE_TITLE_LINK_HOVER).toString());
        }
        if (rootPageData.containsKey(GlobalConstants.REGISTER_TITLE)) {
            flyoutColors.put(GlobalConstants.REGISTER_TITLE, rootPageData.get(GlobalConstants.REGISTER_TITLE).toString());
        }
        if (rootPageData.containsKey(GlobalConstants.REGISTER_TITLE)) {
            flyoutColors.put(GlobalConstants.REGISTER_TITLE, rootPageData.get(GlobalConstants.REGISTER_TITLE).toString());
        }
        if (rootPageData.containsKey(GlobalConstants.REGISTER_TITLE_HOVER)) {
            flyoutColors.put(GlobalConstants.REGISTER_TITLE_HOVER, rootPageData.get(GlobalConstants.REGISTER_TITLE_HOVER).toString());
        }
        if (rootPageData.containsKey(GlobalConstants.NAME_CONTAINER_BG_COLOR_NEW_VARIANT)) {
            flyoutColors.put(GlobalConstants.NAME_CONTAINER_BG_COLOR_NEW_VARIANT, rootPageData.get(GlobalConstants.NAME_CONTAINER_BG_COLOR_NEW_VARIANT).toString());
        }
        if (rootPageData.containsKey(GlobalConstants.LOGIN_BOX_ICONNECT)) {
            flyoutColors.put(GlobalConstants.LOGIN_BOX_ICONNECT, rootPageData.get(GlobalConstants.LOGIN_BOX_ICONNECT).toString());
        }
        
    }

    /**
     * Processes account-specific URLs and adds them to the JSON configuration.
     * <p>
     * This method extracts account-related URLs such as representative info,
     * inactive account page, and call center landing page from the page properties.
     * </p>
     *
     * @param rootPageData the ValueMap containing root page properties
     * @param jsonConfig the JSON object to add account-specific URLs to
     */
    private void processAccountSpecificUrls(ValueMap rootPageData, ObjectNode jsonConfig) {
        if (rootPageData == null || jsonConfig == null) {
            log.warn("RootPageData or jsonConfig is null in processAccountSpecificUrls");
            return;
        }

        log.debug("Processing account-specific URLs");

        try {
            // Representative info URL
            if (rootPageData.containsKey(GlobalConstants.URL_REPRESENTATIVE_INFO)) {
                Object repInfoUrl = rootPageData.get(GlobalConstants.URL_REPRESENTATIVE_INFO);
                if (repInfoUrl != null) {
                    String processedUrl = LinkUtil.createValidLinkFromAuthoredProperty(repInfoUrl.toString());
                    jsonConfig.put(GlobalConstants.URL_REPRESENTATIVE_INFO, processedUrl);
                }
            }

            // Inactive account page URL
            if (rootPageData.containsKey(GlobalConstants.INACTIVE_ACCOUNT_PAGE_URL)) {
                Object inactiveAccountUrl = rootPageData.get(GlobalConstants.INACTIVE_ACCOUNT_PAGE_URL);
                if (inactiveAccountUrl != null) {
                    String processedUrl = LinkUtil.createValidLinkFromAuthoredProperty(inactiveAccountUrl.toString());
                    jsonConfig.put(GlobalConstants.INACTIVE_ACCOUNT_PAGE_URL, processedUrl);
                }
            }

            // Call center landing page URL
            if (rootPageData.containsKey(GlobalConstants.CALL_CENTER_LANDING_PAGE)) {
                Object callCenterUrl = rootPageData.get(GlobalConstants.CALL_CENTER_LANDING_PAGE);
                if (callCenterUrl != null) {
                    String processedUrl = LinkUtil.createValidLinkFromAuthoredProperty(callCenterUrl.toString());
                    jsonConfig.put(GlobalConstants.CALL_CENTER_LANDING_PAGE, processedUrl);
                }
            }

            log.debug("Account-specific URLs processed successfully");

        } catch (Exception e) {
            log.error("Error processing account-specific URLs: {}", e.getMessage(), e);
        }
    }

    /**
     * This method will fetch the MW domain from AEM configurations.
     *
     * @return
     */
    public String getServiceDomain() {
        return PropertiesUtil.toString(CscnConfigFactory.getPropertyValue(GlobalConstants.MIDDLEWARE_CONFIG_CSCN_SERVICE_DOMAIN), "");
    }

    /**
     * This will tell whether the CDN or the AEM should be serving the JS/CSS assets
     *
     * @return cic clientLib version
     */
    public boolean getShouldUseCdn() {
        CicCaConfig caConfig = getCaConfig();
        return java.util.Optional.ofNullable(caConfig)
                .map(CicCaConfig::useCdn).orElse(false);
    }

    /**
     * This method will fetch the Cic clientLib version from AEM configurations.
     *
     * @return cic clientLib version
     */
    public String getCicClientLibVersion() {
        CicCaConfig caConfig = getCaConfig();
        return java.util.Optional.ofNullable(caConfig)
                .map(CicCaConfig::cicClientLibsVersion)
                .filter(StringUtils::isNotBlank)
                .orElse("develop");
    }
    /**
     * This method will fetch the Cic clientLib version from AEM configurations.
     *
     * @return cic clientLib version
     */
    public String getCscClientLibVersion() {
        CicCaConfig caConfig = getCaConfig();
        return java.util.Optional.ofNullable(caConfig)
                .map(CicCaConfig::cscClientLibsVersion)
                .filter(StringUtils::isNotBlank)
                .orElse("develop");
    }

    private CicCaConfig getCaConfig() {
        ConfigurationBuilder cBuilder = resource.adaptTo(ConfigurationBuilder.class);
        return java.util.Optional.ofNullable(cBuilder).map(cb -> cb.as(CicCaConfig.class)).orElse(null);
    }

    private String getEnvType() {
        return PropertiesUtil.toString(CicConfigFactory.getPropertyValue(GlobalConstants.CIC_ENV_TYPE), "");
    }

    /**
     * This method will fetch the Cic domain from AEM configurations.
     *
     * @return cic clientlib domain
     */
    public String getCicClientLibDomain() {
        return PropertiesUtil.toString(CicConfigFactory.getPropertyValue(GlobalConstants.CIC_CDN_DOMAIN), "");
    }
    /**
     * This method will fetch the Csc domain from AEM configurations.
     *
     * @return cscn clientlib domain
     */
    public String getCscClientLibDomain() {
        return PropertiesUtil.toString(CicConfigFactory.getPropertyValue(GlobalConstants.CSC_CDN_DOMAIN), "");
    }
    /**
     * This method will fetch the Cic js url from AEM configurations.
     *
     * @return cic clientlib url
     */
    public String getCicClientLibJsUrl() {
        String domain = getCicClientLibDomain();
        String version = getCicClientLibVersion();
        return domain + "/" + version + "/js/main.js";
    }
    /**
     * This method will fetch the Csc js url from AEM configurations.
     *
     * @return cscn clientlib url
     */
    public String getCscClientLibJsUrl() {
        String domain = getCscClientLibDomain();
        String version = getCscClientLibVersion();
        return domain + "/" + version + "/js/main.js";
    }
    /**
     * This method will fetch the Cic css url from AEM configurations.
     *
     * @return cic clientlib url
     */
    public String getCicClientLibCssUrl() {
        String domain = getCicClientLibDomain();
        String version = getCicClientLibVersion();
        return domain + "/" + version + "/css/main.css";
    }
    /**
     * This method will fetch the Csc css url from AEM configurations.
     *
     * @return cscn clientlib url
     */
    public String getCscClientLibCssUrl() {
        String domain = getCscClientLibDomain();
        String version = getCscClientLibVersion();
        return domain + "/" + version + "/css/main.css";
    }
    /**
     * This method will fetch the MW domain from AEM configurations.
     *
     */
    public String getAccountDomain() {
        return PropertiesUtil.toString(CscnConfigFactory.getPropertyValue(GlobalConstants.MIDDLEWARE_CONFIG_CSCN_ACCOUNT_DOMAIN), "");
    }


    private String getOsgiKey(String prop) {
        Boolean isCicContentTree = CommonUtil.isCicContentTree(getCurrentPage());
        Boolean isAppSpecificProp = isAppSpecificConfigProp(prop);
        return (isCicContentTree && isAppSpecificProp ? GlobalConstants.APP_SPECIFIC_CIC_PREFIX : "") + dsoName + "-" + prop;
    }

    private Boolean isAppSpecificConfigProp(String prop) {
        if (prop != null) {
            String[] splitProp = prop.split(GlobalConstants.APP_SPECIFIC_PROP_SEPARATOR);
            String lastProp = splitProp[splitProp.length - 1];
            return lastProp != null && appSpecificConfigs.contains(lastProp);
        }
        return false;
    }

    /**
     * Retrieves CIC domains configuration as JSON string.
     * <p>
     * This method fetches both account and customer domain configurations
     * and returns them as a JSON string containing both domains.
     * </p>
     *
     * @return JSON string containing account and customer domains
     */
    public String getCicDomains() {
        try {
            ObjectNode urlConfig = OBJECT_MAPPER.createObjectNode();
            String accountDomain = PropertiesUtil.toString(CicConfigFactory.getPropertyValue(GlobalConstants.MIDDLEWARE_DOMAIN_ACCOUNT), "");
            String customerDomain = PropertiesUtil.toString(CicConfigFactory.getPropertyValue(GlobalConstants.MIDDLEWARE_DOMAIN_CUSTOMER), "");

            urlConfig.put("account", accountDomain);
            urlConfig.put("customer", customerDomain);

            return OBJECT_MAPPER.writeValueAsString(urlConfig);
        } catch (Exception e) {
            log.error("Error retrieving CIC domains: {}", e.getMessage(), e);
            return "{}";
        }
    }

    public String getCicAccountDomain() {
        return PropertiesUtil.toString(CicConfigFactory.getPropertyValue(GlobalConstants.MIDDLEWARE_DOMAIN_ACCOUNT), "");
    }
    public String getCicServiceDomain() {
        return PropertiesUtil.toString(CicConfigFactory.getPropertyValue(GlobalConstants.MIDDLEWARE_DOMAIN_CUSTOMER), "");
    }
}
