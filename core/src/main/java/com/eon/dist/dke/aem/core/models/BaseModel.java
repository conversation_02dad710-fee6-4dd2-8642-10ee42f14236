package com.eon.dist.dke.aem.core.models;

import com.day.cq.wcm.api.Page;

import org.apache.commons.codec.binary.Hex;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.models.annotations.Model;
import org.apache.sling.models.annotations.injectorspecific.ScriptVariable;
import org.apache.sling.models.annotations.injectorspecific.Self;
import org.apache.sling.models.factory.ModelFactory;

import java.security.SecureRandom;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import static com.eon.dist.dke.aem.core.models.BasePageModel.REQUEST_ATTR_NAME;

@Model(adaptables = SlingHttpServletRequest.class)
@Slf4j
public class BaseModel {

    @ScriptVariable
    private Page currentPage;

    @Inject
    @Getter
    protected Resource resource;

    @Self
    @Getter
    private SlingHttpServletRequest request;

    @Inject
    @Getter
    private ModelFactory modelFactory;

    private BasePageModel basePageModel;

    @PostConstruct
    protected void init() {
        log.debug("Init base model.");
        basePageModel = (BasePageModel) request.getAttribute(REQUEST_ATTR_NAME);
        if (null == basePageModel) {
            log.info("No base page model found in attributes. Creating a new instance. Resource is {}.", resource.getPath());
            basePageModel = modelFactory.getModelFromWrappedRequest(request, currentPage.getContentResource(), BasePageModel.class);
        } else {
            log.debug("Reusing existing base page model.");
        }
    }

    public String getUniqueId() {
        SecureRandom secRandom = new SecureRandom();
        byte[] result = new byte[32];
        secRandom.nextBytes(result);
        return "id-" +  Hex.encodeHexString(result);
    }

    public BasePageModel getBasePageModel() {
        return basePageModel;
    }

    public Page getCurrentPage() {
        return currentPage;
    }
}
