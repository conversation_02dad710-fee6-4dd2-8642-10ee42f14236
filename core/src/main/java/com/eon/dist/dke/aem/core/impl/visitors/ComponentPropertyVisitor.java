package com.eon.dist.dke.aem.core.impl.visitors;

import org.apache.commons.lang3.StringUtils;
import org.apache.sling.api.resource.AbstractResourceVisitor;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ValueMap;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ComponentPropertyVisitor<E> extends AbstractResourceVisitor {

    @Getter
    private boolean foundProperty = false;

    private E value;

    private String propertyName;

    private boolean checkFragments = true;

    public ComponentPropertyVisitor(String propertyName, E value) {
        this.propertyName = propertyName;
        this.value = value;
    }

    public ComponentPropertyVisitor(String propertyName, E value, boolean checkFragments) {
        this.propertyName = propertyName;
        this.checkFragments = checkFragments;
        this.value = value;
    }

    @Override
    public void visit(Resource resource) {
        log.trace("Visiting {}.", resource.getPath());
        if (!foundProperty) {
            ValueMap map = resource.getValueMap();
            if (map.containsKey(propertyName)) {
                E currentValue = (E) map.get(propertyName);
                foundProperty = value.equals(currentValue);
                log.trace("Found property {} with value {}.", propertyName, currentValue);
            }
            if (checkFragments && map.containsKey("fragmentPath")) {
                String path = map.get("fragmentPath", String.class);
                log.trace("Found experience fragment - checking the components {}.", path);
                if (StringUtils.isNotEmpty(path)) {
                    this.accept(resource.getResourceResolver().getResource(path));
                }
            }
        } else {
            log.trace("Property with value {} has already been found.", value);
        }
    }
}
