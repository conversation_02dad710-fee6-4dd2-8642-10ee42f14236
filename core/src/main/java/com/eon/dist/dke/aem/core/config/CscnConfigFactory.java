package com.eon.dist.dke.aem.core.config;

import lombok.extern.slf4j.Slf4j;
import org.osgi.service.component.annotations.Activate;
import org.osgi.service.component.annotations.Component;
import org.osgi.service.component.annotations.Modified;
import org.osgi.service.component.annotations.Deactivate;
import org.osgi.service.component.ComponentContext;
import org.osgi.service.metatype.annotations.AttributeDefinition;
import org.osgi.service.metatype.annotations.Designate;
import org.osgi.service.metatype.annotations.ObjectClassDefinition;

import java.util.Dictionary;

/**
 * Configuration Factory for CSCN (Customer Service Configuration Network).
 * <p>
 * This OSGi service provides centralized configuration management for CSCN parameters
 * including middleware domains, CDN domains, and DSO-specific action/logout URLs.
 * The service uses OSGi Declarative Services annotations for modern component lifecycle management.
 * </p>
 *
 * <AUTHOR>
 * @version 2.0
 */
@Slf4j
@Component(
    service = CscnConfigFactory.class,
    immediate = true,
    name = "com.eon.dist.dke.aem.core.config.CscnConfigFactory"
)
@Designate(ocd = CscnConfigFactory.Config.class)
public class CscnConfigFactory {

    /**
     * Configuration interface for CSCN Configuration Factory.
     * <p>
     * This interface defines all configurable properties for the CSCN system
     * including middleware domains, CDN settings, and DSO-specific URLs.
     * All property names are preserved exactly as they were in the Felix annotations
     * to maintain backward compatibility.
     * </p>
     */
    @ObjectClassDefinition(
        name = "CSCN Configuration Factory",
        description = "CSCN config factory for storing the configurable parameters"
    )
    public @interface Config {

        @AttributeDefinition(
            name = "Middleware Service Domain",
            description = "Middleware SAP Configurations domain"
        )
        String middlewre_config_cscn_service_domain() default "";

        @AttributeDefinition(
            name = "Middleware Account Domain",
            description = "Middleware CIAM Configurations domain"
        )
        String middlewre_config_cscn_account_domain() default "";

        @AttributeDefinition(
            name = "CSC JS CDN Domain",
            description = "JS assets for the project"
        )
        String csc_js_cdn_domain() default "";

        @AttributeDefinition(
            name = "CIC JS CDN Domain",
            description = "JS assets for the project"
        )
        String cic_js_cdn_domain() default "";

        @AttributeDefinition(
            name = "Bayernwerk Netz Action URL",
            description = "bayernwerk-netz action url for PING"
        )
        String bayernwerk_netz_action() default "";

        @AttributeDefinition(
            name = "Bayernwerk Netz Logout URL",
            description = "bayernwerk-netz logout url for PING"
        )
        String bayernwerk_netz_logout() default "";

        @AttributeDefinition(
            name = "Avacon Netz Action URL",
            description = "avacon-netz action url for PING"
        )
        String avacon_netz_action() default "";

        @AttributeDefinition(
            name = "Avacon Netz Logout URL",
            description = "avacon-netz logout url for PING"
        )
        String avacon_netz_logout() default "";

        @AttributeDefinition(
            name = "E-DIS Netz Action URL",
            description = "e-dis-netz action url for PING"
        )
        String e_dis_netz_action() default "";

        @AttributeDefinition(
            name = "E-DIS Netz Logout URL",
            description = "e-dis-netz logout url for PING"
        )
        String e_dis_netz_logout() default "";

        @AttributeDefinition(
            name = "SH Netz Action URL",
            description = "sh-netz action url for PING"
        )
        String sh_netz_action() default "";

        @AttributeDefinition(
            name = "SH Netz Logout URL",
            description = "sh-netz logout url for PING"
        )
        String sh_netz_logout() default "";

        @AttributeDefinition(
            name = "Gasnetz Hamburg Action URL",
            description = "gasnetz-hamburg action url for PING"
        )
        String gasnetz_hamburg_action() default "";

        @AttributeDefinition(
            name = "Gasnetz Hamburg Logout URL",
            description = "gasnetz-hamburg logout url for PING"
        )
        String gasnetz_hamburg_logout() default "";

        @AttributeDefinition(
            name = "Hansegas Action URL",
            description = "hansegas action url for PING"
        )
        String hansegas_action() default "";

        @AttributeDefinition(
            name = "Hansegas Logout URL",
            description = "hansegas logout url for PING"
        )
        String hansegas_logout() default "";

        @AttributeDefinition(
            name = "Nordnetz Action URL",
            description = "nordnetz action url for PING"
        )
        String nordnetz_action() default "";

        @AttributeDefinition(
            name = "Nordnetz Logout URL",
            description = "nordnetz logout url for PING"
        )
        String nordnetz_logout() default "";

        @AttributeDefinition(
            name = "Energienetze Bayern Action URL",
            description = "energienetze-bayern action url for PING"
        )
        String energienetze_bayern_action() default "";

        @AttributeDefinition(
            name = "Energienetze Bayern Logout URL",
            description = "energienetze-bayern logout url for PING"
        )
        String energienetze_bayern_logout() default "";

        @AttributeDefinition(
            name = "Energieversorgung Putzbrunn Action URL",
            description = "energieversorgung-putzbrunn action url for PING"
        )
        String energieversorgung_putzbrunn_action() default "";

        @AttributeDefinition(
            name = "Energieversorgung Putzbrunn Logout URL",
            description = "energieversorgung-putzbrunn logout url for PING"
        )
        String energieversorgung_putzbrunn_logout() default "";

        @AttributeDefinition(
            name = "Avacon Hochdrucknetz Action URL",
            description = "avacon-hochdrucknetz action url for PING"
        )
        String avacon_hochdrucknetz_action() default "";

        @AttributeDefinition(
            name = "Avacon Hochdrucknetz Logout URL",
            description = "avacon-hochdrucknetz logout url for PING"
        )
        String avacon_hochdrucknetz_logout() default "";

        @AttributeDefinition(
            name = "Energienetze Schaafheim Action URL",
            description = "energienetze-schaafheim action url for PING"
        )
        String energienetze_schaafheim_action() default "";

        @AttributeDefinition(
            name = "Energienetze Schaafheim Logout URL",
            description = "energienetze-schaafheim logout url for PING"
        )
        String energienetze_schaafheim_logout() default "";

        @AttributeDefinition(
            name = "Panketal Netz Action URL",
            description = "panketal-netz action url for PING"
        )
        String panketal_netz_action() default "";

        @AttributeDefinition(
            name = "Panketal Netz Logout URL",
            description = "panketal-netz logout url for PING"
        )
        String panketal_netz_logout() default "";
    }


    /**
     * The properties dictionary containing configuration values.
     * Thread-safe access is ensured through synchronized methods.
     */
    private static volatile Dictionary<?, ?> properties = null;

    /**
     * Activate method: initializes the configuration factory with provided properties.
     * <p>
     * This method is called when the OSGi component is activated and stores the
     * configuration properties for later retrieval. Implements comprehensive null
     * checking to prevent NPEs during component lifecycle.
     * </p>
     *
     * @param componentContext the OSGi component context containing configuration properties
     * @throws IllegalStateException if component context is null during activation
     */
    @Activate
    protected void activate(final ComponentContext componentContext) {
        log.info("Activating CscnConfigFactory service");

        if (componentContext == null) {
            log.error("ComponentContext is null during activation - this should not happen");
            throw new IllegalStateException("ComponentContext cannot be null during activation");
        }

        try {
            Dictionary<?, ?> newProperties = componentContext.getProperties();
            if (newProperties != null) {
                synchronized (CscnConfigFactory.class) {
                    properties = newProperties;
                }
                log.info("CscnConfigFactory activated successfully with {} properties",
                    newProperties.size());
                logConfigurationSummary(newProperties);
            } else {
                log.warn("Properties dictionary is null during activation");
                synchronized (CscnConfigFactory.class) {
                    properties = null;
                }
            }
        } catch (Exception e) {
            log.error("Error during CscnConfigFactory activation: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to activate CscnConfigFactory", e);
        }
    }

    /**
     * Re-initializes the property map when configuration changes are detected.
     * <p>
     * This method is called when the OSGi configuration is modified and updates
     * the stored properties with the new values. Implements thread-safe property
     * updates and comprehensive error handling.
     * </p>
     *
     * @param componentContext the OSGi component context containing updated configuration properties
     */
    @Modified
    protected void modified(final ComponentContext componentContext) {
        log.info("Modifying CscnConfigFactory service configuration");

        if (componentContext == null) {
            log.warn("ComponentContext is null during modification - keeping existing properties");
            return;
        }

        try {
            Dictionary<?, ?> newProperties = componentContext.getProperties();
            if (newProperties != null) {
                synchronized (CscnConfigFactory.class) {
                    properties = newProperties;
                }
                log.info("CscnConfigFactory configuration modified successfully with {} properties",
                    newProperties.size());
                logConfigurationSummary(newProperties);
            } else {
                log.warn("Properties dictionary is null during modification - keeping existing properties");
            }
        } catch (Exception e) {
            log.error("Error during CscnConfigFactory modification: {}", e.getMessage(), e);
        }
    }

    /**
     * Deactivate method: cleans up resources when the component is deactivated.
     * <p>
     * This method is called when the OSGi component is being deactivated and
     * performs cleanup operations to prevent memory leaks.
     * </p>
     *
     * @param componentContext the OSGi component context
     */
    @Deactivate
    protected void deactivate(final ComponentContext componentContext) {
        log.info("Deactivating CscnConfigFactory service");

        try {
            synchronized (CscnConfigFactory.class) {
                properties = null;
            }
            log.info("CscnConfigFactory deactivated successfully");
        } catch (Exception e) {
            log.error("Error during CscnConfigFactory deactivation: {}", e.getMessage(), e);
        }
    }

    /**
     * Retrieves a String property value from the configuration with comprehensive null safety.
     * <p>
     * This method safely retrieves a configuration property as a String with proper
     * null checking, thread safety, and error handling. Maintains backward compatibility
     * with the original API while adding defensive programming practices.
     * </p>
     *
     * @param key the configuration property key (must not be null or empty)
     * @return the configuration value as String, or null if not found, key is invalid, or service not initialized
     * @throws IllegalArgumentException if the key is null or empty
     */
    public static String getStringPropertyValue(String key) {
        if (key == null || key.trim().isEmpty()) {
            log.warn("Attempted to retrieve property with null or empty key");
            throw new IllegalArgumentException("Property key cannot be null or empty");
        }

        Dictionary<?, ?> currentProperties;
        synchronized (CscnConfigFactory.class) {
            currentProperties = properties;
        }

        if (currentProperties == null) {
            log.debug("Properties dictionary is null, service may not be initialized yet for key: {}", key);
            return null;
        }

        try {
            Object value = getPropertyWithMapping(key, currentProperties);
            if (value == null) {
                log.debug("No value found for property key: {}", key);
                return null;
            }

            String stringValue = value.toString();
            log.trace("Retrieved property '{}' = '{}'", key, stringValue);
            return stringValue;

        } catch (Exception e) {
            log.error("Error retrieving string property for key '{}': {}", key, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Retrieves a property value from the configuration as an Object with comprehensive null safety.
     * <p>
     * This method safely retrieves a configuration property as an Object with proper
     * null checking, thread safety, and error handling. Maintains backward compatibility
     * with the original API while adding defensive programming practices.
     * </p>
     *
     * @param key the configuration property key (must not be null or empty)
     * @return the configuration value as Object, or null if not found, key is invalid, or service not initialized
     * @throws IllegalArgumentException if the key is null or empty
     */
    public static Object getPropertyValue(String key) {
        if (key == null || key.trim().isEmpty()) {
            log.warn("Attempted to retrieve property with null or empty key");
            throw new IllegalArgumentException("Property key cannot be null or empty");
        }

        Dictionary<?, ?> currentProperties;
        synchronized (CscnConfigFactory.class) {
            currentProperties = properties;
        }

        if (currentProperties == null) {
            log.debug("Properties dictionary is null, service may not be initialized yet for key: {}", key);
            return null;
        }

        try {
            Object value = getPropertyWithMapping(key, currentProperties);
            log.trace("Retrieved property '{}' = '{}'", key, value);
            return value;

        } catch (Exception e) {
            log.error("Error retrieving property for key '{}': {}", key, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Maps OSGi annotation property names to original Felix property names for backward compatibility.
     * <p>
     * This method ensures that existing code using the original property names continues
     * to work after migration to OSGi DS annotations. The mapping handles the conversion
     * between underscore-separated annotation names and hyphen-separated original names.
     * </p>
     *
     * @param annotationPropertyName the property name from OSGi annotation
     * @return the original property name used in Felix annotations, or the input if no mapping exists
     */
    private static String mapToOriginalPropertyName(String annotationPropertyName) {
        if (annotationPropertyName == null) {
            return null;
        }

        // Handle special cases for middleware properties
        if ("middlewre.config.cscn.service.domain".equals(annotationPropertyName)) {
            return "middlewre.config.cscn.service.domain";
        }
        if ("middlewre.config.cscn.account.domain".equals(annotationPropertyName)) {
            return "middlewre.config.cscn.account.domain";
        }

        // Handle CDN domain properties
        if ("csc_js_cdn_domain".equals(annotationPropertyName)) {
            return "csc-js-cdn-domain";
        }
        if ("cic_js_cdn_domain".equals(annotationPropertyName)) {
            return "cic-js-cdn-domain";
        }

        // Handle DSO-specific properties - convert underscores to hyphens
        return annotationPropertyName.replace("_", "-");
    }

    /**
     * Enhanced property retrieval that checks both original and mapped property names.
     * <p>
     * This method first tries to retrieve the property using the original name,
     * then falls back to checking the mapped annotation name for backward compatibility.
     * </p>
     *
     * @param key the property key to retrieve
     * @param currentProperties the properties dictionary
     * @return the property value or null if not found
     */
    private static Object getPropertyWithMapping(String key, Dictionary<?, ?> currentProperties) {
        if (key == null || currentProperties == null) {
            return null;
        }

        // First try the original key
        Object value = currentProperties.get(key);
        if (value != null) {
            return value;
        }

        // Try the mapped key (convert hyphens to underscores for annotation names)
        String mappedKey = key.replace("-", "_");
        if (!mappedKey.equals(key)) {
            value = currentProperties.get(mappedKey);
            if (value != null) {
                log.trace("Found property '{}' using mapped key '{}'", key, mappedKey);
                return value;
            }
        }

        return null;
    }

    /**
     * Logs a summary of the current configuration for debugging purposes.
     * <p>
     * This helper method provides visibility into the loaded configuration
     * without exposing sensitive values in the logs.
     * </p>
     *
     * @param configProperties the properties dictionary to summarize
     */
    private void logConfigurationSummary(Dictionary<?, ?> configProperties) {
        if (configProperties == null) {
            log.debug("Configuration properties is null");
            return;
        }

        try {
            log.debug("Configuration summary: {} total properties loaded", configProperties.size());

            // Log presence of key configuration categories without exposing values
            boolean hasMiddlewareConfig = getPropertyWithMapping("middlewre.config.cscn.service.domain", configProperties) != null ||
                                        getPropertyWithMapping("middlewre.config.cscn.account.domain", configProperties) != null;
            boolean hasCdnConfig = getPropertyWithMapping("csc-js-cdn-domain", configProperties) != null ||
                                 getPropertyWithMapping("cic-js-cdn-domain", configProperties) != null;

            log.debug("Configuration categories - Middleware: {}, CDN: {}", hasMiddlewareConfig, hasCdnConfig);

        } catch (Exception e) {
            log.debug("Error logging configuration summary: {}", e.getMessage());
        }
    }
}
