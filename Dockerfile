FROM repo.eon-cds.de/aem-docker-local/jdk11-node-v14:latest

ARG ARTIFACTORY_USER
ARG ARTIFACTORY_URL
ARG ARTIFACTORY_NPM_USER
ARG ARTIFACTORY_NPM_TOKEN
ARG ARTIFACTORY_PASSWORD
ARG MAVEN_COMMON_OPTS
ARG MAVEN_COMMON_FULL_OPTS
ARG GROUP_FRONTEND_ID
ARG ARTIFACT_FRONTEND_ID
ARG DOCKER_SONAR_CLI_OPTS

ARG versionDkereact
ARG versionPatternlab


COPY . /opt

RUN cd /opt && npm config set progress=false && npm config set registry ${ARTIFACTORY_URL}/artifactory/api/npm/funke-npm-virtual && curl -u ${ARTIFACTORY_NPM_USER}:${ARTIFACTORY_NPM_TOKEN} ${ARTIFACTORY_URL}/artifactory/api/npm/auth > .npmrc
RUN echo MAVEN_COMMON_FULL_OPTS  ${MAVEN_COMMON_FULL_OPTS}  DOCKER_SONAR_CLI_OPTS $DOCKER_SONAR_CLI_OPTS
RUN cd /opt && mvn ${MAVEN_COMMON_FULL_OPTS} build-helper:parse-version versions:set -DnewVersion="1.0.999"
RUN cd /opt && mvn ${MAVEN_COMMON_OPTS} -DskipTests -Dmaven.test.skip=true install -Dversion.patternlab=https://repo.eon-cds.de/artifactory/funke-npm-virtual/aem-dso-patternlab/-/aem-dso-patternlab-${versionPatternlab}.tgz -Dversion.dkereact=https://repo.eon-cds.de/artifactory/funke-npm-virtual/react-aem-project-bundler/-/react-aem-project-bundler-${versionDkereact}.tgz
RUN cd /opt && mvn ${MAVEN_COMMON_OPTS} test &&  mvn ${MAVEN_COMMON_OPTS} sonar:sonar $DOCKER_SONAR_CLI_OPTS
RUN rm -rf /opt/* && du -sh /root/.m2/repository && rm -rf /root/.m2/repository/com/eon/*  && du -sh /root/.m2/repository
