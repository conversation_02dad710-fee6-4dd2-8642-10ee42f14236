<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:mix="http://www.jcp.org/jcr/mix/1.0"
    jcr:language="en"
    jcr:mixinTypes="[mix:language]"
    jcr:primaryType="sling:Folder">
    <com.eon.de.foundation.components.content.parsys.margin.description
        jcr:primaryType="sling:MessageEntry"
        sling:key="com.eon.de.foundation.components.content.parsys.margin.description"
        sling:message="Include margin between the present components"/>
    <com.eon.de.foundation.components.content.parsys.margin.title
        jcr:primaryType="sling:MessageEntry"
        sling:key="com.eon.de.foundation.components.content.parsys.margin.title"
        sling:message="Margins"/>
</jcr:root>
