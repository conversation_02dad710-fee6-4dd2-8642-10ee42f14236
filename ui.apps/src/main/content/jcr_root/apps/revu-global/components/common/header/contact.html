<div class="header-layer-contact" id="headerLayerContact" data-sly-use.headerData="${'/apps/revu-global/components/common/includes/multifieldValues.js' @ propertyName='contactlist'}" data-sly-test="${properties.contactDescription || headerData.valueList.length}" data-sly-use.lib="revu-global/components/common/includes/templateLib.html">
    <div class="container-fluid">
        <div class="container">
            <div class="row">
                <div class="col-lg-4">
                    <p class="header-layer-contact__heading">${properties.contactHeadline}</p>
                    <p class="header-layer-contact__text">${properties.contactDescription @ context='html'}</p>
                </div>
                <div class="col-lg-8">
                    <div class="flex-container" data-sly-list.contactList="${headerData.valueList}">
                        <div data-sly-use.pagelink="${'com.eon.dist.dke.aem.core.util.UtilityMethods' @ passedUrl=contactList.linkDefault}" data-sly-use.colortheme="/apps/revu-global/components/page/basepage/colortheme.js" data-sly-unwrap></div>
                        <a tabindex="0" onkeydown="if (event.key === ' ' || event.key === 'Enter') { event.preventDefault(); event.stopPropagation(); this.click(); }" href="${pagelink.externalizedLink}" class="large-icon-link link--${colortheme.header.naviMSFlyoutLink}" title="${contactList.linkText}" target="${(contactList.newTabCheck || contactList.newTabCheck[0]) ? '_blank' : '_self'}">
						   	<i>
						   	    <sly data-sly-call="${lib.inlinesvg @ assetPath=contactList.linkIcon, title=contactList.linkText, width='54', height='54'}"></sly>
							</i>							
							<span>${contactList.linkText}</span>
                        </a>								
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const headerLayerContact = document.getElementById('headerLayerContact');
        const openTriggerButton = document.querySelector('.header-meta__list');
        const logoSizeChange = document.querySelector('.logo');
        const dividerLineDivChange = document.querySelector('.dividing-line');
        const headerLayerWrapperChange = document.querySelector('.header-layer-wrapper');
        if (headerLayerContact) {
            const closeContactComponent = () => {
                headerLayerContact.style.display = 'none';
                const listItems = openTriggerButton.querySelectorAll("li");
                listItems.forEach(li => {
                    const interactiveElement = li.querySelector('a, button, [tabindex="-1"], *'); // Targets <a>, <button>, or any element with tabindex="-1", or the first child element if none of those exist
                    if (interactiveElement) {
                        interactiveElement.tabIndex = 0;
                    } else {
                        // Fallback: If no specific interactive element found, set on the li itself
                        li.tabIndex = 0;
                    }
                });
                takeToParent();
            };

            // Event listener for focusout on the entire component
            headerLayerContact.addEventListener('focusout', function (event) {
                setTimeout(() => {
                    const newlyFocusedElement = document.activeElement;
                    const isFocusInsideComponent = headerLayerContact.contains(newlyFocusedElement);
                    if (!isFocusInsideComponent) {
                        closeContactComponent();
                    }
                }, 10);
            });
            const takeToParent = () => {
                if (openTriggerButton) {
                    headerLayerContact.style.display = 'none'; // Show the component
                    const headerParentListContainerNew = document.querySelector(".header-meta__list");
                    const firstTabbable = headerParentListContainerNew.querySelector('a[tabindex="0"], button[tabindex="0"]');
                    if (firstTabbable) {
                        firstTabbable.classList.remove('actv');
                        logoSizeChange.classList.remove('small')
                        dividerLineDivChange.removeAttribute('style');
                        headerLayerWrapperChange.classList.remove('jsopen');
                        headerLayerWrapperChange.style.display = 'none';
                        firstTabbable.focus();
                    }
                }
            };
        } 
    });
</script>