<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0"
    jcr:primaryType="cq:Panel"
    title="revu.global.components.forms.avacongasconnectioncampaign.dialog.tab_connectionpoint.title">
    <items jcr:primaryType="cq:WidgetCollection">
        <connectionAddressStreet
            jcr:primaryType="cq:Widget"
            collapsed="{Boolean}false"
            collapsible="{<PERSON><PERSON><PERSON>}false"
            xtype="dialogfieldset">
            <items jcr:primaryType="cq:WidgetCollection">
                <connectionAddressStreetLabel
                    jcr:primaryType="cq:Widget"
                    fieldLabel="revu.global.components.forms.avacongasconnectioncampaign.dialog.tab_connectionpoint.street"
                    defaultValue="Strasse"
                    name="./connectionAddressStreetLabel"
                    xtype="textfield"/>
                 <connectionAddressStreetRequired
                    jcr:primaryType="cq:Widget"
                    fieldLabel="revu.global.components.forms.avacongasconnectioncampaign.dialog.tab_connectionpoint.street.required"
                    inputValue="true"
                    name="./connectionAddressStreetRequired"
                    type="checkbox"
                    xtype="selection"/>
            </items>
        </connectionAddressStreet>
        <connectionAddressHouseNumber
            jcr:primaryType="cq:Widget"
            collapsed="{Boolean}false"
            collapsible="{Boolean}false"
            xtype="dialogfieldset">
            <items jcr:primaryType="cq:WidgetCollection">
                <connectionAddressHouseNumberLabel
                    jcr:primaryType="cq:Widget"
                    fieldLabel="revu.global.components.forms.avacongasconnectioncampaign.dialog.tab_connectionpoint.house.number"
                    defaultValue="Hausnummer"
                    name="./connectionAddressHouseNumberLabel"
                    xtype="textfield"/>
                 <connectionAddressHouseNumberRequired
                    jcr:primaryType="cq:Widget"
                    fieldLabel="revu.global.components.forms.avacongasconnectioncampaign.dialog.tab_connectionpoint.house.number.required"
                    inputValue="true"
                    name="./connectionAddressHouseNumberRequired"
                    type="checkbox"
                    xtype="selection"/>
            </items>
        </connectionAddressHouseNumber>
        <connectionAddressZip
            jcr:primaryType="cq:Widget"
            collapsed="{Boolean}false"
            collapsible="{Boolean}false"
            xtype="dialogfieldset">
            <items jcr:primaryType="cq:WidgetCollection">
                <connectionAddressZipLabel
                    jcr:primaryType="cq:Widget"
                    fieldLabel="revu.global.components.forms.avacongasconnectioncampaign.dialog.tab_connectionpoint.zip"
                    defaultValue="PLZ"
                    name="./connectionAddressZipLabel"
                    xtype="textfield"/>
               <connectionAddressZipRequired
                    jcr:primaryType="cq:Widget"
                    fieldLabel="revu.global.components.forms.avacongasconnectioncampaign.dialog.tab_connectionpoint.zip.required"
                    inputValue="true"
                    name="./connectionAddressZipRequired"
                    type="checkbox"
                    xtype="selection"/>
                <connectionAddressZipMaxLength
                    jcr:primaryType="cq:Widget"
                    fieldLabel="revu.global.components.forms.avacongasconnectioncampaign.dialog.tab_connectionpoint.zip.maxlength"
                    name="./connectionAddressZipMaxLength"
                    defaultValue="5"
                    xtype="numberfield"/>
                <connectionAddressZipMinLength
                    jcr:primaryType="cq:Widget"
                    fieldLabel="revu.global.components.forms.avacongasconnectioncampaign.dialog.tab_connectionpoint.zip.minlength"
                    name="./connectionAddressZipMinLength"
                    defaultValue="5"
                    xtype="numberfield"/>
               <connectionAddressZipErrorMessage
                    jcr:primaryType="cq:Widget"
                    fieldLabel="revu.global.components.forms.avacongasconnectioncampaign.dialog.tab_connectionpoint.zip.error.message"
                    name="./connectionAddressZipErrorMessage"
                    xtype="textfield"/>
               <connectionAddressZipMinLengthErrorMessage
                    jcr:primaryType="cq:Widget"
                    fieldLabel="revu.global.components.forms.avacongasconnectioncampaign.dialog.tab_connectionpoint.zip.min.length.error.message"
                    name="./connectionAddressZipMinLengthErrorMessage"
                    xtype="textfield"/>
            </items>
        </connectionAddressZip>
        <connectionAddressTown
            jcr:primaryType="cq:Widget"
            collapsed="{Boolean}false"
            collapsible="{Boolean}false"
            xtype="dialogfieldset">
            <items jcr:primaryType="cq:WidgetCollection">
                <connectionAddressTownLabel
                    jcr:primaryType="cq:Widget"
                    fieldLabel="revu.global.components.forms.avacongasconnectioncampaign.dialog.tab_connectionpoint.town"
                    defaultValue="Ort"
                    name="./connectionAddressTownLabel"
                    xtype="textfield"/>
               <connectionAddressTownRequired
                    jcr:primaryType="cq:Widget"
                    fieldLabel="revu.global.components.forms.avacongasconnectioncampaign.dialog.tab_connectionpoint.town.required"
                    inputValue="true"
                    name="./connectionAddressTownRequired"
                    type="checkbox"
                    xtype="selection"/>
                <connectionAddressTownErrorMessage
                    jcr:primaryType="cq:Widget"
                    fieldLabel="revu.global.components.forms.avacongasconnectioncampaign.dialog.tab_connectionpoint.town.error.message"
                    name="./connectionAddressTownErrorMessage"
                    xtype="textfield"/>
            </items>
        </connectionAddressTown>
    </items>
</jcr:root>
