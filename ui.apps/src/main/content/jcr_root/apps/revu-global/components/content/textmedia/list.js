"use strict";
use(function() {
	var linkUtil = Packages.com.eon.dist.dke.aem.core.util.LinkUtil;
	var richText = this.arg || '';
	var value = '';

	/* Check if internal links are present in richtext and externalize it */
	function externalizeDamPath(richText, start, end) {
		var contentPath = String(richText.substring(start, end).match(/<a\s+(?:[^>]*?\s+)?href=(["'])(.*?)\1/)).split(',')[2];

		if (contentPath == undefined || contentPath == null) {
			value = richText;
			return richText;
		}

		if (String(contentPath).startsWith("/content")) {
			try {
				var externalizedLink = linkUtil.getExternalizedUrl(contentPath, resolver);

				try {
					externalizedLink = decodeURI(externalizedLink)
				} catch (e) {
					externalizedLink = externalizedLink;
				}

				richText = String(richText).replace('"'+contentPath, '"'+externalizedLink);

				if (String(richText).lastIndexOf(externalizedLink)+String(externalizedLink).length+1 >= String(richText).length - 1) {
					value = richText;
					return richText;
				}
				else {
					externalizeDamPath(richText, String(richText).lastIndexOf(externalizedLink)+String(externalizedLink).length+1, String(richText).length - 1);
				}
			}
			catch (error) {
				log.error('The externalizer has occured an error : ' + error);
			}
		}
		else {
			if (String(richText).lastIndexOf(contentPath) >= String(richText).length - 1) {
				value = richText;
				return richText;
			}
			else {
				externalizeDamPath(richText, String(richText).lastIndexOf(contentPath)+String(contentPath).length+1, String(richText).length - 1);
			}
		}
	}

	richText = externalizeDamPath(String(richText), 0, String(richText).length - 1);

	return {
		value: value,
	};
});
