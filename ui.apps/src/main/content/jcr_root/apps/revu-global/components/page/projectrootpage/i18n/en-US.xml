<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:mix="http://www.jcp.org/jcr/mix/1.0"
    jcr:language="en"
    jcr:mixinTypes="[mix:language]"
    jcr:primaryType="sling:Folder">
    <revu.global.templates.projectrootpage.dialog.color.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Color Config" />
    <revu.global.templates.projectrootpage.dialog.color.breadcrumb.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Breadcrumb" />
    <revu.global.templates.projectrootpage.dialog.color.text.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Text" />
    <revu.global.templates.projectrootpage.dialog.color.texthover.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Text-Hover" />
    <revu.global.templates.projectrootpage.dialog.color.textactive.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Text-Active" />
    <revu.global.templates.projectrootpage.dialog.color.arrow.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Arrow" />
    <revu.global.templates.projectrootpage.dialog.color.teaser.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Teaser" />
    <revu.global.templates.projectrootpage.dialog.color.tiles.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Tiles" />
    <revu.global.templates.projectrootpage.dialog.color.variation.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Variation-Name" />
    <revu.global.templates.projectrootpage.dialog.color.background.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Background" />
    <revu.global.templates.projectrootpage.dialog.color.headline.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Title" />
    <revu.global.templates.projectrootpage.dialog.color.subtitle.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Subtitle" />
    <revu.global.templates.projectrootpage.dialog.color.line.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Line" />
    <revu.global.templates.projectrootpage.dialog.tab.color.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Color" />
    <revu.global.templates.projectrootpage.dialog.tab.color.variation
        jcr:primaryType="sling:MessageEntry"
        sling:message="Color-Variation" />
    <revu.global.templates.projectrootpage.dialog.color.cookiedisclaimer.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Cookie Disclaimer" />
    <revu.global.templates.projectrootpage.dialog.color.link.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Link" />
    <revu.global.templates.projectrootpage.dialog.color.linkhover.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Link-Hover" />
    <revu.global.templates.projectrootpage.dialog.color.button.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Button" />
    <revu.global.templates.projectrootpage.dialog.color.outline.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Outline" />
    <revu.global.templates.projectrootpage.dialog.color.icon.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Icon" />
    <revu.global.templates.projectrootpage.dialog.color.gradient.label
            jcr:primaryType="sling:MessageEntry"
            sling:message="Gradient Color" />
    <revu.global.templates.projectrootpage.dialog.color.background.hover.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Background-Hover" />
    <revu.global.templates.projectrootpage.dialog.color.outline.hover.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Outline-Hover" />
    <revu.global.templates.projectrootpage.dialog.color.icon.hover.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Icon-Hover" />
    <revu.global.templates.projectrootpage.dialog.tab.color.button.variation.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Button-Variation" />
    <revu.global.templates.projectrootpage.dialog.color.link.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Link" />
    <revu.global.templates.projectrootpage.dialog.color.footer.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Footer" />
    <revu.global.templates.projectrootpage.dialog.color.footercta.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="CTA" />
    <revu.global.templates.projectrootpage.dialog.color.footernavi.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Navigation" />
    <revu.global.templates.projectrootpage.dialog.color.footermeta.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Meta-Navigation" />
    <revu.global.templates.projectrootpage.dialog.color.hseperator.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="H-Seperator" />
    <revu.global.templates.projectrootpage.dialog.color.vseperator.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="V-Seperator" />
    <revu.global.templates.projectrootpage.dialog.color.chatbutton.background.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="ChatButton-Background" />
    <revu.global.templates.projectrootpage.dialog.color.chatbutton.on.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="ChatButton-On" />
    <revu.global.templates.projectrootpage.dialog.color.chatbutton.off.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="ChatButton-Off" />
    <revu.global.templates.projectrootpage.dialog.color.chatbutton.text.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="ChatButton-Text" />
    <revu.global.templates.projectrootpage.dialog.color.seperator.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Seperator" />
    <revu.global.templates.projectrootpage.dialog.tab.color.link.variation.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Link-Variation" />
    <revu.global.templates.projectrootpage.dialog.color.iconteaser.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Icon-Teaser" />
    <revu.global.templates.projectrootpage.dialog.color.teaser.global.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Teaser-Global" />
    <revu.global.templates.projectrootpage.dialog.color.teaser.globalv2.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Teaser-Global V2" />
    <revu.global.templates.projectrootpage.dialog.color.newsteaser.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="News-Teaser" />
    <revu.global.templates.projectrootpage.dialog.color.date.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Date" />
    <revu.global.templates.projectrootpage.dialog.color.newsteaser.link.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Link (all news)" />
    <revu.global.templates.projectrootpage.dialog.color.videoicon.bg.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="VideoIcon-Background" />
    <revu.global.templates.projectrootpage.dialog.color.videoicon.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Video-Icon" />
    <revu.global.templates.projectrootpage.dialog.color.expandables.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Expandables" />
    <revu.global.templates.projectrootpage.dialog.color.expandable.item.title.closed.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Item title (closed)" />
    <revu.global.templates.projectrootpage.dialog.color.expandable.item.title.open.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Item title (open)" />
    <revu.global.templates.projectrootpage.dialog.color.expandable.separator.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Separator" />
    <revu.global.templates.projectrootpage.dialog.color.header.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Header" />
    <revu.global.templates.projectrootpage.dialog.color.headermeta.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Meta-Level" />
    <revu.global.templates.projectrootpage.dialog.color.portalbutton.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Portal-Button" />
    <revu.global.templates.projectrootpage.dialog.color.navigation.large.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Navigation-LG" />
    <revu.global.templates.projectrootpage.dialog.color.navigation.medium.small.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Navigation-(MD-XS)" />
    <revu.global.templates.projectrootpage.dialog.color.background.active.hover.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Background-(Active/Hover)" />
    <revu.global.templates.projectrootpage.dialog.color.icon.active.hover.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Icon-(Active/Hover)" />
    <revu.global.templates.projectrootpage.dialog.color.text.active.hover.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Text-(Active/Hover)" />
    <revu.global.templates.projectrootpage.dialog.color.navi.level1.item.background.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Level1-Background" />
    <revu.global.templates.projectrootpage.dialog.color.navi.level1.item.text.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Level1-Text" />
    <revu.global.templates.projectrootpage.dialog.color.navi.level1.item.text.active.hover.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Level1-Text(Active/Hover)" />
    <revu.global.templates.projectrootpage.dialog.color.navi.level1.item.bottomline.active.hover.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Level1-BottomLine(Active/Hover)" />
    <revu.global.templates.projectrootpage.dialog.color.navi.flyout.background.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Flyout-Backgrund" />
    <revu.global.templates.projectrootpage.dialog.color.navi.level2.item.text.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Level2-Text" />
    <revu.global.templates.projectrootpage.dialog.color.navi.level2.item.text.active.hover.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Level2-Text(Active/Hover)" />
    <revu.global.templates.projectrootpage.dialog.color.navi.level3.item.text.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Level3-Text" />
    <revu.global.templates.projectrootpage.dialog.color.navi.level3.item.text.active.hover.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Level3-Text(Active/Hover)" />
    <revu.global.templates.projectrootpage.dialog.color.navi.teaser.title.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Teaser-Title" />
    <revu.global.templates.projectrootpage.dialog.color.navi.teaser.text.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Teaser-Text" />
    <revu.global.templates.projectrootpage.dialog.tab.color.teaser.link.variation.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Teaser-Link" />
    <revu.global.templates.projectrootpage.dialog.tab.color.doorpage.link.variation.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Doorpage-Link" />
    <revu.global.templates.projectrootpage.dialog.tab.color.faq.link.variation.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="FAQ-Link" />
    <revu.global.templates.projectrootpage.dialog.color.navi.flyout.text.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Flyout-Text" />
    <revu.global.templates.projectrootpage.dialog.color.navi.flyout.icon.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Flyout-Icon" />
	<revu.global.templates.projectrootpage.dialog.color.metanavi.flyout.background.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="MetaNav-Flyout-Backgrund"/>
    <revu.global.templates.projectrootpage.dialog.color.metanavi.flyout.text.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="MetaNav-Flyout-Text"/>
    <revu.global.templates.projectrootpage.dialog.tab.color.flyout.link.variation.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Flyout-Links" />
    <revu.global.templates.projectrootpage.dialog.color.navi.horizontal.vertical.line.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="H&amp;V-Lines" />
    <revu.global.templates.projectrootpage.dialog.color.navi.horizontal.vertical.transparent.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="H&amp;V-Transparent" />
    <revu.global.templates.projectrootpage.dialog.color.navi.hline.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="H-Line" />
    <revu.global.templates.projectrootpage.dialog.color.navi.vline.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="V-Line" />
       <revu.global.templates.projectrootpage.dialog.color.sliderstage.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Slider Stage" />
    <revu.global.templates.projectrootpage.dialog.color.sliderstage.variation
        jcr:primaryType="sling:MessageEntry"
        sling:message="Variation" />
    <revu.global.templates.projectrootpage.dialog.color.sliderstage.bg.active
        jcr:primaryType="sling:MessageEntry"
        sling:message="Background active" />
    <revu.global.templates.projectrootpage.dialog.color.sliderstage.bg.active.loader
        jcr:primaryType="sling:MessageEntry"
        sling:message="Background active Loader" />
    <revu.global.templates.projectrootpage.dialog.color.sliderstage.bg.active.text
        jcr:primaryType="sling:MessageEntry"
        sling:message="Background active Text" />
    <revu.global.templates.projectrootpage.dialog.color.sliderstage.bg.active.button
        jcr:primaryType="sling:MessageEntry"
        sling:message="Background active Button" />
    <revu.global.templates.projectrootpage.dialog.color.download.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Download Links" />
    <revu.global.templates.projectrootpage.dialog.color.title.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Title" />
    <revu.global.templates.projectrootpage.dialog.color.titleline.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Title Line" />
    <revu.global.templates.projectrootpage.dialog.color.separator.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Separator" />
    <revu.global.templates.projectrootpage.dialog.color.staticstage.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Static Stage" />
    <revu.global.templates.projectrootpage.dialog.color.staticstagev2.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Static Stage V2" />
    <revu.global.templates.projectrootpage.dialog.color.contentslider.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Slider Content" />
    <revu.global.templates.projectrootpage.dialog.color.contentslider.countnumbers
        jcr:primaryType="sling:MessageEntry"
        sling:message="Number of slides" />
    <revu.global.templates.projectrootpage.dialog.color.contentslider.countnumbers.active
        jcr:primaryType="sling:MessageEntry"
        sling:message="Number of active slide" />
    <revu.global.templates.projectrootpage.dialog.color.contentslider.controls
        jcr:primaryType="sling:MessageEntry"
        sling:message="Prev, Next controls" />
       <revu.global.templates.projectrootpage.dialog.color.global.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Global" />
    <revu.global.templates.projectrootpage.dialog.color.global.h1line.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="H1-Line" />
    <revu.global.templates.projectrootpage.dialog.color.global.h1topline.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="H1-TopLine" />
    <revu.global.templates.projectrootpage.dialog.color.listicons.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="List-Icons" />
    <revu.global.templates.projectrootpage.dialog.color.global.links.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Links" />
    <revu.global.templates.projectrootpage.dialog.color.image.text.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Image-Text (Copyright)" />
    <revu.global.templates.projectrootpage.dialog.color.global.pagination.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Pagination" />
    <revu.global.templates.projectrootpage.dialog.color.global.pagination.numbers.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Numbers" />
       <revu.global.templates.projectrootpage.dialog.color.global.pagination.numbers.active.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Numbers-Active" />
       <revu.global.templates.projectrootpage.dialog.color.global.pagination.numbers.hover.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Numbers-Hover" />
       <revu.global.templates.projectrootpage.dialog.color.global.pagination.icon.arrows.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Icon-Arrows" />
    <revu.global.templates.projectrootpage.dialog.youtubeurl.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="YouTube URL" />
    <revu.global.templates.projectrootpage.dialog.color.verticaltileteaser.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Vertical-Teaser" />
    <revu.global.templates.projectrootpage.dialog.color.iconimagetileteaser.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Icon/Image-Teaser" />
    <revu.global.templates.projectrootpage.dialog.color.iconimagetileteaserv2.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Icon/Image-Teaser V2" />
    <revu.global.templates.projectrootpage.dialog.color.imagevideo.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Image/Video" />
    <revu.global.templates.projectrootpage.dialog.color.line.active.hover.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Line-(Active/Hover)" />
    <revu.global.templates.projectrootpage.dialog.color.tabs.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Tabs" />
    <revu.global.templates.projectrootpage.dialog.color.sitemap.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Sitemap" />
    <revu.global.templates.projectrootpage.dialog.sitemap.color.firstlevellink.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Level 1 link" />
    <revu.global.templates.projectrootpage.dialog.sitemap.color.otherlevellink.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Level 2-4 link" />
    <revu.global.templates.projectrootpage.dialog.sitemap.color.togglelevel4link.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Level 4 toggle link" />
    <revu.global.templates.projectrootpage.dialog.color.relatedcontentteaser.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Related content" />
    <revu.global.templates.projectrootpage.dialog.color.global.errorpage.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Error Page" />
    <revu.global.templates.projectrootpage.dialog.color.global.errorpage.numbers.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Numbers" />
    <revu.global.templates.projectrootpage.dialog.color.global.icon.link.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Icon-Link" />
    <revu.global.templates.projectrootpage.dialog.color.global.icon.link.hover.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Icon-Link Hover" />
    <revu.global.templates.projectrootpage.dialog.color.global.seperator.line.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Seperator-Line" />
    <revu.global.templates.projectrootpage.dialog.color.teaserlist.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Teaser List" />
    <revu.global.templates.projectrootpage.dialog.color.titlehover.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Title Hover" />
    <revu.global.templates.projectrootpage.dialog.color.imagegallery.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Image gallery" />
    <revu.global.templates.projectrootpage.dialog.color.closeicon.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Close icon" />
    <revu.global.templates.projectrootpage.dialog.color.downloadlink.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Download link" />
    <revu.global.templates.projectrootpage.dialog.color.progressbar.background.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Progressbar-Background" />
    <revu.global.templates.projectrootpage.dialog.color.progressbar.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Progressbar" />
    <revu.global.templates.projectrootpage.dialog.color.shareicon.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Share icon" />
    <revu.global.templates.projectrootpage.dialog.color.shareicon.hover.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Share icon (hover)" />
    <revu.global.templates.projectrootpage.dialog.color.teasercountlabel.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Teaser label (+count)" />
    <revu.global.templates.projectrootpage.dialog.color.background.shadow.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Background shadow" />
    <revu.global.templates.projectrootpage.dialog.color.tooltip.background.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Tooltip background" />
    <revu.global.templates.projectrootpage.dialog.color.tooltip.text.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Tooltip text" />
    <revu.global.templates.projectrootpage.dialog.color.sidebarcontactteaser.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Sidebar Contact Teaser (chatbutton)" />
    <revu.global.templates.projectrootpage.dialog.color.form.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Form Config" />
    <revu.global.templates.projectrootpage.dialog.color.formhint.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Form Hint" />
    <revu.global.templates.projectrootpage.dialog.color.formhinterror.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Form Hint Error" />
    <revu.global.templates.projectrootpage.dialog.color.forminput.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Form Input" />
    <revu.global.templates.projectrootpage.dialog.color.formdropdown.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Form Dropdown" />
    <revu.global.templates.projectrootpage.dialog.color.formcheckbox.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Form Checkbox" />
    <revu.global.templates.projectrootpage.dialog.color.formdatepicker.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Form Datepicker" />
    <revu.global.templates.projectrootpage.dialog.color.formtextarea.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Form TextArea" />
    <revu.global.templates.projectrootpage.dialog.color.formswitches.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Form Switches" />
    <revu.global.templates.projectrootpage.dialog.color.formradio.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Form Radio" />
    <revu.global.templates.projectrootpage.dialog.color.formspopuphint.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Form Popup Hint" />
    <revu.global.templates.projectrootpage.dialog.color.formsareahint.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Form Area Hint" />
    <revu.global.templates.projectrootpage.dialog.color.formloader.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Form Loader" />
    <revu.global.templates.projectrootpage.dialog.color.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Label" />
    <revu.global.templates.projectrootpage.dialog.color.starcolor.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Mandatory Star" />
    <revu.global.templates.projectrootpage.dialog.color.reqtext.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Required Text" />
    <revu.global.templates.projectrootpage.dialog.color.valtext.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Value Text" />
    <revu.global.templates.projectrootpage.dialog.color.valtextdis.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Disabled Value Text" />
    <revu.global.templates.projectrootpage.dialog.color.bottomline.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Bottom line" />
    <revu.global.templates.projectrootpage.dialog.color.bottomlinefoc.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Focused Bottom Line" />
    <revu.global.templates.projectrootpage.dialog.color.tickicon.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Check Icon" />
    <revu.global.templates.projectrootpage.dialog.color.crossicon.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Error Icon" />
    <revu.global.templates.projectrootpage.dialog.color.bottomlineerr.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Error Bottom Line" />
    <revu.global.templates.projectrootpage.dialog.color.entry.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Entry" />
    <revu.global.templates.projectrootpage.dialog.color.entryactive.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Active Entry" />
    <revu.global.templates.projectrootpage.dialog.color.entryseparator.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Entry Separator" />
    <revu.global.templates.projectrootpage.dialog.color.openyeartxt.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Open Year Text" />
    <revu.global.templates.projectrootpage.dialog.color.openmonthtxt.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Open Month Text" />
    <revu.global.templates.projectrootpage.dialog.color.openyeararrow.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Open Year Arrow" />
    <revu.global.templates.projectrootpage.dialog.color.openweekdaystxt.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Open Weekdaays Text" />
    <revu.global.templates.projectrootpage.dialog.color.opendaystxt.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Open Days Text" />
    <revu.global.templates.projectrootpage.dialog.color.opendaystxtdis.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Open Days Disabled" />
    <revu.global.templates.projectrootpage.dialog.color.opendaysactsetxt.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Open Days Active" />
    <revu.global.templates.projectrootpage.dialog.color.opendaysactsebg.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Open Days Active Background" />
    <revu.global.templates.projectrootpage.dialog.color.opendaysactperiodtxt.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Open Days Active Period Text" />
    <revu.global.templates.projectrootpage.dialog.color.opendaysactperiodbg.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Open Days Active Period Background" />
    <revu.global.templates.projectrootpage.dialog.color.calendarLines.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Calendar Lines" />
    <revu.global.templates.projectrootpage.dialog.color.calendarLinesdis.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Disabled Calandar Lines" />
    <revu.global.templates.projectrootpage.dialog.color.labelicon
        jcr:primaryType="sling:MessageEntry"
        sling:message="Label Icon" />
    <revu.global.templates.projectrootpage.dialog.color.labelctive
        jcr:primaryType="sling:MessageEntry"
        sling:message="Active Label" />
    <revu.global.templates.projectrootpage.dialog.color.labelctiveicon
        jcr:primaryType="sling:MessageEntry"
        sling:message="Active Label Icon" />
    <revu.global.templates.projectrootpage.dialog.color.labeldisabled
        jcr:primaryType="sling:MessageEntry"
        sling:message="Disabled Label" />
    <revu.global.templates.projectrootpage.dialog.color.labelicondisabled
        jcr:primaryType="sling:MessageEntry"
        sling:message="Disabled Label Icon" />
    <revu.global.templates.projectrootpage.dialog.color.languageswitch
        jcr:primaryType="sling:MessageEntry"
        sling:message="Language Switch" />
    <revu.global.templates.projectrootpage.dialog.color.checkicon
        jcr:primaryType="sling:MessageEntry"
        sling:message="Check Icon" />
    <revu.global.templates.projectrootpage.dialog.color.checkiconActiive
        jcr:primaryType="sling:MessageEntry"
        sling:message="Active Check Icon" />
    <revu.global.templates.projectrootpage.dialog.color.switchoff.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Switch Off" />
    <revu.global.templates.projectrootpage.dialog.color.switchon.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Switch On" />
    <revu.global.templates.projectrootpage.dialog.color.stroke.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Stroke" />
    <revu.global.templates.projectrootpage.dialog.color.table.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Table" />
    <revu.global.templates.projectrootpage.dialog.color.table.columnheadlines.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Column headlines" />
    <revu.global.templates.projectrootpage.dialog.color.table.headerline.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Header line" />
    <revu.global.templates.projectrootpage.dialog.color.table.linebackground.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Row background (zebra stripe)" />
    <revu.global.templates.projectrootpage.dialog.color.career.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Career/Job search" />
    <revu.global.templates.projectrootpage.dialog.color.career.choosedtagtext.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Choosed tag text" />
    <revu.global.templates.projectrootpage.dialog.color.career.choosedtagcloseiconbg.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Choosed tag close icon background" />
    <revu.global.templates.projectrootpage.dialog.color.career.choosedtagcloseicon.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Choosed tag close icon" />
    <revu.global.templates.projectrootpage.dialog.color.career.tagtext.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Tag text" />
    <revu.global.templates.projectrootpage.dialog.color.career.tagbackground.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Tag background" />
    <revu.global.templates.projectrootpage.dialog.color.career.tagborder.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Tag border" />
    <revu.global.templates.projectrootpage.dialog.color.career.tagbackgroundactive.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Tag background active" />
    <revu.global.templates.projectrootpage.dialog.color.career.tagtextactive.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Tag text active" />
    <revu.global.templates.projectrootpage.dialog.color.career.tagborderactive.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Tag border active" />
    <revu.global.templates.projectrootpage.dialog.color.search.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Search" />
    <revu.global.templates.projectrootpage.dialog.color.search.header.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Header (Autocomplete)" />
    <revu.global.templates.projectrootpage.dialog.color.search.inputtext.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Input field text" />
    <revu.global.templates.projectrootpage.dialog.color.inputbottomline.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Input field bottom line" />
    <revu.global.templates.projectrootpage.dialog.color.search.inputbackground.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Input field background" />
    <revu.global.templates.projectrootpage.dialog.color.search.suggestionsbackground.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Suggestions background" />
    <revu.global.templates.projectrootpage.dialog.color.search.suggestionstext.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Suggestions text" />
    <revu.global.templates.projectrootpage.dialog.color.search.suggestionshoveractivebackground.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Suggestions hover/active background" />
    <revu.global.templates.projectrootpage.dialog.color.search.suggestionshoveractivetext.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Suggestions hover/active text" />
    <revu.global.templates.projectrootpage.dialog.color.search.suggestionsitembottomline.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Suggestions bottom border" />
    <revu.global.templates.projectrootpage.dialog.color.search.results.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Results page" />
    <revu.global.templates.projectrootpage.dialog.color.search.placeholdertext.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Placeholder text" />
    <revu.global.templates.projectrootpage.dialog.color.modal.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Modal" />
    <revu.global.templates.projectrootpage.dialog.color.closebutton.background.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Close button background" />
    <revu.global.templates.projectrootpage.dialog.color.closebutton.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Close button" />
    <revu.global.templates.projectrootpage.dialog.color.overlay.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Overlay" />
    <revu.global.templates.projectrootpage.loaderimage.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Loader Graphic (GIF)" />
    <revu.global.templates.projectrootpage.dialog.color.barchart.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Bar chart" />
    <revu.global.templates.projectrootpage.dialog.color.bar.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Bar" />
     <revu.global.templates.projectrootpage.dialog.secretKey
        jcr:primaryType="sling:MessageEntry"
        sling:message="Secret key" />
     <revu.global.templates.projectrootpage.dialog.secretKey.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="Secret key for google recaptcha" />
     <revu.global.templates.projectrootpage.dialog.siteKey
        jcr:primaryType="sling:MessageEntry"
        sling:message="Site key" />
     <revu.global.templates.projectrootpage.dialog.siteKey.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="Site key for google recaptcha" />
    <revu.global.templates.projectrootpage.dialog.iadvizeId
            jcr:primaryType="sling:MessageEntry"
            sling:message="Iadvize ID " />
     <revu.global.templates.projectrootpage.dialog.desastercommunication
     	jcr:primaryType="sling:MessageEntry"
        sling:message="Desaster Communication" />
    <revu.global.templates.projectrootpage.dialog.color.overlay.label
            jcr:primaryType="sling:MessageEntry"
            sling:message="Text Overlay" />
    <revu.global.templates.projectrootpage.dialog.consentManagement
            jcr:primaryType="sling:MessageEntry"
            sling:message="Consent Management" />
    <revu.global.templates.projectrootpage.dialog.consentmanagement.label
            jcr:primaryType="sling:MessageEntry"
            sling:message="Usercentrics ID" />
    <revu.global.templates.projectrootpage.dialog.color.expandable.title
            jcr:primaryType="sling:MessageEntry"
            sling:message="Expandables"/>
    <revu.global.templates.projectrootpage.dialog.color.expandable.headline.label
            jcr:primaryType="sling:MessageEntry"
            sling:message="Headline H1-H5"/>
    <revu.global.templates.projectrootpage.dialog.color.expandable.item.label
            jcr:primaryType="sling:MessageEntry"
            sling:message="Background"/>
    <revu.global.templates.projectrootpage.dialog.color.expandable.link.label
            jcr:primaryType="sling:MessageEntry"
            sling:message="Links"/>
    <revu.global.templates.projectrootpage.dialog.color.expandable.text.label
            jcr:primaryType="sling:MessageEntry"
            sling:message="Text"/>
    <revu.global.templates.projectrootpage.dialog.color.background.title
            jcr:primaryType="sling:MessageEntry"
            sling:message="Grid Background Color"/>
    <revu.global.templates.projectrootpage.dialog.bulletpointicon
            jcr:primaryType="sling:MessageEntry"
            sling:message="Bullet Point Icon Color"/>
    <revu.global.templates.projectrootpage.dialog.bulletpointicon.circle
            jcr:primaryType="sling:MessageEntry"
            sling:message="Circle color"/>
    <revu.global.templates.projectrootpage.dialog.bulletpointicon.icon
            jcr:primaryType="sling:MessageEntry"
            sling:message="Icon color"/>
    <revu.global.templates.projectrootpage.dialog.uctemplates.label
            jcr:primaryType="sling:MessageEntry"
            sling:message="Custom Data Processing Services"/>
    <revu.global.templates.projectrootpage.dialog.uctemplates.service.label
            jcr:primaryType="sling:MessageEntry"
            sling:message="Service"/>
    <revu.global.templates.projectrootpage.dialog.uctemplates.customeTemplateID.label
            jcr:primaryType="sling:MessageEntry"
            sling:message="Custom Template ID"/>
    <revu.global.templates.projectrootpage.dialog.ucpreview.label
            jcr:primaryType="sling:MessageEntry"
            sling:message="Use preview mode"/>
    <revu.global.templates.projectrootpage.dialog.color.disabled.background.label 
            jcr:primaryType="sling:MessageEntry"
            sling:message="Disabled Background (Read-Only)"/>
    <revu.global.templates.projectrootpage.dialog.omnichannelSrc 
            jcr:primaryType="sling:MessageEntry"
            sling:message="Src"/>
    <revu.global.templates.projectrootpage.dialog.omnichannelAppId
            jcr:primaryType="sling:MessageEntry"
            sling:message="App-ID"/>
    <revu.global.templates.projectrootpage.dialog.omnichannelOrgId 
            jcr:primaryType="sling:MessageEntry"
            sling:message="Org-ID"/>
    <revu.global.templates.projectrootpage.dialog.omnichannelOrgUrl 
            jcr:primaryType="sling:MessageEntry"
            sling:message="Org-Url"/>
    <revu.global.templates.projectrootpage.dialog.omnichannelChatColor
            jcr:primaryType="sling:MessageEntry"
            sling:message="Chat-color"/>
    <revu.global.templates.projectrootpage.omnichannelChatColor.description
            jcr:primaryType="sling:MessageEntry"
            sling:message="Please use hex color to adjust the chat's appearance. For example #0000ff"/>
    <revu.global.templates.projectrootpage.dialog.omnichannelFloatButton
            jcr:primaryType="sling:MessageEntry"
            sling:message="Floating Button"/>
    <revu.global.templates.projectrootpage.omnichannelFloatButton.description
            jcr:primaryType="sling:MessageEntry"
            sling:message="Please deactivate sidebar to avoid overlapping components if floating button is active"/>
    <revu.global.templates.projectrootpage.dialog.omnichannelFloatButton.active
            jcr:primaryType="sling:MessageEntry"
            sling:message="Active"/>    
    <revu.global.templates.projectrootpage.dialog.omnichannelFloatButton.inactive
            jcr:primaryType="sling:MessageEntry"
            sling:message="Inactive"/>
   	<revu.global.templates.projectrootpage.dialog.matomoUrl
            jcr:primaryType="sling:MessageEntry"
            sling:message="Matomo Tag Manager URL"/>                      
</jcr:root>
