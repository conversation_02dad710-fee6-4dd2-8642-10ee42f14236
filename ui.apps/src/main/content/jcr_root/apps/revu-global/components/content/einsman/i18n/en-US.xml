<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:mix="http://www.jcp.org/jcr/mix/1.0"
    jcr:language="en"
    jcr:mixinTypes="[mix:language]"
    jcr:primaryType="sling:Folder">
    <revu.global.components.einsman.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Einsman" />
    <revu.global.components.einsman.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="Einsman component" />
    <revu.global.components.einsman.dialog.webservicetab.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Webservice" />
    <revu.global.components.einsman.dialog.fields.webserviceurl.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Webservice url" />
    <revu.global.components.einsman.dialog.fields.type.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Type" />
    <revu.global.components.einsman.dialog.fields.networkoperator.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Networkoperator" />
    <revu.global.components.einsman.dialog.fields.exporturl.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Export webservice url" />
    <revu.global.components.einsman.dialog.fields.rssfeedurl.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="RSS feed webservice url" />
    <revu.global.components.einsman.dialog.generaltab.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="General Settings" />
    <revu.global.components.einsman.dialog.fields.tableheight.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Table height" />
    <revu.global.components.einsman.dialog.fields.tablewidth.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Table width" />
    <revu.global.components.einsman.dialog.fields.defaultsortby.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Sort by default value" />
    <revu.global.components.einsman.dialog.fields.defaultsortorder.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Sort order default value" />
    <revu.global.components.einsman.dialog.fields.shownavgridsearch.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Show search" />
    <revu.global.components.einsman.dialog.helptab.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Help" />
    <revu.global.components.einsman.dialog.fields.linkhelp.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Link to help page" />
    <revu.global.components.einsman.dialog.fields.linknotificationregistration.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Link to notification registration page" />
    <revu.global.components.einsman.dialog.fields.linknotificationunregistration.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Link to notification unregistration page" />
    <revu.global.components.einsman.monthdays.long
        jcr:primaryType="sling:MessageEntry"
        sling:message="January, February, March, April, May, June, July, August, September, October, November, December" />
    <revu.global.components.einsman.monthdays.short
        jcr:primaryType="sling:MessageEntry"
        sling:message="Jan, Feb, Mar, Apr, May, Jun, Jul, Aug, Sep, Oct, Nov, Dec" />
    <revu.global.components.einsman.weekdays.long
        jcr:primaryType="sling:MessageEntry"
        sling:message="Sunday, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday" />
    <revu.global.components.einsman.weekdays.short
        jcr:primaryType="sling:MessageEntry"
        sling:message="So, Mo, Tu, We, Th, Fr, Sa" />
    <revu.global.components.einsman.column.names.forecastid
        jcr:primaryType="sling:MessageEntry"
        sling:message="Forecast-ID" />
    <revu.global.components.einsman.column.names.start
        jcr:primaryType="sling:MessageEntry"
        sling:message="Start" />    
    <revu.global.components.einsman.column.names.end
        jcr:primaryType="sling:MessageEntry"
        sling:message="End" />
    <revu.global.components.einsman.column.names.location
        jcr:primaryType="sling:MessageEntry"
        sling:message="Location" />
    <revu.global.components.einsman.column.names.locationbottleneck
        jcr:primaryType="sling:MessageEntry"
        sling:message="Location bottleneck" />
    <revu.global.components.einsman.column.names.duration
        jcr:primaryType="sling:MessageEntry"
        sling:message="Duration" />
    <revu.global.components.einsman.column.names.controlstage
        jcr:primaryType="sling:MessageEntry"
        sling:message="Stufe" />
    <revu.global.components.einsman.column.names.inducingno
        jcr:primaryType="sling:MessageEntry"
        sling:message="Inducing No" />
    <revu.global.components.einsman.column.names.operationid
        jcr:primaryType="sling:MessageEntry"
        sling:message="Operation-ID" />
    <revu.global.components.einsman.column.names.dsokey
        jcr:primaryType="sling:MessageEntry"
        sling:message="DSO key" />
    <revu.global.components.einsman.column.names.requestor
        jcr:primaryType="sling:MessageEntry"
        sling:message="Requestor" />
    <revu.global.components.einsman.column.names.assetkey
        jcr:primaryType="sling:MessageEntry"
        sling:message="Anlagenschlüssel" />
    <revu.global.components.einsman.column.names.reason
        jcr:primaryType="sling:MessageEntry"
        sling:message="Reason" />
    <revu.global.components.einsman.column.names.liabilityofcompensation
   		jcr:primaryType="sling:MessageEntry"
        sling:message="Liability of compensation" />
    <revu.global.components.einsman.button.refresh
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Refresh" />
    <revu.global.components.einsman.button.firstpage
    	jcr:primaryType="sling:MessageEntry"
        sling:message="First page" />
    <revu.global.components.einsman.button.lastpage
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Last page" />
    <revu.global.components.einsman.numrecords
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Showing {from} - {to} of {total}" />
    <revu.global.components.einsman.dialog.notificationtab.title
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Notification" />
    <revu.global.components.einsman.dialog.fields.headlinenotification.label
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Headline" />
    <revu.global.components.einsman.dialog.fields.textnotificationregistration.label
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Text for registration" />
    <revu.global.components.einsman.dialog.fields.textnotificationunregistration.label
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Text for unregistration" />
    <revu.global.components.einsman.dialog.fields.buttonnotificationregistration.label
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Button label for registration" />
    <revu.global.components.einsman.dialog.fields.buttonnotificationunregistration.label
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Button label for unregistration" />    
    <revu.global.components.einsman.tooltip.title.id
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Fortlaufende Nummerierung der EinsMan-Maßnahmen" />
    <revu.global.components.einsman.tooltip.title.operationid
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Eindeutige Identifikationskennung für einen laufenden EinsMan-Einsatz. Sie wird im Format 'Kurzform des Netzbetreibers JJJJMMTT####' dargestellt." />
    <revu.global.components.einsman.tooltip.title.operationid.finished
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Eindeutige Identifikationskennung für einen abgeschlossenen EinsMan-Einsatz. Sie wird im Format 'Kurzform des Netzbetreibers JJJJMMTT####' dargestellt." />
    <revu.global.components.einsman.tooltip.title.start
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Zeitpunkt des Beginns des EinsMan-Einsatzes gemäß den Vorgaben der Netzsteuerung. Eine EinsMan-Maßnahme gilt als begonnen, wenn die Reduzierungsstufe ungleich 100 % vorgeben wird." />
    <revu.global.components.einsman.tooltip.title.end
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Zeitpunkt Abschlusses der EinsMan-Maßnahme gemäß Vorgaben der Netzsteuerung. Eine EinsMan-Maßnahme gilt als abgeschlossen, wenn die Reduzierungsstufe / Freigabe von 100 % wieder erreicht ist." />
    <revu.global.components.einsman.tooltip.title.duration
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Zeitlicher Umfang der Reduzierungsstufe als rechnerische Größe aus Start und Ende der jeweiligen Reduzierungsstufe." />
    <revu.global.components.einsman.tooltip.title.location
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Einzugsgebiet der Regelung (z. B. Gemeinde, Landkreis, Betriebsmittel)." />
    <revu.global.components.einsman.tooltip.title.locationbottleneck
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Ort des Engpasses oder das engpassbehaftete Betriebsmittel." />
    <revu.global.components.einsman.tooltip.title.reason
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Ursprünglichen Grund (z. B. Netzengpass) des EinsMan-Einsatzes." />
    <revu.global.components.einsman.tooltip.title.controlstage
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Leistungstechnischer Umfang der EinsMan-Maßnahme gemäß der Vorgabe der Netzsteuerung. Die Reduzierungsstufe 100 % wird nicht angezeigt." />
    <revu.global.components.einsman.tooltip.title.assetkey
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Ist eine 33 stellige Nummer, beginnend mit „E“, die jeder EEG-Anlage vom Netzbetreiber zugewiesen wird." />
    <revu.global.components.einsman.tooltip.title.requestor
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Originärer Verursacher (Netzbetreiber, überlagerter Netzbetreiber) der EinsMan-Maßnahme." />
    <revu.global.components.einsman.tooltip.title.inducingno
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Netzbetrteiber in dessen Netz der Einspeiser unmittelbar angeschlossen ist." />
    <revu.global.components.einsman.tooltip.title.dsokey
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Netzbetreiberspezifische Identifikationskennung in dessen Netz angeschlossene EEG-Anlage." />
    <revu.global.components.einsman.tooltip.title.liabilityofcompensation
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Kennzeichnet die EinsMan-Maßnahme als entschädigungspflichtig (= ja) oder als nicht entschädigungspflichtig (= nein)." />
    <revu.global.components.einsman.tooltip.title.exportlimit.100000
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Exportlimit von 100000 überschritten" />
    <revu.global.components.einsman.tooltip.title.exportlimit.5000
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Exportlimit von 5000 überschritten" />
    <revu.global.components.einsman.tooltip.title.exportlimit.200
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Exportlimit von 200 überschritten" />
    <revu.global.components.einsman.column.names.kalkid
    	jcr:primaryType="sling:MessageEntry"
        sling:message="Abrechnungs-ID" />
    <revu.global.components.contact.headlinesize
        jcr:primaryType="sling:MessageEntry"
        sling:key="revu.global.components.contact.headlinesize"
        sling:message="Headline Size"/>
    <functionTest
        jcr:primaryType="sling:MessageEntry"
        sling:message="function test"/>  
</jcr:root>
