<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:mix="http://www.jcp.org/jcr/mix/1.0"
	jcr:language="en"
	jcr:mixinTypes="[mix:language]"
	jcr:primaryType="sling:Folder">
	<revu.global.components.footerlivechat
		jcr:primaryType="sling:MessageEntry"
		sling:message="Footer livechat" />
	<revu.global.components.footerlivechat.headline
		jcr:primaryType="sling:MessageEntry"
		sling:message="Headline" />
	<revu.global.components.footerlivechat.text
		jcr:primaryType="sling:MessageEntry"
		sling:message="Text" />
	<revu.global.components.footerlivechat.infotext
			jcr:primaryType="sling:MessageEntry"
			sling:message="Text that is displayed if opt-in is needed" />
	<revu.global.components.footerlivechat.ucinfo
			jcr:primaryType="sling:MessageEntry"
			sling:message="Link label to open detailed information" />
	<revu.global.components.footerlivechat.ucaccept
			jcr:primaryType="sling:MessageEntry"
			sling:message="Link label to accept service" />
	<revu.global.components.footerlivechat.buttonlabel
		jcr:primaryType="sling:MessageEntry"
		sling:message="Button label" />
</jcr:root>
