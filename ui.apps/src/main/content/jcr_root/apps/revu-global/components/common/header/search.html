<div class="header-layer-search" data-sly-use.colortheme="/apps/revu-global/components/page/basepage/colortheme.js">
    <div class="container-fluid">
        <div class="container">
            <div class="row">
                <div class="col-md-12">  
                <div data-sly-use.model="com.eon.dist.dke.aem.core.models.SearchModel" class='react-mini-app' data-component='SearchAutocomplete' data-application-name='dke-search-autocomplete'
					data-props='{
	    				"apiUrl": "${model.apiUrl @context="uri"}",
	    				"redirectUrl": "${model.searchResultPage @context="uri"}",
	    				"numberOfSuggestions": ${properties.numberOfSuggestions || 6 @context="attribute"},
	    				"submitButtonLabel": "${properties.searchButtonLabel @context="attribute"}",
	    				"submitButtonVersion": "${properties.variationName @context="attribute"}",
	    				"autocompletePlaceholder": "${properties.searchDescription @context="attribute"}"
	  					}'>
	  				</div>
                </div>
            </div>
        </div>
    </div>
</div>