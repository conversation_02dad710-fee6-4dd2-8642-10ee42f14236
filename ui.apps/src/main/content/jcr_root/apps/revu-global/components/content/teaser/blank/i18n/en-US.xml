<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:mix="http://www.jcp.org/jcr/mix/1.0"
    jcr:language="en"
    jcr:mixinTypes="[mix:language]"
    jcr:primaryType="sling:Folder">    
    <revu.global.components.teaser.blank.title
            jcr:primaryType="sling:MessageEntry"            
            sling:message="Teaser Blank Placeholder"/>                                                                     
    <revu.global.components.teaser.blank.author.hint
            jcr:primaryType="sling:MessageEntry"            
            sling:message="This content is only visible in author mode, not on published page!"/>
</jcr:root>
