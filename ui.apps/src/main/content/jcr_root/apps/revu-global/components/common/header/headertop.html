<div class="header-wrapper-top ${properties.biggerLogo ? 'header-wrapper-big' : ''}" data-sly-use.lib="revu-global/components/common/includes/templateLib.html">
  <div class="logo ${properties.biggerLogo ? 'logo-big' : ''}" data-sly-test="${properties.logoPath}" itemscope itemtype="http://schema.org/Organization">
        <div data-sly-use.pagelink="${'com.eon.dist.dke.aem.core.util.UtilityMethods' @ passedUrl=properties.logoLink}" data-sly-unwrap></div>
    <a href="${pagelink.externalizedLink}" itemprop="url">
        <img src="${properties.logoPath}" alt="${properties.logoAltText}" title="${properties.logoTitle}" itemprop="logo">
        </a>
    <sly data-sly-test="${properties.logo2Path}" data-sly-use.pagelink="${'com.eon.dist.dke.aem.core.util.UtilityMethods' @ passedUrl=properties.logo2Link}">
      <a href="${pagelink.externalizedLink}" itemprop="url">
        <img src="${properties.logo2Path}" alt="${properties.logo2AltText}" title="${properties.logo2Title}" itemprop="logo">
      </a>
    </sly>
  </div>
  <sly data-sly-use.metanavigationmodel="${'com.eon.dist.dke.aem.core.models.OptionalNavigationModel'}">
    <sly data-sly-test = "${metanavigationmodel.optionalnavigationlevel == 4}">
       <div class="header-nav-links">
          <div class="header-navigation-levl1">
            <ul class="header-navigation-levl1__list" data-sly-list.level1="${metanavigationmodel.optionalheadertop}">
	          <li class="header-navigation__item">
		         <a class = "${level1.item.isCurrentLink ? 'actv' : ''}" data-sly-attribute.href="${level1.item.link}" title="${level1.item.title}">${level1.item.title}</a>
		         <i class="icon-plus visible-xs visible-sm visible-md pull-right"></i>
              </li>
            </ul>
          </div>
       </div>
    </sly>
 </sly>
  <div class="header-meta" data-role="headermeta">
    <a class="hamburger-menu hidden-lg" href="#" data-sly-test="${!pageProperties['hideNav']}">
        <i><svg width="18" height="18" viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg"><title>7D7EBCE9-F1EA-4CEB-AD9F-E13DEDFABA97</title><g fill="#767676" fill-rule="evenodd"><path d="M0 2h18v2H0zM0 8h18v2H0zM0 14h18v2H0z"/></g></svg></i>
            <i class="icomoon-close"></i>
            <span>${'revu.global.components.common.header.menu' @i18n}</span>
        </a>
    <ul class="header-meta__list">
      <li class="header-meta__item hidden-xs hidden-sm " data-sly-test="${properties.extraItemPath}" data-sly-use.pagelink="${'com.eon.dist.dke.aem.core.util.UtilityMethods' @ passedUrl=properties.extraItemPath}">
                <a href="${pagelink.externalizedLink}" tabindex="0" title="${properties.extraItemLabel}" target="${properties.extraItemTarget ? '_blank': ''}">
                    <i>
                      <sly data-sly-call="${lib.inlinesvg @ assetPath=properties.extraItemIcon, title=properties.extraItemLabel, width='18', height='18'}"></sly>
                    </i>
                    <span>${properties.extraItemLabel}</span>
                </a>
            </li>
      <sly data-sly-test="${properties.contactLabel}">        
      <li class="header-meta__item">
        <a class="contact-link" title="${properties.contactLabel}" data-target=".header-layer-wrapper">
          <i>
                   <sly data-sly-call="${lib.inlinesvg @ assetPath=properties.contactIcon, width='18', height='18'}"></sly>
          </i>
          <i class="icomoon-close"></i>
          <span>${properties.contactLabel}</span>
        </a>
      </li>
      </sly>
      <sly data-sly-test="${properties.searchLabel}">  
      <li class="header-meta__item">
        <a class="search-link" title="${properties.searchLabel}" data-target=".header-layer-wrapper">
          <i>
                       <sly data-sly-call="${lib.inlinesvg @ assetPath=properties.searchIcon, width='18', height='18'}"></sly>
                  </i>
                  <i class="icomoon-close"></i>
                  <span>${properties.searchLabel}</span>
        </a>
      </li>
		</sly>
      <sly data-sly-use.info="com.eon.dist.dke.aem.core.models.FlyoutModel" data-sly-unwrap>

        <sly data-sly-test="${!info.getFlyoutFlagData['overRideHomePageValues']}">
          <sly data-sly-test="${!properties.isFlyoutRequired}">
            <li class="header-meta__item header-meta__item--last" data-sly-test="${!properties.disableCustomerPortal && properties.customerPortalPath}">
              <sly data-sly-use.pagelink="${'com.eon.dist.dke.aem.core.util.UtilityMethods' @ passedUrl=properties.customerPortalPath}" data-sly-unwrap></sly>
              <a class="portal-link" tabindex="0" href="${pagelink.externalizedLink}" title="${properties.customerPortalLabel}" target="${properties.customerPortalTarget ? '_blank': ''}">
                <i>
                  <sly data-sly-call="${lib.inlinesvg @ assetPath=properties.customerPortalIcon, title=properties.customerPortalLabel, width='36', height='36'}"></sly>
                </i>
                <span>${properties.customerPortalLabel}</span>
              </a>
            </li>
          </sly>
        </sly>

        <sly data-sly-test="${info.getFlyoutFlagData['overRideHomePageValues']}">
          <sly data-sly-test="${!info.getFlyoutFlagData['isFlyoutRequired']}">
            <li class="header-meta__item header-meta__item--last" data-sly-test="${!info.getFlyoutFlagData['disableCustomerPortal'] && info.getFlyoutFlagData['customerPortalPath']}">
              <sly data-sly-use.pagelink="${'com.eon.dist.dke.aem.core.util.UtilityMethods' @ passedUrl=info.getFlyoutFlagData['customerPortalPath']}" data-sly-unwrap></sly>
                <a class="portal-link" tabindex="0" href="${pagelink.externalizedLink}" title="${info.getFlyoutFlagData['customerPortalLabel']}" target="${info.getFlyoutFlagData['customerPortalTarget'] ? '_blank': ''}">
                  <i>
                    <sly data-sly-call="${lib.inlinesvg @ assetPath=info.getFlyoutFlagData['customerPortalIcon'], title=info.getFlyoutFlagData['customerPortalLabel'], width='36', height='36'}"></sly>
                  </i>
                  <span>${info.getFlyoutFlagData['customerPortalLabel']}</span>
                </a>
            </li>
          </sly>
        </sly>

        <sly data-sly-test="${!info.getFlyoutFlagData['overRideHomePageValues']}">
            <sly data-sly-test="${properties.isFlyoutRequired}">
              <li class="header-meta__item header-meta__item--last" data-sly-test="${!properties.disableCustomerPortal}">
                <a class="portal-link csc-portal-link" tabindex="0" href="${info.getFlyoutFlagData['flyoutFallbackUrl']}">
                  <i>
                    <svg width="36" height="36" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg">
                      <title>${properties.customerPortalLabel}</title>
                      <path
                            d="M18 35C8.626 35 1 27.376 1 18 1 8.628 8.626 1 18 1c9.374 0 17 7.627 17 17 0 9.375-7.626 17-17 17m0-35C8.075 0 0 8.077 0 18c0 9.926 8.075 18 18 18s18-8.074 18-18c0-9.924-8.075-18-18-18m-4 13.01c0-2.205 1.794-4 4-4s4 1.795 4 4a4.001 4.001 0 0 1-3.94 3.994c-.02 0-.039-.003-.06-.003-.021 0-.04.003-.06.003A4.001 4.001 0 0 1 14 13.01m6.418 4.349c1.531-.855 2.582-2.473 2.582-4.35 0-2.756-2.243-5-5-5s-5 2.244-5 5c0 1.877 1.051 3.495 2.582 4.35C11.791 18.478 9 22.147 9 26.5a.5.5 0 0 0 1 0c0-4.665 3.557-8.462 7.94-8.497.021 0 .**************.021 0 .039-.006.06-.006 4.383.035 7.94 3.832 7.94 8.497a.5.5 0 0 0 1 0c0-4.354-2.791-8.023-6.582-9.142"
                                          fill="#000" fill-rule="evenodd"></path>
                    </svg>
                  </i>
                  <span>${properties.customerPortalLabel}</span>
                </a>
              </li>
            </sly>
        </sly>

        <sly data-sly-test="${info.getFlyoutFlagData['overRideHomePageValues']}">

            <sly data-sly-test="${info.getFlyoutFlagData['isFlyoutRequired']}">
              <li class="header-meta__item header-meta__item--last" data-sly-test="${!info.getFlyoutFlagData['disableCustomerPortal']}">
                <a class="portal-link csc-portal-link" tabindex="0" href="${info.getFlyoutFlagData['flyoutFallbackUrl']}">
                  <i>
                    <svg width="36" height="36" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg">
                      <title>${info.getFlyoutFlagData['customerPortalLabel']}</title>
                      <path
                          d="M18 35C8.626 35 1 27.376 1 18 1 8.628 8.626 1 18 1c9.374 0 17 7.627 17 17 0 9.375-7.626 17-17 17m0-35C8.075 0 0 8.077 0 18c0 9.926 8.075 18 18 18s18-8.074 18-18c0-9.924-8.075-18-18-18m-4 13.01c0-2.205 1.794-4 4-4s4 1.795 4 4a4.001 4.001 0 0 1-3.94 3.994c-.02 0-.039-.003-.06-.003-.021 0-.04.003-.06.003A4.001 4.001 0 0 1 14 13.01m6.418 4.349c1.531-.855 2.582-2.473 2.582-4.35 0-2.756-2.243-5-5-5s-5 2.244-5 5c0 1.877 1.051 3.495 2.582 4.35C11.791 18.478 9 22.147 9 26.5a.5.5 0 0 0 1 0c0-4.665 3.557-8.462 7.94-8.497.021 0 .**************.021 0 .039-.006.06-.006 4.383.035 7.94 3.832 7.94 8.497a.5.5 0 0 0 1 0c0-4.354-2.791-8.023-6.582-9.142"
                                        fill="#000" fill-rule="evenodd"></path>
                    </svg>
                  </i>
                  <span>${info.getFlyoutFlagData['customerPortalLabel']}</span>
                </a>
              </li>
            </sly>

        </sly>

      </sly>
    </ul>
  </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function () {
  const headerWrapperBottom = document.querySelector('.header-wrapper-bottom');
  const hamburgerMenu = document.querySelector('.hamburger-menu.hidden-lg');
  let prevDisplay = getComputedStyle(headerWrapperBottom).display;

  const observer = new MutationObserver(() => {
    const currentDisplay = getComputedStyle(headerWrapperBottom).display;

    if (prevDisplay !== currentDisplay) {
      if (currentDisplay === "none") {
        hamburgerMenu?.classList.remove("actv");
      }
      prevDisplay = currentDisplay;
    }
  });

  observer.observe(headerWrapperBottom, {
    attributes: true,
    attributeFilter: ['style'],
  });
});
</script>