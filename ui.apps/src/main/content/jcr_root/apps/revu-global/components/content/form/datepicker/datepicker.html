<sly data-sly-use.randomNumber="/apps/revu-global/components/common/includes/randomNumber.js"/>
<div class="form-group dyn_datepikr">
    <div class="inputfield-overlay__container">
        <div>
            <div class="form-input form-input--label ${properties.required ? 'form-input--mandatory' : ''}">
            <input class="form-input__field form-input__field-icon dyn_datepikr_fld" id="${properties.id ? properties.id : randomNumber.number}" type="daypicker" name="${properties.name}" maxlength="${properties.maxLength}"
                           disabled="${properties.disabled}" data-min="${properties.minvalue}" data-max="${properties.maxvalue}" required="${properties.required}"
                           data-lang="${currentPage.language.language}"  data-value="${properties.value}" autocomplete="off" />


            <label class="form-input__label" for="${properties.id ? properties.id : randomNumber.number}">
                    <span class="form-input--label--content">${properties.jcr:title}</span>
                    <span data-sly-test="${properties.required}" class="form-input--required-asterisk form-input__field__required-notifier--active">*</span></label>
                    <span class="form-input__bar"></span></div>


            <div data-sly-test="${properties.requiredMessage}" class="rqerr form-input__message-error"><p>${properties.requiredMessage}</p></div>

            <div data-sly-test="${properties.constraintMessage}" class="constrainterr form-input__message-error"><p>${properties.constraintMessage}</p></div>    

        </div>
    </div>
     <div data-sly-test="${properties.showinfo}" class="form-input__info-icon" id="${randomNumber.number}">
					 <svg xmlns="http://www.w3.org/2000/svg" class="form-input__info-icon--${properties.variationName}" width=30 height=30 viewBox="0 0 54 54">
						<path d="M27,16.19a1.75,1.75,0,1,0,0,3.46,1.75,1.75,0,1,0,0-3.46Z"/>
						<polygon class="form-input__info-icon--${properties.variationName}" points="25.54 37.81 28.46 37.81 28.46 22.15 25.54 22.69 25.54 37.81" />
						<path class="form-input__info-icon--${properties.variationName}" d="M27,9.2A17.8,17.8,0,1,0,44.8,27,17.81,17.81,0,0,0,27,9.2Zm0,33.68A15.88,15.88,0,1,1,42.88,27,15.89,15.89,0,0,1,27,42.88Z" />
					 </svg>
				  </div>
</div>

 <div data-sly-test="${properties.showinfo}" class="form-input__info-wrapper">
	<div class="form-input__info-content form-input__info-content_bg--${properties.variationName} form-input__info-content_tclr--${properties.variationName}" id="${randomNumber.number}">
		${properties.information}
	</div>
 </div>