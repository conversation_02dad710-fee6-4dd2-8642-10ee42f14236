<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:mix="http://www.jcp.org/jcr/mix/1.0"
    jcr:language="de"
    jcr:mixinTypes="[mix:language]"
    jcr:primaryType="sling:Folder">
    <revu.global.components.common.header.autocompleteplaceholder
        jcr:primaryType="sling:MessageEntry"
        sling:message="Autocomplete Platzhalter"/>
    <revu.global.components.common.header.biggerlogo
        jcr:primaryType="sling:MessageEntry"
        sling:message="Großes Logo"/>
    <revu.global.components.common.header.checkboxlanguagelabel
        jcr:primaryType="sling:MessageEntry"
        sling:message="Language Switch erforderlich"/>
    <revu.global.components.common.header.contactlist
        jcr:primaryType="sling:MessageEntry"
        sling:message="Kontaktliste"/>
    <revu.global.components.common.header.contactlist.linkdefault
        jcr:primaryType="sling:MessageEntry"
        sling:message="Link"/>
    <revu.global.components.common.header.contactlist.linkicon
        jcr:primaryType="sling:MessageEntry"
        sling:message="Icon"/>
    <revu.global.components.common.header.contactlist.linktext
        jcr:primaryType="sling:MessageEntry"
        sling:message="Texte"/>
    <revu.global.components.common.header.contactlist.maximum.limit.message
        jcr:primaryType="sling:MessageEntry"
        sling:message="Sie dürfen nur 6 Kontaktkanäle hinzufügen."/>
    <revu.global.components.common.header.contactlist.maximum.limit.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Maximale Grenze erreicht"/>
    <revu.global.components.common.header.contactlist.minimum.limit.message
        jcr:primaryType="sling:MessageEntry"
        sling:message="Sie müssen mindestens 1 Kontaktkanal konfigurieren."/>
    <revu.global.components.common.header.contactlist.minimum.limit.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Mindestlimit erreicht"/>
    <revu.global.components.common.header.contactlist.newtabcheck
        jcr:primaryType="sling:MessageEntry"
        sling:message="In neuem Tab öffnen"/>
    <revu.global.components.common.header.contacttab
        jcr:primaryType="sling:MessageEntry"
        sling:message="Kontakt"/>
    <revu.global.components.common.header.customerportalpath
        jcr:primaryType="sling:MessageEntry"
        sling:message="Customer Portal Path"/>
    <revu.global.components.common.header.customerportaltab
        jcr:primaryType="sling:MessageEntry"
        sling:message="Customer Portal"/>
    <revu.global.components.common.header.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="Beschreibung"/>
    <revu.global.components.common.header.disableCustomerPortal
        jcr:primaryType="sling:MessageEntry"
        sling:message="Customer Portal ausblenden"/>
    <revu.global.components.common.header.disableServicePortal
        jcr:primaryType="sling:MessageEntry"
        sling:message="Service Portal ausblenden"/>
    <revu.global.components.common.header.extraitempath
        jcr:primaryType="sling:MessageEntry"
        sling:message="Pfad/Url"/>
    <revu.global.components.common.header.extraitemtab
        jcr:primaryType="sling:MessageEntry"
        sling:message="Zusätzliche Verlinkung"/>
    <revu.global.components.common.header.extraitemtarget
        jcr:primaryType="sling:MessageEntry"
        sling:message="In neuem Fenster/Tab öffnen"/>
    <revu.global.components.common.header.fragSize
        jcr:primaryType="sling:MessageEntry"
        sling:message="Anzahl der Charakter in der Such-Ergebnis"/>
    <revu.global.components.common.header.headline
        jcr:primaryType="sling:MessageEntry"
        sling:message="Überschrift"/>
    <revu.global.components.common.header.iconpath
        jcr:primaryType="sling:MessageEntry"
        sling:message="Icon-Pfad"/>
    <revu.global.components.common.header.isflyoutrequired
        jcr:primaryType="sling:MessageEntry"
        sling:message="Flyout erforderlich"/>
    <revu.global.components.common.header.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Beschriftung"/>
    <revu.global.components.common.header.languageswitchtab
        jcr:primaryType="sling:MessageEntry"
        sling:message="Language Switch"/>
    <revu.global.components.common.header.logo
        jcr:primaryType="sling:MessageEntry"
        sling:message="Header Logo"/>
    <revu.global.components.common.header.logo2alttext
        jcr:primaryType="sling:MessageEntry"
        sling:message="Zweites Logo - Alt Text"/>
    <revu.global.components.common.header.logo2iconpath
        jcr:primaryType="sling:MessageEntry"
        sling:message="Zweites Logo - Icon Pfad/Url"/>
    <revu.global.components.common.header.logo2link
        jcr:primaryType="sling:MessageEntry"
        sling:message="Zweites Logo - Logo Link"/>
    <revu.global.components.common.header.logo2title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Zweites Logo - Titel"/>
    <revu.global.components.common.header.logoalttext
        jcr:primaryType="sling:MessageEntry"
        sling:message="Alt Text"/>
    <revu.global.components.common.header.logoiconpath
        jcr:primaryType="sling:MessageEntry"
        sling:message="Logo Icon Pfad/Url"/>
    <revu.global.components.common.header.logolink
        jcr:primaryType="sling:MessageEntry"
        sling:message="Logo Link"/>
    <revu.global.components.common.header.logotitle
        jcr:primaryType="sling:MessageEntry"
        sling:message="Titel"/>
    <revu.global.components.common.header.menu
        jcr:primaryType="sling:MessageEntry"
        sling:message="Menü"/>
    <revu.global.components.common.header.numberOfResults
        jcr:primaryType="sling:MessageEntry"
        sling:message="Anzahl der Treffer"/>
    <revu.global.components.common.header.numberofsuggestions
        jcr:primaryType="sling:MessageEntry"
        sling:message="Anzahl der Vorschläge"/>
    <revu.global.components.common.header.portaltab
        jcr:primaryType="sling:MessageEntry"
        sling:message="Portal Konfiguration"/>
    <revu.global.components.common.header.reloadPageForAnalyticsTracking
        jcr:primaryType="sling:MessageEntry"
        sling:message="Seite für jede Suchanfrage neu laden (für Analytics Tracking)"/>
    <revu.global.components.common.header.search.button.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Button Beschriftung"/>
    <revu.global.components.common.header.search.view
        jcr:primaryType="sling:MessageEntry"
        sling:message="Standardsuchansicht"/>
    <revu.global.components.common.header.search.view.lists
        jcr:primaryType="sling:MessageEntry"
        sling:message="Liste"/>
    <revu.global.components.common.header.search.view.tiles
        jcr:primaryType="sling:MessageEntry"
        sling:message="Fliese"/>
    <revu.global.components.common.header.searchresultpage
        jcr:primaryType="sling:MessageEntry"
        sling:message="Suchergebnisseite"/>
    <revu.global.components.common.header.searchtab
        jcr:primaryType="sling:MessageEntry"
        sling:message="Suche"/>
    <revu.global.components.common.header.serviceportalpath
        jcr:primaryType="sling:MessageEntry"
        sling:message="Service Portal Pfad/Url"/>
    <revu.global.components.common.header.serviceportaltab
        jcr:primaryType="sling:MessageEntry"
        sling:message="Service Portal"/>
</jcr:root>
