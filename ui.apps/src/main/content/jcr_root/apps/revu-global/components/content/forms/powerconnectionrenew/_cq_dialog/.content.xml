<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    jcr:primaryType="nt:unstructured"
    jcr:title="Power Connection Renew"
    sling:resourceType="cq/gui/components/authoring/dialog"
    extraClientlibs="[acs-commons.granite.ui.coral2.foundation,revu.rte.plugin]">
    <content
        jcr:primaryType="nt:unstructured"
        sling:resourceType="granite/ui/components/coral/foundation/tabs">
        <items jcr:primaryType="nt:unstructured">
            <generic
                jcr:primaryType="nt:unstructured"
                jcr:title="revu.global.components.forms.gridconnection.dialog.mail.details"
                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns">
                <items jcr:primaryType="nt:unstructured">
                    <column
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/foundation/container">
                        <items jcr:primaryType="nt:unstructured">
                            <headline
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <headline
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="Headline"
                                        name="./headline"/>
                                    <subheadline
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.form.gasconnection.dialog.subheadline"
                                        name="./subHeadline"/>
                                </items>
                            </headline>
                            <basicmail
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <mailSubject
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.mail.subject"
                                        name="./mailSubject"/>
                                    <mailTemplatePath
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                        fieldLabel="revu.global.components.forms.dialog.mailemplate.path"
                                        filter="hierarchy"
                                        name="./mailTemplatePath"
                                        rootPath="/etc/notification/email/revu-global"/>
                                    <mailid
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.mail.mailid"
                                        name="./mailid"/>
                                    <fromemail
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.mail.fromemail"
                                        name="./fromEmail"/>
                                </items>
                            </basicmail>
                            <mailtouser
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <mailSubject
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.mailtouser.subject"
                                        name="./userMailSubject"/>
                                    <mailTemplatePath
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                        fieldLabel="revu.global.components.forms.dialog.mailtouser.template.path"
                                        filter="hierarchy"
                                        name="./userEmailTemplate"
                                        rootPath="/etc/notification/email/revu-global"/>
                                </items>
                            </mailtouser>
                            <basicfields
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <successHeadline
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_basic.successHeadline"
                                        name="./successHeadline"/>
                                    <successCopy
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textarea"
                                        fieldLabel="revu.global.components.forms.dialog.tab_basic.successPageDescription"
                                        name="./successCopy"/>
                                    <requiredMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_basic.requiredMessage"
                                        name="./requiredMessage"/>
                                    <requiredErrorMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_basic.requiredErrorMessage"
                                        name="./requiredErrorMessage"/>
                                    <hintMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textarea"
                                        fieldLabel="revu.global.components.forms.dialog.tab_basic.hintMessage"
                                        name="./hintMessage"/>
                                </items>
                            </basicfields>
                        </items>
                    </column>
                </items>
            </generic>
            <fields
                jcr:primaryType="nt:unstructured"
                jcr:title="Power Connection Fields"
                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns">
                <items jcr:primaryType="nt:unstructured">
                    <column
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/foundation/container">
                        <items jcr:primaryType="nt:unstructured">
                            <formgroupgasconnection
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                fieldLabel="Power Connection Headline"
                                name="./pcHeadline"/>
                            <street
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <label
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.street.label"
                                        name="./pcStreetLabel"/>
                                    <isrequired
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.street.isrequired"
                                        name="./pcStreetIsRequired"
                                        text="revu.global.components.forms.dialog.tab_fields.street.isrequired"
                                        value="true"/>
                                    <errorMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.street.errormessage"
                                        name="./pcStreetErrorMessage"/>
                                </items>
                            </street>
                            <housenumber
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <label
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.housenumber.label"
                                        name="./pcHouseNumberLabel"/>
                                    <isrequired
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.housenumber.isrequired"
                                        name="./pcHouseNumberIsRequired"
                                        text="revu.global.components.forms.dialog.tab_fields.housenumber.isrequired"
                                        value="true"/>
                                    <errorMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.housenumber.errormessage"
                                        name="./pcHouseNumberErrorMessage"/>
                                </items>
                            </housenumber>
                            <zip
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <zipLabel
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.zip.label"
                                        name="./pcZipLabel"/>
                                    <zipRequired
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.zip.isrequired"
                                        name="./pcZipIsRequired"
                                        text="revu.global.components.forms.dialog.tab_fields.zip.isrequired"
                                        value="true"/>
                                    <zipMaxLength
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/numberfield"
                                        defaultValue="5"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.zip.maxlength"
                                        name="./pcZipMaxLength"/>
                                    <zipMinLength
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/numberfield"
                                        defaultValue="5"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.zip.minlength"
                                        name="./pcZipMinLength"/>
                                    <zipErrorMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.zip.errormessage"
                                        name="./pcZipErrorMessage"/>
                                    <zipMinLengthErrorMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.zip.minlength.errormessage"
                                        name="./pcZipMinLengthErrorMessage"/>
                                </items>
                            </zip>
                            <town
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <townLabel
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.town.label"
                                        name="./pcTownLabel"/>
                                    <townRequired
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.town.isrequired"
                                        name="./pcTownIsRequired"
                                        text="revu.global.components.forms.dialog.tab_fields.town.isrequired"
                                        value="true"/>
                                    <townErrorMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.town.errormessage"
                                        name="./pcTownErrorMessage"/>
                                </items>
                            </town>
                            <addaddress
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <label
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="Additional Address Label"
                                        name="./pcAddressLabel"/>
                                    <isrequired
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                        fieldLabel="Additional Address Label Reuired"
                                        name="./pcAddressIsRequired"
                                        text="Additional Address Label Reuired"
                                        value="true"/>
                                    <errorMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="Additional Address Label Error Message"
                                        name="./pcAddressLabelErrorMessage"/>
                                </items>
                            </addaddress>
                            <housenumberid
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <label
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="House ID Number"
                                        name="./pcHouseId"/>
                                    <isrequired
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                        fieldLabel="House ID Number Required"
                                        name="./pcHouseIdIsRequired"
                                        text="House ID Number Required"
                                        value="true"/>
                                    <errorMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="House ID Number Error Message"
                                        name="./pcHouseIdErrorMessage"/>
                                </items>
                            </housenumberid>
                        </items>
                    </column>
                </items>
            </fields>
            <fields2
                jcr:primaryType="nt:unstructured"
                jcr:title="revu.global.components.form.gasconnection.dialog.customer.title"
                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns">
                <items jcr:primaryType="nt:unstructured">
                    <column
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/foundation/container">
                        <items jcr:primaryType="nt:unstructured">
                            <customers
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                fieldLabel="revu.global.components.form.gasconnection.dialog.customer.headline"
                                name="./cusHeadline"/>
                            <salutation
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <label
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="Salutation Label"
                                        name="./salutationLabel"/>
                                    <salutationRequired
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                        fieldLabel="Is Salutation field required"
                                        name="./salutationIsRequired"
                                        text="Is Salutation field required"
                                        value="true"/>
                                    <options
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                        fieldLabel="Salutation Options">
                                        <field
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                            acs-commons-nested=""
                                            name="./salutationOptions">
                                            <layout
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/foundation/layouts/fixedcolumns"
                                                method="absolute"/>
                                            <items jcr:primaryType="nt:unstructured">
                                                <column
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/foundation/container">
                                                    <items jcr:primaryType="nt:unstructured">
                                                        <linkTitle
                                                            jcr:primaryType="nt:unstructured"
                                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                            fieldLabel="Options"
                                                            name="./salutation"/>
                                                    </items>
                                                </column>
                                            </items>
                                        </field>
                                    </options>
                                </items>
                            </salutation>
                            <lastname
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <townLabel
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.lastname.label"
                                        name="./cusLastNameLabel"/>
                                    <townRequired
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.lastname.isrequired"
                                        name="./cusLastNameIsRequired"
                                        text="revu.global.components.forms.dialog.tab_fields.lastname.isrequired"
                                        value="true"/>
                                    <townErrorMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.lastname.errormessage"
                                        name="./cusLastNameErrorMessage"/>
                                </items>
                            </lastname>
                            <firstname
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <townLabel
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.firstname.label"
                                        name="./cusFirstNameLabel"/>
                                    <townRequired
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.firstname.isrequired"
                                        name="./cusFirstNameIsRequired"
                                        text="revu.global.components.forms.dialog.tab_fields.firstname.isrequired"
                                        value="true"/>
                                    <townErrorMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.firstname.errormessage"
                                        name="./cusFirstNameErrorMessage"/>
                                </items>
                            </firstname>
                            <phone
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <phoneLabel
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.phone.label"
                                        name="./cusPhoneLabel"/>
                                    <isRequired
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.phone.isrequired"
                                        name="./cusPhoneIsRequired"
                                        text="revu.global.components.forms.dialog.tab_fields.phone.isrequired"
                                        value="true"/>
                                    <phoneErrorMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.phone.errormessage"
                                        name="./cusPhoneErrorMessage"/>
                                </items>
                            </phone>
                            <email
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <emailLabel
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.email.label"
                                        name="./cusEmailLabel"/>
                                    <emailRequired
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.email.isrequired"
                                        name="./cusEmailIsRequired"
                                        text="revu.global.components.forms.dialog.tab_fields.email.isrequired"
                                        value="true"/>
                                    <emailErrorMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.email.errormessage"
                                        name="./cusEmailErrorMessage"/>
                                </items>
                            </email>
                            <contactperson
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <label
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="Contact Person Label"
                                        name="./contactPersonLabel"/>
                                    <contaactpersonRequired
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                        fieldLabel="Is Contact Person field required"
                                        name="./contactPersonIsRequired"
                                        text="Is Contact Person field required"
                                        value="true"/>
                                    <options
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                        fieldLabel="Contact Person Options">
                                        <field
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                            acs-commons-nested=""
                                            name="./customTypeOptions">
                                            <layout
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/foundation/layouts/fixedcolumns"
                                                method="absolute"/>
                                            <items jcr:primaryType="nt:unstructured">
                                                <column
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/foundation/container">
                                                    <items jcr:primaryType="nt:unstructured">
                                                        <linkTitle
                                                            jcr:primaryType="nt:unstructured"
                                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                            fieldLabel="Options"
                                                            name="./contactPerson"/>
                                                    </items>
                                                </column>
                                            </items>
                                        </field>
                                    </options>
                                </items>
                            </contactperson>
                        </items>
                    </column>
                </items>
            </fields2>
            <fields5
                jcr:primaryType="nt:unstructured"
                jcr:title="revu.global.components.forms.dialog.tab_fields.title"
                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns">
                <items jcr:primaryType="nt:unstructured">
                    <column
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/foundation/container">
                        <items jcr:primaryType="nt:unstructured">
                            <contactpersonheadline
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                fieldLabel="Contact Person Message headline"
                                name="./messageHeadlinePerson"/>
                            <contactpersonmessage
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <Label
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="Comment field label for Contact Person"
                                        name="./messageLabelPerson"/>
                                    <Required
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.comments.isrequired"
                                        name="./messageIsRequiredPerson"
                                        text="revu.global.components.forms.dialog.tab_fields.comments.isrequired"
                                        value="true"/>
                                    <MaxLength
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/numberfield"
                                        defaultValue="5000"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.comments.maxlength"
                                        name="./messagemaxLengthPerson"/>
                                    <ErrorMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.comments.errormessage"
                                        name="./messageErrorMessagePerson"/>
                                </items>
                            </contactpersonmessage>
                            <messageheadline
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                fieldLabel="Message headline"
                                name="./messageHeadline"/>
                            <message
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <Label
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.comments.label"
                                        name="./messageLabel"/>
                                    <Required
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.comments.isrequired"
                                        name="./messageIsRequired"
                                        text="revu.global.components.forms.dialog.tab_fields.comments.isrequired"
                                        value="true"/>
                                    <MaxLength
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/numberfield"
                                        defaultValue="5000"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.comments.maxlength"
                                        name="./messagemaxLength"/>
                                    <ErrorMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.comments.errormessage"
                                        name="./messageErrorMessage"/>
                                </items>
                            </message>
                            <reinforcementheadline
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                fieldLabel="Reinforcement headline"
                                name="./reinforcementHeading"/>
                            <reinforcemessage
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <Label
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="Reinforce Message Option"
                                        name="./reinforceOption"/>
                                    <Required
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                        fieldLabel="Reinforcement Message Required"
                                        name="./reMessageIsRequired"
                                        text="Reinforcement Message Required"
                                        value="true"/>
                                    <ErrorMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="Reinforcement Error Message"
                                        name="./reErrorMessage"/>
                                </items>
                            </reinforcemessage>
                            <sprintheadline
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                fieldLabel="Sprint Bonus Headline"
                                name="./sprintBonusHeading"/>
                            <sprintmessage
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <Label
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="Sprint Option Message"
                                        name="./sprintOption"/>
                                    <Required
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                        fieldLabel="Sprint Option Required"
                                        name="./sprintOptionRequired"
                                        text="Sprint Option Required"
                                        value="true"/>
                                    <ErrorMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="Sprint Option Error Message"
                                        name="./sprintErrorMessage"/>
                                </items>
                            </sprintmessage>
                            <confirmationheadline
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                fieldLabel="Confirmation headline"
                                name="./confirmationHeadline"/>
                            <footerMsg1
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/gui/components/authoring/dialog/richtext"
                                fieldLabel="Footer Message Line 1"
                                hideLabel="{Boolean}true"
                                name="./footerMsg1"
                                rtelistrevuglobal="{Boolean}true"
                                useFixedInlineToolbar="{Boolean}true">
                                <uiSettings jcr:primaryType="nt:unstructured">
                                    <cui jcr:primaryType="nt:unstructured">
                                        <inline
                                            jcr:primaryType="nt:unstructured"
                                            toolbar="[#styles,format#bold,format#italic,format#underline,subsuperscript#subscript,subsuperscript#superscript,#edit,edit#cut,edit#copy,edit#paste-default,edit#paste-plaintext,edit#paste-wordhtml,#links,links#modifylink,links#unlink,links#anchor,undo#undo,undo#redo,justify#justifyleft,justify#justifycenter,justify#justifyright,#lists,lists#unordered,lists#ordered,lists#outdent,lists#indent,spellcheck#checktext,misctools#specialchars,misctools#sourceedit,#paraformat,image#imageProps,findreplace#find,findreplace#replace,dkestyleformat#dkestyles]">
                                            <popovers jcr:primaryType="nt:unstructured">
                                                <styles
                                                    jcr:primaryType="nt:unstructured"
                                                    items="styles:getStyles:styles-pulldown"
                                                    ref="styles"/>
                                                <justify
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[justify#justifyleft,justify#justifycenter,justify#justifyright]"
                                                    ref="justify"/>
                                                <paraformat
                                                    jcr:primaryType="nt:unstructured"
                                                    items="paraformat:getFormats:paraformat-pulldown"
                                                    ref="paraformat"/>
                                                <table
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[table#table,table#removetable, table#insertrow, table#removerow, table#insertcolumn, table#removecolumn, table#cellprops, table#mergecells, table#splitcell, table#selectrow, table#selectcolumn]"
                                                    ref="table"/>
                                                <findreplace
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[findreplace#find,findreplace#replace]"
                                                    ref="findreplace"/>
                                                <misctools
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[misctools#specialchars,misctools#sourceedit]"
                                                    ref="misctools"/>
                                                <subsuperscript
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[subsuperscript#subscript,subsuperscript#superscript]"
                                                    ref="subsuperscript"/>
                                                <undo
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[undo#undo,undo#redo]"
                                                    ref="undo"/>
                                                <spellcheck
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[spellcheck#checktext]"
                                                    ref="spellcheck"/>
                                            </popovers>
                                        </inline>
                                        <tableEditOptions
                                            jcr:primaryType="nt:unstructured"
                                            toolbar="[table#insertcolumn-before,table#insertcolumn-after,table#removecolumn,table#insertrow-before,table#insertrow-after,table#removerow,table#mergecells-right,table#mergecells-down,table#mergecells,table#splitcell-horizontal,table#splitcell-vertical,table#selectrow,table#selectcolumn,table#ensureparagraph,table#modifytableandcell,table#removetable,undo#undo,undo#redo,table#exitTableEditing]">
                                            <popovers jcr:primaryType="nt:unstructured">
                                                <table
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[table#insertcolumn-before,table#insertcolumn-after,table#removecolumn, table#insertrow-before,table#insertrow-after,table#removerow,table#mergecells-right,table#mergecells-down,table#mergecells,table#splitcell-horizontal,table#splitcell-vertical,table#selectrow,table#selectcolumn,table#removetable, table#insertrow, table#removerow, table#insertcolumn, table#removecolumn, table#cellprops, table#mergecells, table#splitcell, table#selectrow, table#selectcolumn,table#ensureparagraph,table#exitTableEditing]"
                                                    ref="table"/>
                                                <undo
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[undo#undo,undo#redo]"
                                                    ref="undo"/>
                                            </popovers>
                                        </tableEditOptions>
                                        <dialogFullScreen
                                            jcr:primaryType="nt:unstructured"
                                            toolbar="[#styles,format#bold,format#italic,format#underline,subsuperscript#subscript,subsuperscript#superscript,#edit,edit#cut,edit#copy,edit#paste-default,edit#paste-plaintext,edit#paste-wordhtml,#links,links#modifylink,links#unlink,links#anchor,undo#undo,undo#redo,justify#justifyleft,justify#justifycenter,justify#justifyright,#lists,lists#unordered,lists#ordered,lists#outdent,lists#indent,spellcheck#checktext,misctools#specialchars,misctools#sourceedit,#paraformat,image#imageProps,findreplace#find,findreplace#replace,dkestyleformat#dkestyles]">
                                            <popovers jcr:primaryType="nt:unstructured">
                                                <paraformat
                                                    jcr:primaryType="nt:unstructured"
                                                    items="paraformat:getFormats:paraformat-pulldown"
                                                    ref="paraformat"/>
                                                <styles
                                                    jcr:primaryType="nt:unstructured"
                                                    items="styles:getStyles:styles-pulldown"
                                                    ref="styles"/>
                                                <justify
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[justify#justifyleft,justify#justifycenter,justify#justifyright]"
                                                    ref="justify"/>
                                                <table
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[table#table,table#removetable, table#insertrow, table#removerow, table#insertcolumn, table#removecolumn, table#cellprops, table#mergecells, table#splitcell, table#selectrow, table#selectcolumn]"
                                                    ref="table"/>
                                                <findreplace
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[findreplace#find,findreplace#replace]"
                                                    ref="findreplace"/>
                                                <misctools
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[misctools#specialchars,misctools#sourceedit]"
                                                    ref="misctools"/>
                                                <subsuperscript
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[subsuperscript#subscript,subsuperscript#superscript]"
                                                    ref="subsuperscript"/>
                                                <undo
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[undo#undo,undo#redo]"
                                                    ref="undo"/>
                                                <spellcheck
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[spellcheck#checktext]"
                                                    ref="spellcheck"/>
                                            </popovers>
                                        </dialogFullScreen>
                                    </cui>
                                </uiSettings>
                                <rtePlugins jcr:primaryType="nt:unstructured">
                                    <format
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <justify
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <lists
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <links
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <table
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <spellcheck
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <image
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <edit
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <findreplace
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <styles
                                        jcr:primaryType="nt:unstructured"
                                        features="*">
                                        <styles jcr:primaryType="cq:WidgetCollection">
                                            <textSmall
                                                jcr:primaryType="nt:unstructured"
                                                cssName="text-small"
                                                text="Text Small"/>
                                            <textMicro
                                                jcr:primaryType="nt:unstructured"
                                                cssName="text-micro"
                                                text="Text Micro"/>
                                            <textSubline
                                                jcr:primaryType="nt:unstructured"
                                                cssName="text-subline"
                                                text="Text Subline"/>
                                        </styles>
                                    </styles>
                                    <paraformat
                                        jcr:primaryType="nt:unstructured"
                                        features="*">
                                        <formats jcr:primaryType="cq:WidgetCollection">
                                            <p
                                                jcr:primaryType="nt:unstructured"
                                                description="paragraph"
                                                tag="p"/>
                                            <h2
                                                jcr:primaryType="nt:unstructured"
                                                description="Heading 2"
                                                tag="h2"/>
                                            <h3
                                                jcr:primaryType="nt:unstructured"
                                                description="heading 3"
                                                tag="h3"/>
                                            <h4
                                                jcr:primaryType="nt:unstructured"
                                                description="Heading 4"
                                                tag="h4"/>
                                            <h5
                                                jcr:primaryType="nt:unstructured"
                                                description="Heading 5"
                                                tag="h5"/>
                                        </formats>
                                    </paraformat>
                                    <misctools
                                        jcr:primaryType="nt:unstructured"
                                        features="*">
                                        <specialCharsConfig jcr:primaryType="nt:unstructured">
                                            <chars jcr:primaryType="nt:unstructured">
                                                <copyright
                                                    jcr:primaryType="nt:unstructured"
                                                    entity="&amp;#169"
                                                    name="copyright"/>
                                                <emDash
                                                    jcr:primaryType="nt:unstructured"
                                                    entity="&amp;#8212"
                                                    name="emDash"/>
                                                <nbsp
                                                    jcr:primaryType="nt:unstructured"
                                                    entity="&amp;#160"
                                                    name="nbsp"/>
                                                <registered
                                                    jcr:primaryType="nt:unstructured"
                                                    entity="&amp;#174"
                                                    name="registered"/>
                                                <trademark
                                                    jcr:primaryType="nt:unstructured"
                                                    entity="&amp;#8482"
                                                    name="trademark"/>
                                            </chars>
                                        </specialCharsConfig>
                                    </misctools>
                                    <undo
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <subsuperscript
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <tracklinks
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <dkestyleformat
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                </rtePlugins>
                            </footerMsg1>
                            <footerMsg2
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/gui/components/authoring/dialog/richtext"
                                fieldLabel="Footer Message Line 2"
                                hideLabel="{Boolean}true"
                                name="./footerMsg2"
                                rtelistrevuglobal="{Boolean}true"
                                useFixedInlineToolbar="{Boolean}true">
                                <uiSettings jcr:primaryType="nt:unstructured">
                                    <cui jcr:primaryType="nt:unstructured">
                                        <inline
                                            jcr:primaryType="nt:unstructured"
                                            toolbar="[#styles,format#bold,format#italic,format#underline,subsuperscript#subscript,subsuperscript#superscript,#edit,edit#cut,edit#copy,edit#paste-default,edit#paste-plaintext,edit#paste-wordhtml,#links,links#modifylink,links#unlink,links#anchor,undo#undo,undo#redo,justify#justifyleft,justify#justifycenter,justify#justifyright,#lists,lists#unordered,lists#ordered,lists#outdent,lists#indent,spellcheck#checktext,misctools#specialchars,misctools#sourceedit,#paraformat,image#imageProps,findreplace#find,findreplace#replace,dkestyleformat#dkestyles]">
                                            <popovers jcr:primaryType="nt:unstructured">
                                                <styles
                                                    jcr:primaryType="nt:unstructured"
                                                    items="styles:getStyles:styles-pulldown"
                                                    ref="styles"/>
                                                <justify
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[justify#justifyleft,justify#justifycenter,justify#justifyright]"
                                                    ref="justify"/>
                                                <paraformat
                                                    jcr:primaryType="nt:unstructured"
                                                    items="paraformat:getFormats:paraformat-pulldown"
                                                    ref="paraformat"/>
                                                <table
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[table#table,table#removetable, table#insertrow, table#removerow, table#insertcolumn, table#removecolumn, table#cellprops, table#mergecells, table#splitcell, table#selectrow, table#selectcolumn]"
                                                    ref="table"/>
                                                <findreplace
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[findreplace#find,findreplace#replace]"
                                                    ref="findreplace"/>
                                                <misctools
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[misctools#specialchars,misctools#sourceedit]"
                                                    ref="misctools"/>
                                                <subsuperscript
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[subsuperscript#subscript,subsuperscript#superscript]"
                                                    ref="subsuperscript"/>
                                                <undo
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[undo#undo,undo#redo]"
                                                    ref="undo"/>
                                                <spellcheck
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[spellcheck#checktext]"
                                                    ref="spellcheck"/>
                                            </popovers>
                                        </inline>
                                        <tableEditOptions
                                            jcr:primaryType="nt:unstructured"
                                            toolbar="[table#insertcolumn-before,table#insertcolumn-after,table#removecolumn,table#insertrow-before,table#insertrow-after,table#removerow,table#mergecells-right,table#mergecells-down,table#mergecells,table#splitcell-horizontal,table#splitcell-vertical,table#selectrow,table#selectcolumn,table#ensureparagraph,table#modifytableandcell,table#removetable,undo#undo,undo#redo,table#exitTableEditing]">
                                            <popovers jcr:primaryType="nt:unstructured">
                                                <table
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[table#insertcolumn-before,table#insertcolumn-after,table#removecolumn, table#insertrow-before,table#insertrow-after,table#removerow,table#mergecells-right,table#mergecells-down,table#mergecells,table#splitcell-horizontal,table#splitcell-vertical,table#selectrow,table#selectcolumn,table#removetable, table#insertrow, table#removerow, table#insertcolumn, table#removecolumn, table#cellprops, table#mergecells, table#splitcell, table#selectrow, table#selectcolumn,table#ensureparagraph,table#exitTableEditing]"
                                                    ref="table"/>
                                                <undo
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[undo#undo,undo#redo]"
                                                    ref="undo"/>
                                            </popovers>
                                        </tableEditOptions>
                                        <dialogFullScreen
                                            jcr:primaryType="nt:unstructured"
                                            toolbar="[#styles,format#bold,format#italic,format#underline,subsuperscript#subscript,subsuperscript#superscript,#edit,edit#cut,edit#copy,edit#paste-default,edit#paste-plaintext,edit#paste-wordhtml,#links,links#modifylink,links#unlink,links#anchor,undo#undo,undo#redo,justify#justifyleft,justify#justifycenter,justify#justifyright,#lists,lists#unordered,lists#ordered,lists#outdent,lists#indent,spellcheck#checktext,misctools#specialchars,misctools#sourceedit,#paraformat,image#imageProps,findreplace#find,findreplace#replace,dkestyleformat#dkestyles]">
                                            <popovers jcr:primaryType="nt:unstructured">
                                                <paraformat
                                                    jcr:primaryType="nt:unstructured"
                                                    items="paraformat:getFormats:paraformat-pulldown"
                                                    ref="paraformat"/>
                                                <styles
                                                    jcr:primaryType="nt:unstructured"
                                                    items="styles:getStyles:styles-pulldown"
                                                    ref="styles"/>
                                                <justify
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[justify#justifyleft,justify#justifycenter,justify#justifyright]"
                                                    ref="justify"/>
                                                <table
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[table#table,table#removetable, table#insertrow, table#removerow, table#insertcolumn, table#removecolumn, table#cellprops, table#mergecells, table#splitcell, table#selectrow, table#selectcolumn]"
                                                    ref="table"/>
                                                <findreplace
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[findreplace#find,findreplace#replace]"
                                                    ref="findreplace"/>
                                                <misctools
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[misctools#specialchars,misctools#sourceedit]"
                                                    ref="misctools"/>
                                                <subsuperscript
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[subsuperscript#subscript,subsuperscript#superscript]"
                                                    ref="subsuperscript"/>
                                                <undo
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[undo#undo,undo#redo]"
                                                    ref="undo"/>
                                                <spellcheck
                                                    jcr:primaryType="nt:unstructured"
                                                    items="[spellcheck#checktext]"
                                                    ref="spellcheck"/>
                                            </popovers>
                                        </dialogFullScreen>
                                    </cui>
                                </uiSettings>
                                <rtePlugins jcr:primaryType="nt:unstructured">
                                    <format
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <justify
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <lists
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <links
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <table
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <spellcheck
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <image
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <edit
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <findreplace
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <styles
                                        jcr:primaryType="nt:unstructured"
                                        features="*">
                                        <styles jcr:primaryType="cq:WidgetCollection">
                                            <textSmall
                                                jcr:primaryType="nt:unstructured"
                                                cssName="text-small"
                                                text="Text Small"/>
                                            <textMicro
                                                jcr:primaryType="nt:unstructured"
                                                cssName="text-micro"
                                                text="Text Micro"/>
                                            <textSubline
                                                jcr:primaryType="nt:unstructured"
                                                cssName="text-subline"
                                                text="Text Subline"/>
                                        </styles>
                                    </styles>
                                    <paraformat
                                        jcr:primaryType="nt:unstructured"
                                        features="*">
                                        <formats jcr:primaryType="cq:WidgetCollection">
                                            <p
                                                jcr:primaryType="nt:unstructured"
                                                description="paragraph"
                                                tag="p"/>
                                            <h2
                                                jcr:primaryType="nt:unstructured"
                                                description="Heading 2"
                                                tag="h2"/>
                                            <h3
                                                jcr:primaryType="nt:unstructured"
                                                description="heading 3"
                                                tag="h3"/>
                                            <h4
                                                jcr:primaryType="nt:unstructured"
                                                description="Heading 4"
                                                tag="h4"/>
                                            <h5
                                                jcr:primaryType="nt:unstructured"
                                                description="Heading 5"
                                                tag="h5"/>
                                        </formats>
                                    </paraformat>
                                    <misctools
                                        jcr:primaryType="nt:unstructured"
                                        features="*">
                                        <specialCharsConfig jcr:primaryType="nt:unstructured">
                                            <chars jcr:primaryType="nt:unstructured">
                                                <copyright
                                                    jcr:primaryType="nt:unstructured"
                                                    entity="&amp;#169"
                                                    name="copyright"/>
                                                <emDash
                                                    jcr:primaryType="nt:unstructured"
                                                    entity="&amp;#8212"
                                                    name="emDash"/>
                                                <nbsp
                                                    jcr:primaryType="nt:unstructured"
                                                    entity="&amp;#160"
                                                    name="nbsp"/>
                                                <registered
                                                    jcr:primaryType="nt:unstructured"
                                                    entity="&amp;#174"
                                                    name="registered"/>
                                                <trademark
                                                    jcr:primaryType="nt:unstructured"
                                                    entity="&amp;#8482"
                                                    name="trademark"/>
                                            </chars>
                                        </specialCharsConfig>
                                    </misctools>
                                    <undo
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <subsuperscript
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <tracklinks
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                    <dkestyleformat
                                        jcr:primaryType="nt:unstructured"
                                        features="*"/>
                                </rtePlugins>
                            </footerMsg2>
                            <confirmation
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <Label
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.confirmation.message"
                                        name="./confirmationLabel"/>
                                    <Required
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.confirmation.isrequired"
                                        name="./confirmationIsRequired"
                                        text="revu.global.components.forms.dialog.tab_fields.confirmation.isrequired"
                                        value="true"/>
                                    <ErrorMessage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="revu.global.components.forms.dialog.tab_fields.confirmation.errormessage"
                                        name="./confirmationErrorMessage"/>
                                </items>
                            </confirmation>
                            <submitLabel
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                fieldLabel="revu.global.components.forms.dialog.tab_fields.submit.label"
                                name="./submitLabel"/>
                        </items>
                    </column>
                </items>
            </fields5>
            <color
                jcr:primaryType="nt:unstructured"
                jcr:title="revu.global.templates.projectrootpage.dialog.tab.color.title"
                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns">
                <items jcr:primaryType="nt:unstructured">
                    <column
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/foundation/container">
                        <items jcr:primaryType="nt:unstructured">
                            <colorvariation
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.variation"
                                name="./variationName">
                                <datasource
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="/bin/optionsProvider"
                                    propName="buttonColorConfig"/>
                            </colorvariation>
                        </items>
                    </column>
                </items>
            </color>
        </items>
    </content>
</jcr:root>
