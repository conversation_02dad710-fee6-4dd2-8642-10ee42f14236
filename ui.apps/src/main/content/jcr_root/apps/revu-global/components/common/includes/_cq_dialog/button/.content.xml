<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    jcr:primaryType="nt:unstructured"
    jcr:title="revu.global.components.common.includes.dialog.button.label"
    sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns">
    <items jcr:primaryType="nt:unstructured">
        <column
            jcr:primaryType="nt:unstructured"
            sling:resourceType="granite/ui/components/foundation/container">
            <items jcr:primaryType="nt:unstructured">
                <button
                    jcr:primaryType="nt:unstructured"
                    jcr:title="revu.global.components.common.includes.dialog.button.label"
                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                    <items jcr:primaryType="nt:unstructured">
                        <buttontext
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                            fieldLabel="revu.global.components.common.includes.dialog.label.text"
                            name="./buttonLabel"/>
                        <buttonlink
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                            fieldLabel="revu.global.components.common.includes.dialog.link.target"
                            name="./buttonTarget"
                            rootPath="/content/revu-global"/>
                        <checkbox
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                            fieldDescription="revu.global.components.common.includes.dialog.link.newtabcheck"
                            name="./buttonNewTab"
                            text="revu.global.components.common.includes.dialog.link.newtabcheck"
                            value="true"/>
                    </items>
                </button>
            </items>
        </column>
    </items>
</jcr:root>
