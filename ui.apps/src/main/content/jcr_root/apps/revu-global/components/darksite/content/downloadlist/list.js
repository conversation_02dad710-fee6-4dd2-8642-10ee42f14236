use(["/apps/revu-global/components/common/includes/formatMultifield.js"],function(format) {

    var linklist={};
    var linkUtil = Packages.com.eon.dist.dke.aem.core.util.LinkUtil
    var jcrUtil =  Packages.com.eon.dist.dke.aem.core.util.JcrUtil;
    var pageUtil = Packages.com.eon.dist.dke.aem.core.util.PageUtil;

    var links = granite.resource.properties["linklist"] || [];
    var numOfCol = parseInt(granite.resource.properties["numOfCol"] || 3);

    var rowList = [];
    var columnItems = [];

    linklist.headline = granite.resource.properties["headline"] || "";
    linklist.additionalinfo = granite.resource.properties["additionalinfo"] || "";
    links = format(links);
    for (var i=0, key=0; i < links.length; i++){
        var assetResource = resolver.getResource(links[i].linkDefault);
        var asset = assetResource? assetResource.adaptTo(Packages.com.day.cq.dam.api.Asset) : null ;
        if (asset){
            links[i].linkText = links[i].linkText || asset.getMetadataValue("jcr:title") || asset.getMetadataValue("dc:title") || asset.getName();
            links[i].linkText = links[i].linkText + getTitleExt(asset);
			links[i].linkDefault = linkUtil.getExternalizedUrl(links[i].linkDefault, resolver);
        } else {
			links[i].linkDefault = linkUtil.getExternalizedUrl(links[i].linkDefault, resolver);
            links[i].isLink = true;
        }

        columnItems.push(links[i]);
        rowList[key] = columnItems;
        if( ((i+1) % numOfCol) == 0){
            key++;
            columnItems = [];
        }
        links[i].imagePath = "image"+i;

    }
    function getTitleExt(asset){
        var path = asset.getPath();
		var extension = path? path.substring(path.lastIndexOf(".")+1):"";
        var size = jcrUtil.getSize(asset);
		return " / "+ extension.toUpperCase() +" ("+  size +")";
    }

    linklist.downloadList = links;
    linklist.rowList = rowList;
    linklist.colWidth = 12/numOfCol;

    linklist.isMargin = pageUtil.isMarginContent(resource);

  	return linklist;
});