<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    jcr:primaryType="nt:unstructured"
    sling:resourceType="granite/ui/components/foundation/container">
    <items jcr:primaryType="nt:unstructured">
        <tabs
            jcr:primaryType="nt:unstructured"
            sling:resourceType="granite/ui/components/foundation/container"
            rel="cq-siteadmin-admin-properties-tabs">
            <layout
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/foundation/layouts/tabs"
                type="nav"/>
            <items
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/foundation/container">
                <colorconfig
                    jcr:primaryType="nt:unstructured"
                    jcr:title="revu.global.templates.projectrootpage.dialog.color.title"
                    sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns">
                    <items jcr:primaryType="nt:unstructured">
                        <column
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="granite/ui/components/coral/foundation/container">
                            <items jcr:primaryType="nt:unstructured">
                                <globalconfig
                                    cq:showOnCreate="{Boolean}true"
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.global.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <global
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.global.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset"
                                            title="revu.global.templates.projectrootpage.dialog.color.global.title">
                                            <items jcr:primaryType="nt:unstructured">
                                                <colorConfigExists
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/hidden"
                                                    name="./colorConfigExists"
                                                    value="true"/>
                                                <h1
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="H1"
                                                    name="./globalH1Color"/>
                                                <h1line
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.global.h1line.label"
                                                    name="./globalH1LineColor"/>
                                                <h2
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="H2"
                                                    name="./globalH2Color"/>
                                                <h3
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="H3"
                                                    name="./globalH3Color"/>
                                                <h4
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="H4"
                                                    name="./globalH4Color"/>
                                                <h5
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="H5"
                                                    name="./globalH5Color"/>
                                                <text
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                                    name="./globalTextColor"/>
                                                <listicons
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.listicons.label"
                                                    name="./globalListIconsColor"/>
                                                <links
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.global.links.label"
                                                    name="./globalLinksColor"/>
                                                <imagetext
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.image.text.label"
                                                    name="./globalImageTextColor"/>
                                                <h1topline
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.global.h1topline.label"
                                                    name="./globalH1TopLineColor"/>
                                                <iconlink
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.global.icon.link.label"
                                                    name="./globalIconLinkColor"/>
                                                <iconlinkhover
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.global.icon.link.hover.label"
                                                    name="./globalIconLinkHoverColor"/>
                                                <seperatorline
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.global.seperator.line.label"
                                                    name="./globalSeperatorLineColor"/>
                                                <searchresulthighlighter
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="Highlighted Search Words"
                                                    name="./searchResultsHighlighter"/>
                                                <headerfont
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                    emptyText="Select font"
                                                    fieldLabel="Headerfont"
                                                    name="./headerfont">
                                                    <items jcr:primaryType="nt:unstructured">
                                                        <DefaultFont
                                                            jcr:primaryType="nt:unstructured"
                                                            text="Default Font"
                                                            value=" "/>
                                                        <BoldFont
                                                            jcr:primaryType="nt:unstructured"
                                                            text="Eon Bold"
                                                            value="Eon Bold"/>
                                                        <RegularFont
                                                            jcr:primaryType="nt:unstructured"
                                                            text="Regular"
                                                            value="Regular"/>
                                                    </items>
                                                </headerfont>
                                            </items>
                                        </global>
                                        <pagination
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.global.pagination.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <numbers
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.global.pagination.numbers.title"
                                                    name="./paginationNumbersColor"/>
                                                <numbersactive
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.global.pagination.numbers.active.title"
                                                    name="./paginationNumbersActiveColor"/>
                                                <numbershover
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.global.pagination.numbers.hover.title"
                                                    name="./paginationNumbersHoverColor"/>
                                                <iconarrows
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.global.pagination.icon.arrows.title"
                                                    name="./paginationIconArrowsColor"/>
                                            </items>
                                        </pagination>
                                        <errorpage
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.global.errorpage.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <numbers
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.global.errorpage.numbers.title"
                                                    name="./errorPageNumberColor"/>
                                            </items>
                                        </errorpage>
                                    </items>
                                </globalconfig>
                                <headerconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.header.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <headermeta
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.headermeta.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/container">
                                            <items jcr:primaryType="nt:unstructured">
                                                <bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                    name="./headerMetaBgColor"/>
                                                <iconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
                                                    name="./headerMetaIconColor"/>
                                                <text
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                                    name="./headerMetaTextColor"/>
                                                <bgactivehover
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.active.hover.label"
                                                    name="./headerMetaBgActiveHoverColor"/>
                                                <iconactivehover
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.active.hover.label"
                                                    name="./headerMetaIconActiveHoverColor"/>
                                                <textactivehover
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.active.hover.label"
                                                    name="./headerMetaTextActiveHoverColor"/>
                                            </items>
                                        </headermeta>
                                        <portalbutton
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.portalbutton.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                    name="./headerPortalButtonBgColor"/>
                                                <iconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
                                                    name="./headerPortalButtonIconColor"/>
                                                <text
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                                    name="./headerPortalButtonTextColor"/>
                                                <roundcorner
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                    fieldDescription="Check the box button change to round corners"
                                                    name="./roundcorner"
                                                    text="Button"
                                                    value="true"/>
                                            </items>
                                        </portalbutton>
                                        <navigationlg
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.navigation.large.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <level1bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.level1.item.background.label"
                                                    name="./headerNaviL1ItemBgColor"/>
                                                <level1text
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.level1.item.text.label"
                                                    name="./headerNaviL1ItemTextColor"/>
                                                <level1textactivehover
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.level1.item.text.active.hover.label"
                                                    name="./headerNaviL1ItemTextActiveHoverColor"/>
                                                <level1bottomline
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.level1.item.bottomline.active.hover.label"
                                                    name="./headerNaviL1ItemBottomLineColor"/>
                                                <flyoutbg
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.flyout.background.label"
                                                    name="./headerNaviLGFlyoutBgColor"/>
                                                <level2text
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.level2.item.text.label"
                                                    name="./headerNaviL2ItemTextColor"/>
                                                <level2textactivehover
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.level2.item.text.active.hover.label"
                                                    name="./headerNaviL2ItemTextActiveHoverColor"/>
                                                <level3text
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.level3.item.text.label"
                                                    name="./headerNaviL3ItemTextColor"/>
                                                <level3textactivehover
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.level3.item.text.active.hover.label"
                                                    name="./headerNaviL3ItemTextActiveHoverColor"/>
                                                <teasertitle
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.teaser.title.label"
                                                    name="./headerNaviTeaserTitleColor"/>
                                                <teasertext
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.teaser.text.label"
                                                    name="./headerNaviTeaserTextColor"/>
                                                <hline
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.hline.label"
                                                    name="./headerNaviHLineColor"/>
                                                <vline
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.vline.label"
                                                    name="./headerNaviVLineColor"/>
                                                <teaserlinkvariation
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.teaser.link.variation.label"
                                                    name="./headerNaviTeaserLinkVariation">
                                                    <datasource
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="/bin/optionsProvider"
                                                        propName="linkColorConfig"/>
                                                </teaserlinkvariation>
                                                <doorpagelinkvariation
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.doorpage.link.variation.label"
                                                    name="./headerNaviDoorpageLinkVariation">
                                                    <datasource
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="/bin/optionsProvider"
                                                        propName="linkColorConfig"/>
                                                </doorpagelinkvariation>
                                                <faqlinkvariation
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.faq.link.variation.label"
                                                    name="./headerNaviFaqLinkVariation">
                                                    <datasource
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="/bin/optionsProvider"
                                                        propName="linkColorConfig"/>
                                                </faqlinkvariation>
                                            </items>
                                        </navigationlg>
                                        <navigationms
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.navigation.medium.small.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <flyoutbg
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.flyout.background.label"
                                                    name="./headerNaviMSFlyoutBgColor"/>
                                                <flyouttext
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.flyout.text.label"
                                                    name="./headerNaviMSFlyoutTextColor"/>
                                                <flyouticons
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.flyout.icon.label"
                                                    name="./headerNaviMSFlyoutIconColor"/>
                                                <flyoutlinkvariation
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.flyout.link.variation.label"
                                                    name="./headerNaviMSFlyoutLinkVariation">
                                                    <datasource
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="/bin/optionsProvider"
                                                        propName="linkColorConfig"/>
                                                </flyoutlinkvariation>
                                                <buttonvariation
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.button.variation.label"
                                                    name="./headerNaviMSButtonVariation">
                                                    <datasource
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="/bin/optionsProvider"
                                                        propName="buttonColorConfig"/>
                                                </buttonvariation>
                                                <hvline
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.horizontal.vertical.line.label"
                                                    name="./headerNaviMSHVLineColor"/>
                                                <hvtransparent
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.navi.horizontal.vertical.transparent.label"
                                                    name="./headerNaviMSHVTransparentColor"/>
                                                <metanavflyoutbg
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.metanavi.flyout.background.label"
                                                    name="./headerMetaNaviMSFlyoutBgColor"/>
                                                <metanavflyouttext
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.metanavi.flyout.text.label"
                                                    name="./headerMetaNaviMSFlyoutTextColor"/>
                                            </items>
                                        </navigationms>
                                        <languageswitch
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.languageswitch"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <labelcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.label"
                                                    name="./langradioLabel"/>
                                                <labelactiveiconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelctiveicon"
                                                    name="./langradioLabelActiveIcon"/>
                                            </items>
                                        </languageswitch>
                                    </items>
                                </headerconfig>
                                <breadcrumbconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.breadcrumb.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <text
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                            name="./bcTextColor"/>
                                        <arrow
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.arrow.label"
                                            name="./bcArrowColor"/>
                                        <texthover
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.texthover.label"
                                            name="./bcTextHoverColor"/>
                                        <textactive
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.textactive.label"
                                            name="./bcTextActiveColor"/>
                                        <line
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.line.label"
                                            name="./bcLineColor"/>
                                    </items>
                                </breadcrumbconfig>
                                <buttonconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.button.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <column
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/container">
                                            <items jcr:primaryType="nt:unstructured">
                                                <links
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                    fieldLabel="revu.global.components.linklist.dialog.links">
                                                    <field
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                                        acs-commons-nested=""
                                                        name="./buttonColorConfig">
                                                        <items jcr:primaryType="nt:unstructured">
                                                            <column
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                                <items jcr:primaryType="nt:unstructured">
                                                                    <combination
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.variation.label"
                                                                        name="./variationName"/>
                                                                    <bgcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                                        name="./bgColor"/>
                                                                    <outlinecolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.outline.label"
                                                                        name="./outlineColor"/>
                                                                    <textcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                                                        name="./textColor"/>
                                                                    <iconcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
                                                                        name="./iconColor"/>
                                                                    <bghovercolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.hover.label"
                                                                        name="./bgHoverColor"/>
                                                                    <outlinehovercolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.outline.hover.label"
                                                                        name="./outlineHoverColor"/>
                                                                    <texthovercolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.texthover.label"
                                                                        name="./textHoverColor"/>
                                                                    <iconhovercolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.hover.label"
                                                                        name="./iconHoverColor"/>
                                                                </items>
                                                            </column>
                                                        </items>
                                                    </field>
                                                </links>
                                            </items>
                                        </column>
                                    </items>
                                </buttonconfig>
                                <linkconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.link.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <column
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/foundation/container">
                                            <items jcr:primaryType="nt:unstructured">
                                                <links
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                    fieldLabel="revu.global.components.teaserlist.dialog.teaser">
                                                    <field
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                                        acs-commons-nested=""
                                                        name="./linkColorConfig">
                                                        <layout
                                                            jcr:primaryType="nt:unstructured"
                                                            sling:resourceType="granite/ui/components/foundation/layouts/fixedcolumns"
                                                            method="absolute"/>
                                                        <items jcr:primaryType="nt:unstructured">
                                                            <column
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/foundation/container">
                                                                <items jcr:primaryType="nt:unstructured">
                                                                    <combination
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.variation.label"
                                                                        name="./variationName"/>
                                                                    <textcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                                                        name="./textColor"/>
                                                                    <iconcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
                                                                        name="./iconColor"/>
                                                                    <texthovercolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.texthover.label"
                                                                        name="./textHoverColor"/>
                                                                    <iconhovercolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.hover.label"
                                                                        name="./iconHoverColor"/>
                                                                </items>
                                                            </column>
                                                        </items>
                                                    </field>
                                                </links>
                                            </items>
                                        </column>
                                    </items>
                                </linkconfig>
                                <footerconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.footer.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <footercta
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.footercta.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                    name="./footerctaBgColor"/>
                                                <titlecolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.headline.label"
                                                    name="./footerctaTitleColor"/>
                                                <text
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                                    name="./footerctaTextColor"/>
                                                <hseperator
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.hseperator.label"
                                                    name="./footerctaHSeperatorColor"/>
                                                <vseperator
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.vseperator.label"
                                                    name="./footerctaVSeperatorColor"/>
                                                <iconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
                                                    name="./footerctaIconColor"/>
                                                <iconhover
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.hover.label"
                                                    name="./footerctaIconHoverColor"/>
                                                <buttonvariation
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.button.variation.label"
                                                    name="./footerctaButtonVariation">
                                                    <datasource
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="/bin/optionsProvider"
                                                        propName="buttonColorConfig"/>
                                                </buttonvariation>
                                                <chatbuttonbgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.chatbutton.background.label"
                                                    name="./footerctaChatButtonBgColor"/>
                                                <chatbuttononcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.chatbutton.on.label"
                                                    name="./footerctaChatButtonOnColor"/>
                                                <chatbuttonoffcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.chatbutton.off.label"
                                                    name="./footerctaChatButtonOffColor"/>
                                                <chatbuttontextcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.chatbutton.text.label"
                                                    name="./footerctaChatButtonTextColor"/>
                                            </items>
                                        </footercta>
                                        <footernavi
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.footernavi.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                    name="./footerNaviBgColor"/>
                                                <titlecolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.headline.label"
                                                    name="./footerNaviTitleColor"/>
                                                <iconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
                                                    name="./footerNaviIconColor"/>
                                                <iconhover
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.hover.label"
                                                    name="./footerNaviIconHoverColor"/>
                                                <seperator
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.seperator.label"
                                                    name="./footerNaviSeperatorColor"/>
                                                <linkvariation
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.link.variation.label"
                                                    name="./footerNaviLinkVariation">
                                                    <datasource
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="/bin/optionsProvider"
                                                        propName="linkColorConfig"/>
                                                </linkvariation>
                                            </items>
                                        </footernavi>
                                        <footermeta
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.footermeta.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                    name="./footerMetaBgColor"/>
                                                <linkvariation
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.link.variation.label"
                                                    name="./footerMetaLinkVariation">
                                                    <datasource
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="/bin/optionsProvider"
                                                        propName="linkColorConfig"/>
                                                </linkvariation>
                                            </items>
                                        </footermeta>
                                    </items>
                                </footerconfig>
                                <cookiedisclaimerconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.cookiedisclaimer.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <background
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                            name="./cookieDisclaimerBackgroundColor"/>
                                        <headline
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.headline.label"
                                            name="./cookieDisclaimerHeadlineColor"/>
                                        <text
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                            name="./cookieDisclaimerTextColor"/>
                                        <link
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.link.label"
                                            name="./cookieDisclaimerLinkColor"/>
                                        <linkHover
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.linkhover.label"
                                            name="./cookieDisclaimerLinkHoverColor"/>
                                        <buttonvariation
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.button.variation.label"
                                            name="./cookieDisclaimerButtonVariation">
                                            <datasource
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="/bin/optionsProvider"
                                                propName="buttonColorConfig"/>
                                        </buttonvariation>
                                    </items>
                                </cookiedisclaimerconfig>
                                <gridbackgroundconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="Grid Background Config"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <gridbackgroundcolorconfig
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                            class="full-width"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.title">
                                            <field
                                                cq:showOnCreate="{Boolean}true"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                                acs-commons-nested=""
                                                name="./gridBackgroundColorConfig">
                                                <items jcr:primaryType="nt:unstructured">
                                                    <column
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                                        <items jcr:primaryType="nt:unstructured">
                                                            <variationName
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.variation.label"
                                                                name="./variationName"/>
                                                            <backgroundColor
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="Background Color"
                                                                name="./gridBackgroundColor"/>
                                                        </items>
                                                    </column>
                                                </items>
                                            </field>
                                        </gridbackgroundcolorconfig>
                                    </items>
                                </gridbackgroundconfig>
                                <teaserconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.teaser.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <iconteaser
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.iconteaser.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                    name="./iconTeaserBgColor"/>
                                                <iconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
                                                    name="./iconTeaserIconColor"/>
                                                <titlecolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.headline.label"
                                                    name="./iconTeaserTitleColor"/>
                                                <outlinecolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.outline.label"
                                                    name="./iconTeaserOutlineColor"/>
                                                <linkvariation
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.link.variation.label"
                                                    name="./iconTeaserLinkVariation">
                                                    <datasource
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="/bin/optionsProvider"
                                                        propName="linkColorConfig"/>
                                                </linkvariation>
                                            </items>
                                        </iconteaser>
                                        <relatedcontentteaser
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.relatedcontentteaser.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <headlinecolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.headline.label"
                                                    name="./relatedContentTeaserHeadlineColor"/>
                                            </items>
                                        </relatedcontentteaser>
                                        <teaserglobal
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.teaser.global.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <teaser
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                    class="full-width"
                                                    fieldLabel="revu.global.components.linklist.dialog.links">
                                                    <field
                                                        cq:showOnCreate="{Boolean}true"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                                        acs-commons-nested=""
                                                        name="./teaserColorConfig">
                                                        <items jcr:primaryType="nt:unstructured">
                                                            <column
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                                <items jcr:primaryType="nt:unstructured">
                                                                    <combination
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.variation.label"
                                                                        name="./variationName"/>
                                                                    <bgcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                                        name="./bgColor"/>
                                                                    <titlecolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.headline.label"
                                                                        name="./titleColor"/>
                                                                    <linecolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.line.label"
                                                                        name="./lineColor"/>
                                                                    <textcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                                                        name="./textColor"/>
                                                                    <buttonvariation
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.button.variation.label"
                                                                        name="./buttonVariation">
                                                                        <datasource
                                                                            jcr:primaryType="nt:unstructured"
                                                                            sling:resourceType="/bin/optionsProvider"
                                                                            propName="buttonColorConfig"/>
                                                                    </buttonvariation>
                                                                    <linkvariation
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.link.variation.label"
                                                                        name="./linkVariation">
                                                                        <datasource
                                                                            jcr:primaryType="nt:unstructured"
                                                                            sling:resourceType="/bin/optionsProvider"
                                                                            propName="linkColorConfig"/>
                                                                    </linkvariation>
                                                                    <videoiconbgcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.videoicon.bg.label"
                                                                        name="./videoIconBgColor"/>
                                                                    <videoiconcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.videoicon.label"
                                                                        name="./videoIconColor"/>
                                                                </items>
                                                            </column>
                                                        </items>
                                                    </field>
                                                </teaser>
                                            </items>
                                        </teaserglobal>
                                        <teaserlist
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.teaserlist.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <titlecolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.headline.label"
                                                    name="./teaserListTitleColor"/>
                                                <outlinecolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.titleline.label"
                                                    name="./teaserListTitlelineColor"/>
                                                <titlehovercolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.titlehover.label"
                                                    name="./teaserListTitleHoverColor"/>
                                                <buttonvariation
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.button.variation.label"
                                                    name="./teaserListButtonVariation">
                                                    <datasource
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="/bin/optionsProvider"
                                                        propName="buttonColorConfig"/>
                                                </buttonvariation>
                                            </items>
                                        </teaserlist>
                                        <teaserglobalv2
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.teaser.globalv2.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <teaser
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                    class="full-width"
                                                    fieldLabel="revu.global.components.linklist.dialog.links">
                                                    <field
                                                        cq:showOnCreate="{Boolean}true"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                                        acs-commons-nested=""
                                                        name="./teaserv2ColorConfig">
                                                        <items jcr:primaryType="nt:unstructured">
                                                            <column
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                                <items jcr:primaryType="nt:unstructured">
                                                                    <combination
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.variation.label"
                                                                        name="./variationName"/>
                                                                    <bgcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                                        name="./bgColor"/>
                                                                    <titlecolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.headline.label"
                                                                        name="./titleColor"/>
                                                                    <textcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                                                        name="./textColor"/>
                                                                    <buttonvariation
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.button.variation.label"
                                                                        name="./buttonVariation">
                                                                        <datasource
                                                                            jcr:primaryType="nt:unstructured"
                                                                            sling:resourceType="/bin/optionsProvider"
                                                                            propName="buttonColorConfig"/>
                                                                    </buttonvariation>
                                                                    <linkvariation
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.link.variation.label"
                                                                        name="./linkVariation">
                                                                        <datasource
                                                                            jcr:primaryType="nt:unstructured"
                                                                            sling:resourceType="/bin/optionsProvider"
                                                                            propName="linkColorConfig"/>
                                                                    </linkvariation>
                                                                </items>
                                                            </column>
                                                        </items>
                                                    </field>
                                                </teaser>
                                            </items>
                                        </teaserglobalv2>
                                    </items>
                                </teaserconfig>
                                <sliderstageconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.sliderstage.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <background
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                            name="./sliderStageBgColor"/>
                                        <text
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                            name="./sliderStageTextColor"/>
                                        <overlay
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.overlay.label"
                                            name="./sliderStageOverlayColor"/>
                                        <slider
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                            class="full-width"
                                            fieldLabel="revu.global.components.linklist.dialog.links">
                                            <field
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                                acs-commons-nested=""
                                                name="./sliderStageColorConfig">
                                                <items jcr:primaryType="nt:unstructured">
                                                    <column
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                                        <items jcr:primaryType="nt:unstructured">
                                                            <combination
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.variation.label"
                                                                name="./variationName"/>
                                                            <bgactive
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.sliderstage.bg.active"
                                                                name="./active"/>
                                                            <bgloaderactive
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.sliderstage.bg.active.loader"
                                                                name="./activeLoader"/>
                                                            <bgatextactive
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.sliderstage.bg.active.text"
                                                                name="./activeText"/>
                                                            <buttonvariation
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.button.variation.label"
                                                                name="./buttonVariation">
                                                                <datasource
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="/bin/optionsProvider"
                                                                    propName="buttonColorConfig"/>
                                                            </buttonvariation>
                                                        </items>
                                                    </column>
                                                </items>
                                            </field>
                                        </slider>
                                    </items>
                                </sliderstageconfig>
                                <staticstageconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.staticstage.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <stage
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                            class="full-width"
                                            fieldLabel="revu.global.components.linklist.dialog.links">
                                            <field
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                                acs-commons-nested=""
                                                name="./staticStageConfig">
                                                <items jcr:primaryType="nt:unstructured">
                                                    <column
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                                        <items jcr:primaryType="nt:unstructured">
                                                            <combination
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.variation.label"
                                                                name="./variationName"/>
                                                            <bgcolor
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                                name="./bgColor"/>
                                                            <titlecolor
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.headline.label"
                                                                name="./title"/>
                                                            <textcolor
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                                                name="./text"/>
                                                            <buttonvariation
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.button.variation.label"
                                                                name="./buttonVariation">
                                                                <datasource
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="/bin/optionsProvider"
                                                                    propName="buttonColorConfig"/>
                                                            </buttonvariation>
                                                            <linkvariation
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.link.variation.label"
                                                                name="./linkVariation">
                                                                <datasource
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="/bin/optionsProvider"
                                                                    propName="linkColorConfig"/>
                                                            </linkvariation>
                                                        </items>
                                                    </column>
                                                </items>
                                            </field>
                                        </stage>
                                    </items>
                                </staticstageconfig>
                                <staticstagev2_config
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.staticstagev2.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <stage
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                            class="full-width"
                                            fieldLabel="revu.global.components.linklist.dialog.links">
                                            <field
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                                acs-commons-nested=""
                                                name="./staticStagev2Config">
                                                <items jcr:primaryType="nt:unstructured">
                                                    <column
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                                        <items jcr:primaryType="nt:unstructured">
                                                            <combination
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.variation.label"
                                                                name="./variationName"/>
                                                            <bgcolor
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                                name="./bgColor"/>
                                                            <titlecolor
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.headline.label"
                                                                name="./title"/>
                                                            <textcolor
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                                                name="./text"/>
                                                            <buttonvariation
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.button.variation.label"
                                                                name="./buttonVariation">
                                                                <datasource
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="/bin/optionsProvider"
                                                                    propName="buttonColorConfig"/>
                                                            </buttonvariation>
                                                        </items>
                                                    </column>
                                                </items>
                                            </field>
                                        </stage>
                                    </items>
                                </staticstagev2_config>
                                <expandablesconfig
                                    cq:showOnCreate="{Boolean}true"
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.expandables.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <title
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.headline.label"
                                            name="./expandablesTitleColor"/>
                                        <item_title_closed
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.expandable.item.title.closed.label"
                                            name="./expandableItemTitleColorClosed"/>
                                        <item_title_open
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.expandable.item.title.open.label"
                                            name="./expandableItemTitleColorOpen"/>
                                        <separator
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.seperator.label"
                                            name="./expandablesSeparatorColor"/>
                                        <expandables
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.expandable.title">
                                            <field
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                                acs-commons-nested=""
                                                name="./expandableColorConfig">
                                                <layout
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/foundation/layouts/fixedcolumns"
                                                    method="absolute"/>
                                                <items jcr:primaryType="nt:unstructured">
                                                    <column
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/foundation/container">
                                                        <items jcr:primaryType="nt:unstructured">
                                                            <variationName
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.variation.label"
                                                                name="./variationName"/>
                                                            <item_title_closed
											                    jcr:primaryType="cq:Widget"
											                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.expandable.item.title.closed.label"
											                    name="./expandableItemTitleColorClosedItem"
											                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"/>
											                <item_title_open
											                    jcr:primaryType="cq:Widget"
											                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.expandable.item.title.open.label"
											                    name="./expandableItemTitleColorOpenItem"
											                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"/>
                                                            <headlineExpandable
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.expandable.headline.label"
                                                                name="./expandableTitle"/>
                                                            <expandablepanel
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.expandable.text.label"
                                                                name="./expandableTextColor"/>
                                                            <linkExpandable
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.expandable.link.label"
                                                                name="./expandableLink"/>
                                                            <itemcolor
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.expandable.item.label"
                                                                name="./expandableColor"/>
                                                        </items>
                                                    </column>
                                                </items>
                                            </field>
                                        </expandables>
                                    </items>
                                </expandablesconfig>
                                <bulletpointiconconfig
                                    cq:showOnCreate="{Boolean}true"
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.bulletpointicon"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <bulletpoints
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.bulletpointicon">
                                            <field
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                                acs-commons-nested=""
                                                name="./bulletPointIconColorConfig">
                                                <layout
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/foundation/layouts/fixedcolumns"
                                                    method="absolute"/>
                                                <items jcr:primaryType="nt:unstructured">
                                                    <column
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/foundation/container">
                                                        <items jcr:primaryType="nt:unstructured">
                                                            <variationName
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.variation.label"
                                                                name="./variationName"/>
                                                            <circle
											                    jcr:primaryType="cq:Widget"
											                    fieldLabel="revu.global.templates.projectrootpage.dialog.bulletpointicon.circle"
											                    name="./circle"
											                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"/>
											                <icon
											                    jcr:primaryType="cq:Widget"
											                    fieldLabel="revu.global.templates.projectrootpage.dialog.bulletpointicon.icon"
											                    name="./icon"
											                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"/>
                                                        </items>
                                                    </column>
                                                </items>
                                            </field>
                                        </bulletpoints>
                                    </items>
                                </bulletpointiconconfig>
                                <formfieldinfoconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="Form field Info Config"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <formfieldinfocolorconfig
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                            class="full-width"
                                            fieldLabel="Form field Info Color Config">
                                            <field
                                                cq:showOnCreate="{Boolean}true"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                                acs-commons-nested=""
                                                name="./formfieldinfoColorConfig">
                                                <items jcr:primaryType="nt:unstructured">
                                                    <column
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                                        <items jcr:primaryType="nt:unstructured">
                                                            <combination
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.variation.label"
                                                                name="./variationName"/>
                                                            <formfieldinfobgcolor
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="Form field Info Background Color"
                                                                name="./formfieldinfobgColor"/>
                                                            <formfieldinfotxtcolor
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="Form field Info Text Color"
                                                                name="./formfieldinfotxtColor"/>
                                                            <formfieldinfoiconcolor
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="Form field Info Icon Color"
                                                                name="./formfieldinfoiconColor"/>
                                                        </items>
                                                    </column>
                                                </items>
                                            </field>
                                        </formfieldinfocolorconfig>
                                    </items>
                                </formfieldinfoconfig>
                                <downloadconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.download.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <title
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.title.label"
                                            name="./dwTitle"/>
                                        <titleline
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.titleline.label"
                                            name="./dwTitleLine"/>
                                        <icon
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
                                            name="./dwIcon"/>
                                        <separator
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.separator.label"
                                            name="./dwSeparator"/>
                                        <linkvariation
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.link.variation.label"
                                            name="./dwLinkVariation">
                                            <datasource
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="/bin/optionsProvider"
                                                propName="linkColorConfig"/>
                                        </linkvariation>
                                    </items>
                                </downloadconfig>
                                <slidercontentconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.contentslider.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <background
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                            name="./contentSliderBgColor"/>
                                        <title
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.title.label"
                                            name="./contentSliderTitleColor"/>
                                        <text
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                            name="./contentSliderTextColor"/>
                                        <countnumbers
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.contentslider.countnumbers"
                                            name="./contentSliderCountNumbersColor"/>
                                        <countnumbersactive
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.contentslider.countnumbers.active"
                                            name="./contentSliderCountNumbersActiveColor"/>
                                        <controls
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.contentslider.controls"
                                            name="./contentSliderControlsColor"/>
                                        <buttonvariation
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.button.variation.label"
                                            name="./contentSliderButtonVariation">
                                            <datasource
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="/bin/optionsProvider"
                                                propName="buttonColorConfig"/>
                                        </buttonvariation>
                                    </items>
                                </slidercontentconfig>
                                <contactmoduleconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="Contact Modul"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <title
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="Title"
                                            name="./contactModulTitle"/>
                                        <titleLine
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="Title-Line"
                                            name="./contactModulTitleLine"/>
                                        <subtitle
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="subtitle"
                                            name="./contactModulSubtitle"/>
                                        <text
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="Text"
                                            name="./contactModulText"/>
                                        <seperator
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="Seperator"
                                            name="./contactModulSeperator"/>
                                        <linkvariation
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.link.variation.label"
                                            name="./contactModulLinkVariation">
                                            <datasource
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="/bin/optionsProvider"
                                                propName="linkColorConfig"/>
                                        </linkvariation>
                                    </items>
                                </contactmoduleconfig>
                                <tilesconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.tiles.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <newsteaser
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.newsteaser.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <newsteaser
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                    class="full-width"
                                                    fieldLabel="revu.global.components.linklist.dialog.links">
                                                    <field
                                                        cq:showOnCreate="{Boolean}true"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                                        acs-commons-nested=""
                                                        name="./newsTeaserColorConfig">
                                                        <items jcr:primaryType="nt:unstructured">
                                                            <column
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                                <items jcr:primaryType="nt:unstructured">
                                                                    <combination
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.variation.label"
                                                                        name="./variationName"/>
                                                                    <bgcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                                        name="./bgColor"/>
                                                                    <titlecolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.headline.label"
                                                                        name="./titleColor"/>
                                                                    <linecolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.line.label"
                                                                        name="./lineColor"/>
                                                                    <linkcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.newsteaser.link.label"
                                                                        name="./linkColor"/>
                                                                    <datecolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.date.label"
                                                                        name="./dateColor"/>
                                                                    <textcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                                                        name="./textColor"/>
                                                                </items>
                                                            </column>
                                                        </items>
                                                    </field>
                                                </newsteaser>
                                            </items>
                                        </newsteaser>
                                        <verticaltileteaser
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.verticaltileteaser.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <verticalteaser
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                    class="full-width"
                                                    fieldLabel="revu.global.components.linklist.dialog.links">
                                                    <field
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                                        acs-commons-nested=""
                                                        name="./verticalTileTeaserConfig">
                                                        <items jcr:primaryType="nt:unstructured">
                                                            <column
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                                <items jcr:primaryType="nt:unstructured">
                                                                    <combination
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.variation.label"
                                                                        name="./variationName"/>
                                                                    <bgcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                                        name="./bgColor"/>
                                                                    <titlecolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.headline.label"
                                                                        name="./titleColor"/>
                                                                    <textcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                                                        name="./textColor"/>
                                                                    <buttonvariation
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.button.variation.label"
                                                                        name="./buttonVariation">
                                                                        <datasource
                                                                            jcr:primaryType="nt:unstructured"
                                                                            sling:resourceType="/bin/optionsProvider"
                                                                            propName="buttonColorConfig"/>
                                                                    </buttonvariation>
                                                                    <linkvariation
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.link.variation.label"
                                                                        name="./linkVariation">
                                                                        <datasource
                                                                            jcr:primaryType="nt:unstructured"
                                                                            sling:resourceType="/bin/optionsProvider"
                                                                            propName="linkColorConfig"/>
                                                                    </linkvariation>
                                                                </items>
                                                            </column>
                                                        </items>
                                                    </field>
                                                </verticalteaser>
                                            </items>
                                        </verticaltileteaser>
                                        <iconimagetileteaser
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.iconimagetileteaser.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <iconimage
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                    class="full-width"
                                                    fieldLabel="revu.global.components.linklist.dialog.links">
                                                    <field
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                                        acs-commons-nested=""
                                                        name="./iconImageTeaserConfig">
                                                        <items jcr:primaryType="nt:unstructured">
                                                            <column
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                                <items jcr:primaryType="nt:unstructured">
                                                                    <combination
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.variation.label"
                                                                        name="./variationName"/>
                                                                    <bgcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                                        name="./bgColor"/>
                                                                    <titlecolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.headline.label"
                                                                        name="./titleColor"/>
                                                                    <linecolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.line.label"
                                                                        name="./lineColor"/>
                                                                    <subtitlecolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.subtitle.label"
                                                                        name="./subtitleColor"/>
                                                                    <outlinecolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.outline.label"
                                                                        name="./iconOutlineColor"/>
                                                                    <icon
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
                                                                        name="./iconColor"/>
                                                                    <gradient
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.gradient.label"
                                                                        name="./gradientColor"/>
                                                                </items>
                                                            </column>
                                                        </items>
                                                    </field>
                                                </iconimage>
                                            </items>
                                        </iconimagetileteaser>
                                        <iconimagetileteaserv2
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.iconimagetileteaserv2.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <iconimage
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                    class="full-width"
                                                    fieldLabel="revu.global.components.linklist.dialog.links">
                                                    <field
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                                        acs-commons-nested=""
                                                        name="./iconImageTeaserv2Config">
                                                        <items jcr:primaryType="nt:unstructured">
                                                            <column
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                                <items jcr:primaryType="nt:unstructured">
                                                                    <combination
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.variation.label"
                                                                        name="./variationName"/>
                                                                    <bgcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                                        name="./bgColor"/>
                                                                    <titlecolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.headline.label"
                                                                        name="./titleColor"/>
                                                                    <textcolor
                                                                        cq:showOnCreate="{Boolean}true"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                                                        name="./textColor"/>
                                                                </items>
                                                            </column>
                                                        </items>
                                                    </field>
                                                </iconimage>
                                            </items>
                                        </iconimagetileteaserv2>
                                    </items>
                                </tilesconfig>
                                <stagecontentconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="Stage Content"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <title
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="Title"
                                            name="./stageContentTitle"/>
                                        <titleline
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="Title Line"
                                            name="./stageContentTitleLine"/>
                                        <label
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="Label"
                                            name="./stageContentLabel"/>
                                    </items>
                                </stagecontentconfig>
                                <imagevideoconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.imagevideo.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <buttonvariation
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.button.variation.label"
                                            name="./imageVideoButtonVariation">
                                            <datasource
                                                cq:showOnCreate="{Boolean}true"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="/bin/optionsProvider"
                                                propName="buttonColorConfig"/>
                                        </buttonvariation>
                                    </items>
                                </imagevideoconfig>
                                <tabsconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.tabs.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <text
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                            name="./tabsTextColor"/>
                                        <textHoverActive
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.active.hover.label"
                                            name="./tabsTextHoverActiveColor"/>
                                        <line
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.line.label"
                                            name="./tabsLineColor"/>
                                        <lineHoverActive
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.line.active.hover.label"
                                            name="./tabsLineHoverActiveColor"/>
                                    </items>
                                </tabsconfig>
                                <sitemapconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.sitemap.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <firstLevelLinkVariation
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.sitemap.color.firstlevellink.label"
                                            name="./sitemapFirstLevelLinkVariation">
                                            <datasource
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="/bin/optionsProvider"
                                                propName="linkColorConfig"/>
                                        </firstLevelLinkVariation>
                                        <otherLevelsLinkVariation
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.sitemap.color.otherlevellink.label"
                                            name="./sitemapOtherLevelsLinkVariation">
                                            <datasource
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="/bin/optionsProvider"
                                                propName="linkColorConfig"/>
                                        </otherLevelsLinkVariation>
                                        <toggleLevel4LinkVariation
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.sitemap.color.togglelevel4link.label"
                                            name="./sitemapToggleLevel4LinkVariation">
                                            <datasource
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="/bin/optionsProvider"
                                                propName="linkColorConfig"/>
                                        </toggleLevel4LinkVariation>
                                    </items>
                                </sitemapconfig>
                                <imagegalleryconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.imagegallery.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <background
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                            name="./galleryBackgroundColor"/>
                                        <closeicon
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.closeicon.label"
                                            name="./galleryCloseIconColor"/>
                                        <downloadlink
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.downloadlink.label"
                                            name="./galleryDownloadLinkColor"/>
                                        <progressbarbg
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.progressbar.background.label"
                                            name="./galleryProgressbarBackgroundColor"/>
                                        <progressbar
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.progressbar.label"
                                            name="./galleryProgressbarColor"/>
                                        <text
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                            name="./galleryTextColor"/>
                                        <shareicon
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.shareicon.label"
                                            name="./galleryShareIconColor"/>
                                        <shareiconhover
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.shareicon.hover.label"
                                            name="./galleryShareIconHoverColor"/>
                                        <teasercountlabel
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.teasercountlabel.label"
                                            name="./galleryTeaserCountLabelColor"/>
                                    </items>
                                </imagegalleryconfig>
                                <sidebarcontactteaser
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.sidebarcontactteaser.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <background
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                            name="./sidebarcontactteaserBackgroundColor"/>
                                        <backgroundshadow
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.shadow.label"
                                            name="./sidebarcontactteaserBackgroundShadowColor"/>
                                        <icons
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
                                            name="./sidebarcontactteaserIconsColor"/>
                                        <tooltipbackground
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.tooltip.background.label"
                                            name="./sidebarcontactteaserTooltipBackgroundColor"/>
                                        <tooltiptext
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.tooltip.text.label"
                                            name="./sidebarcontactteaserTooltipTextColor"/>
                                    </items>
                                </sidebarcontactteaser>
                                <formconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.form.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <formhint
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.formhint.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                    name="./formhintBgColor"/>
                                                <textcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                                    name="./formhintTextColor"/>
                                            </items>
                                        </formhint>
                                        <formhinterror
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.formhinterror.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                    name="./formhintErrorBgColor"/>
                                                <textcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                                    name="./formhintErrorTextColor"/>
                                            </items>
                                        </formhinterror>
                                        <forminput
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.forminput.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <labelcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.label"
                                                    name="./inputLabel"/>
                                                <starcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.starcolor.label"
                                                    name="./inputStar"/>
                                                <reqtextcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.reqtext.label"
                                                    name="./inputReqText"/>
                                                <valtextcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.valtext.label"
                                                    name="./inputValText"/>
                                                <valtextdiscolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.valtextdis.label"
                                                    name="./inputValTextDis"/>
                                                <bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.disabled.background.label"
                                                    name="./inputBgColor"/>
                                                <bottomlinecolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomline.label"
                                                    name="./inputBottomLine"/>
                                                <bottomlinefoccolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomlinefoc.label"
                                                    name="./inputBottomLineFoc"/>
                                                <tickiconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.tickicon.label"
                                                    name="./inputTickIcon"/>
                                                <crossiconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.crossicon.label"
                                                    name="./inputCrossIcon"/>
                                                <bottomlineerrcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomlineerr.label"
                                                    name="./inputBottomLineErr"/>
                                            </items>
                                        </forminput>
                                        <formdropdown
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.formdropdown.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                    name="./ddBgColor"/>
                                                <labelcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.label"
                                                    name="./ddLabel"/>
                                                <starcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.starcolor.label"
                                                    name="./ddStar"/>
                                                <reqtextcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.reqtext.label"
                                                    name="./ddReqText"/>
                                                <valtextcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.valtext.label"
                                                    name="./ddValText"/>
                                                <valtextdiscolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.valtextdis.label"
                                                    name="./ddValTextDis"/>
                                                <bottomlinecolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomline.label"
                                                    name="./ddBottomLine"/>
                                                <bottomlinefoccolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomlinefoc.label"
                                                    name="./ddBottomLineFoc"/>
                                                <tickiconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.tickicon.label"
                                                    name="./ddTickIcon"/>
                                                <crossiconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.crossicon.label"
                                                    name="./ddCrossIcon"/>
                                                <bottomlineerrcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomlineerr.label"
                                                    name="./ddBottomLineErr"/>
                                                <entrycolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.entry.label"
                                                    name="./ddEntry"/>
                                                <entryactivecolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.entryactive.label"
                                                    name="./ddEntryActive"/>
                                                <entryseparatorcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.entryseparator.label"
                                                    name="./ddEntrySeparator"/>
                                                <iconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
                                                    name="./ddIcon"/>
                                            </items>
                                        </formdropdown>
                                        <formdatepicker
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.formdatepicker.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                    name="./dpBgColor"/>
                                                <labelcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.label"
                                                    name="./dpLabel"/>
                                                <starcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.starcolor.label"
                                                    name="./dpStar"/>
                                                <reqtextcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.reqtext.label"
                                                    name="./dpReqText"/>
                                                <valtextcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.valtext.label"
                                                    name="./dpValText"/>
                                                <valtextdiscolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.valtextdis.label"
                                                    name="./dpValTextDis"/>
                                                <bottomlinecolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomline.label"
                                                    name="./dpBottomLine"/>
                                                <bottomlinefoccolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomlinefoc.label"
                                                    name="./dpBottomLineFoc"/>
                                                <tickiconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.tickicon.label"
                                                    name="./dpTickIcon"/>
                                                <crossiconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.crossicon.label"
                                                    name="./dpCrossIcon"/>
                                                <bottomlineerrcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomlineerr.label"
                                                    name="./dpBottomLineErr"/>
                                                <iconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
                                                    name="./dpIcon"/>
                                                <openyeartxtcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.openyeartxt.label"
                                                    name="./dpOpenYearTxt"/>
                                                <openmonthtxtcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.openmonthtxt.label"
                                                    name="./dpOpenMonthTxt"/>
                                                <openyeararrowcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.openyeararrow.label"
                                                    name="./dpOpenYearArrow"/>
                                                <openweekdaystxtcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.openweekdaystxt.label"
                                                    name="./dpOpenWeekDaysTxt"/>
                                                <opendaystxtcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.opendaystxt.label"
                                                    name="./dpOpenDaysTxt"/>
                                                <opendaystxtdiscolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.opendaystxtdis.label"
                                                    name="./dpOpenDaysTxtDis"/>
                                                <opendaysactsetxtcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.opendaysactsetxt.label"
                                                    name="./dpOpenDaysActSETxt"/>
                                                <opendaysactsebgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.opendaysactsebg.label"
                                                    name="./dpOpenDaysActSEBg"/>
                                                <opendaysactperiodtxtcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.opendaysactperiodtxt.label"
                                                    name="./dpOpenDaysActPeriodTxt"/>
                                                <opendaysactperiodbgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.opendaysactperiodbg.label"
                                                    name="./dpOpenDaysActPeriodBg"/>
                                                <calendarlinescolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.calendarLines.label"
                                                    name="./dpCalendarLines"/>
                                                <calendarlinesdiscolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.calendarLinesdis.label"
                                                    name="./dpCalendarLinesDis"/>
                                            </items>
                                        </formdatepicker>
                                        <formradio
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.formradio.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                    name="./radioBgColor"/>
                                                <labelcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.label"
                                                    name="./radioLabel"/>
                                                <labeliconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelicon"
                                                    name="./radioLabelIcon"/>
                                                <labelactivecolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelctive"
                                                    name="./radioLabelActive"/>
                                                <labelactiveiconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelctiveicon"
                                                    name="./radioLabelActiveIcon"/>
                                                <labeldisabledcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.labeldisabled"
                                                    name="./radioLabelDisabled"/>
                                                <labelicondisabledcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelicondisabled"
                                                    name="./radioLabelIconDisabled"/>
                                                <checkiconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.checkicon"
                                                    name="./radioCheckIcon"/>
                                                <checkiconcoloractive
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.checkiconActiive"
                                                    name="./radioCheckIconActive"/>
                                            </items>
                                        </formradio>
                                        <formcheckbox
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.formcheckbox.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                    name="./cbBgColor"/>
                                                <labelcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.label"
                                                    name="./cbLabel"/>
                                                <labeliconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelicon"
                                                    name="./cbLabelIcon"/>
                                                <labelactivecolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelctive"
                                                    name="./cbLabelActive"/>
                                                <labelactiveiconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelctiveicon"
                                                    name="./cbLabelActiveIcon"/>
                                                <labeldisabledcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.labeldisabled"
                                                    name="./cbLabelDisabled"/>
                                                <labelicondisabledcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelicondisabled"
                                                    name="./cbLabelIconDisabled"/>
                                                <checkiconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.checkicon"
                                                    name="./cbCheckIcon"/>
                                                <checkiconcoloractive
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.checkiconActiive"
                                                    name="./cbCheckIconActive"/>
                                            </items>
                                        </formcheckbox>
                                        <formswitches
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.formswitches.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                    name="./switchBgColor"/>
                                                <labelcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.label"
                                                    name="./switchLabel"/>
                                                <labelactivecolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelctive"
                                                    name="./switchLabelActive"/>
                                                <labeldisabledcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.labeldisabled"
                                                    name="./switchLabelDisabled"/>
                                                <switchiconoff
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.switchoff.label"
                                                    name="./switchIconOff"/>
                                                <switchiconon
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.switchon.label"
                                                    name="./switchIconOn"/>
                                            </items>
                                        </formswitches>
                                        <formtextarea
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.formtextarea.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <labelcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.label"
                                                    name="./taLabel"/>
                                                <valtextcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.valtext.label"
                                                    name="./taValText"/>
                                                <valtextdiscolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.valtextdis.label"
                                                    name="./taValTextDis"/>
                                               	<bgtextareacolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.disabled.background.label"
                                                    name="./bgtextareacolor"/>
                                                <bottomlinecolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomline.label"
                                                    name="./taBottomLine"/>
                                                <bottomlinefoccolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomlinefoc.label"
                                                    name="./taBottomLineFoc"/>
                                                <bottomlineerrcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomlineerr.label"
                                                    name="./taBottomLineErr"/>
                                            </items>
                                        </formtextarea>
                                        <formpopuphint
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.formspopuphint.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                    name="./popupHintBgColor"/>
                                                <textcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                                    name="./popupHintTextColor"/>
                                                <iconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
                                                    name="./popupHintIconColor"/>
                                            </items>
                                        </formpopuphint>
                                        <formloader
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.formloader.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                    name="./loaderBgColor"/>
                                                <strokecolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.stroke.label"
                                                    name="./loaderStroke"/>
                                            </items>
                                        </formloader>
                                        <formareahint
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.formsareahint.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <bgcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                                    name="./areaHintBgColor"/>
                                                <textcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                                    name="./areaHintTextColor"/>
                                                <iconcolor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
                                                    name="./areaHintIconColor"/>
                                            </items>
                                        </formareahint>
                                    </items>
                                </formconfig>
                                <tableconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.table.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <tableColumnHeadlinesColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.table.columnheadlines.label"
                                            name="./tableColumnHeadlinesColor"/>
                                        <tableIconsColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
                                            name="./tableIconsColor"/>
                                        <tableTextColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                            name="./tableTextColor"/>
                                        <tableHeaderLineColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.table.headerline.label"
                                            name="./tableHeaderLineColor"/>
                                        <tableLinesColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.line.label"
                                            name="./tableLinesColor"/>
                                        <tableLineBackgroundColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.table.linebackground.label"
                                            name="./tableLineBackgroundColor"/>
                                    </items>
                                </tableconfig>
                                <careerconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.career.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <careerChoosedTagTextColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.career.choosedtagtext.label"
                                            name="./careerChoosedTagTextColor"/>
                                        <careerChoosedTagCloseIconBackgroundColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.career.choosedtagcloseiconbg.label"
                                            name="./careerChoosedTagCloseIconBackgroundColor"/>
                                        <careerChoosedTagCloseIconColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.career.choosedtagcloseicon.label"
                                            name="./careerChoosedTagCloseIconColor"/>
                                        <careerTagTextColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.career.tagtext.label"
                                            name="./careerTagTextColor"/>
                                        <careerTagBackgroundColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.career.tagbackground.label"
                                            name="./careerTagBackgroundColor"/>
                                        <careerTagBorderColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.career.tagborder.label"
                                            name="./careerTagBorderColor"/>
                                        <careerTagBackgroundActiveColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.career.tagbackgroundactive.label"
                                            name="./careerTagBackgroundActiveColor"/>
                                        <careerTagTextActiveColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.career.tagtextactive.label"
                                            name="./careerTagTextActiveColor"/>
                                        <careerTagBorderActiveColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.career.tagborderactive.label"
                                            name="./careerTagBorderActiveColor"/>
                                    </items>
                                </careerconfig>
                                <searchconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.search.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <headersearch
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.search.header.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <searchHeaderInputTextColor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.search.inputtext.label"
                                                    name="./searchHeaderInputTextColor"/>
                                                <searchHeaderInputBottomLineColor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.inputbottomline.label"
                                                    name="./searchHeaderInputBottomLineColor"/>
                                                <searchHeaderInputBackgroundColor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.search.inputbackground.label"
                                                    name="./searchHeaderInputBackgroundColor"/>
                                                <searchHeaderSuggestionsBackgroundColor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.search.suggestionsbackground.label"
                                                    name="./searchHeaderSuggestionsBackgroundColor"/>
                                                <searchHeaderSuggestionsTextColor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.search.suggestionstext.label"
                                                    name="./searchHeaderSuggestionsTextColor"/>
                                                <searchHeaderSuggestionsHoverActiveBackgroundColor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.search.suggestionshoveractivebackground.label"
                                                    name="./searchHeaderSuggestionsHoverActiveBackgroundColor"/>
                                                <searchHeaderSuggestionsHoverActiveTextColor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.search.suggestionshoveractivetext.label"
                                                    name="./searchHeaderSuggestionsHoverActiveTextColor"/>
                                                <searchHeaderSuggestionsItemBottomBorderColor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.search.suggestionsitembottomline.label"
                                                    name="./searchHeaderSuggestionsItemBottomBorderColor"/>
                                            </items>
                                        </headersearch>
                                        <searchresults
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.templates.projectrootpage.dialog.color.search.results.title"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <searchResultsInputTextColor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.search.inputtext.label"
                                                    name="./searchResultsInputTextColor"/>
                                                <searchResultsInputBottomLineColor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.inputbottomline.label"
                                                    name="./searchResultsInputBottomLineColor"/>
                                                <searchResultsInputBackgroundColor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.search.inputbackground.label"
                                                    name="./searchResultsInputBackgroundColor"/>
                                                <searchResultsPlaceholderTextColor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.search.placeholdertext.label"
                                                    name="./searchResultsPlaceholderTextColor"/>
                                                <searchResultsSuggestionsBackgroundColor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.search.suggestionsbackground.label"
                                                    name="./searchResultsSuggestionsBackgroundColor"/>
                                                <searchResultsSuggestionsTextColor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.search.suggestionstext.label"
                                                    name="./searchResultsSuggestionsTextColor"/>
                                                <searchResultsSuggestionsHoverActiveBackgroundColor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.search.suggestionshoveractivebackground.label"
                                                    name="./searchResultsSuggestionsHoverActiveBackgroundColor"/>
                                                <searchResultsSuggestionsHoverActiveTextColor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.search.suggestionshoveractivetext.label"
                                                    name="./searchResultsSuggestionsHoverActiveTextColor"/>
                                                <searchResultsSuggestionsItemBottomColor
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.search.suggestionsitembottomline.label"
                                                    name="./searchResultsSuggestionsItemBottomBorderColor"/>
                                            </items>
                                        </searchresults>
                                    </items>
                                </searchconfig>
                                <modalconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.modal.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <modalTitleColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.title.label"
                                            name="./modalTitleColor"/>
                                        <modalTextColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                            name="./modalTextColor"/>
                                        <modalBackgroundColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
                                            name="./modalBackgroundColor"/>
                                        <modalOverlayColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.overlay.label"
                                            name="./modalOverlayColor"/>
                                        <modalCloseButtonBackgroundColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.closebutton.background.label"
                                            name="./modalCloseButtonBackgroundColor"/>
                                        <modalCloseButtonColor
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.closebutton.label"
                                            name="./modalCloseButtonColor"/>
                                        <modalButtonVariation
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.button.variation.label"
                                            name="./modalButtonVariation">
                                            <datasource
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="/bin/optionsProvider"
                                                propName="buttonColorConfig"/>
                                        </modalButtonVariation>
                                    </items>
                                </modalconfig>
                                <chartconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="revu.global.templates.projectrootpage.dialog.color.barchart.title"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <bar1
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.bar.label"
                                            name="./barChartBarColor1"/>
                                        <bar2
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.bar.label"
                                            name="./barChartBarColor2"/>
                                        <line
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.line.label"
                                            name="./barChartLineColor"/>
                                        <text
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                            fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
                                            name="./barChartTexteColor"/>
                                    </items>
                                </chartconfig>
                                <adaptiveconfig
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="Adaptive Image Map Config"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <adaptivecolorconfig
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                            class="full-width"
                                            fieldLabel="Adaptive Image Map Color Config">
                                            <field
                                                cq:showOnCreate="{Boolean}true"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                                acs-commons-nested=""
                                                name="./adaptiveimageColorConfig">
                                                <items jcr:primaryType="nt:unstructured">
                                                    <column
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                                        <items jcr:primaryType="nt:unstructured">
                                                            <combination
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.variation.label"
                                                                name="./variationName"/>
                                                            <adaptivebgcolor
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="Adaptive Image Map Background Color"
                                                                name="./adaptiveimagebgColor"/>
                                                            <adaptivetitlecolor
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="Adaptive Map Text background Color"
                                                                name="./adaptiveimagetitleColor"/>
                                                            <Copy_x0020_of_x0020_adaptivetitlecolor
                                                                cq:showOnCreate="{Boolean}true"
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                fieldLabel="Adaptive Map Text background Color"
                                                                name="./adaptiveimagetitleColor"/>
                                                        </items>
                                                    </column>
                                                </items>
                                            </field>
                                        </adaptivecolorconfig>
                                    </items>
                                </adaptiveconfig>
                                <boxshadow
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="Hide Box Shadows"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <teasers
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                            fieldDescription="Icon-Teaser, Banner-Teaser, Horizontaler Video-Teaser"
                                            name="./teasers"
                                            text="Teasers"
                                            value="true"/>
                                        <tiles
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                            fieldDescription="Related-Content-Teaser, Tiles-Teaser, social-media-posts"
                                            name="./tiles"
                                            text="Tiles"
                                            value="true"/>
                                        <gallery
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                            fieldDescription="Image-Gallery, Teaser-List"
                                            name="./gallery"
                                            text="Gallery"
                                            value="true"/>
                                        <content
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                            fieldDescription="Netzbereich, Formular"
                                            name="./content"
                                            text="Content"
                                            value="true"/>
                                        <common
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                            name="./common"
                                            text="Common"
                                            value="true"/>
                                        <contentslider
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                            name="./contentslider"
                                            text="Contentslider"
                                            value="true"/>
                                        <imgshadow
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                            name="./imgshadow"
                                            text="Image Shadow"
                                            value="true"/>
                                        <decoline
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                            name="./decoline"
                                            text="Decoline"
                                            value="true"/>
                                    </items>
                                </boxshadow>
                            </items>
                        </column>
                    </items>
                </colorconfig>
            </items>
        </tabs>
    </items>
</jcr:root>
