(function(document, $, ns) {
    "use strict";
    var temp;
    var globalThis;
    
    // listen for dialog injection
    $(document).on("dialog-ready", function (e) {
      $(".showhide").each(function () {
          //USER STORY DIG_ATT-3810
          //Adding an additional description for temporary checkbox (when checked)
          if($(this).context.name === './redirectOption'){            
              var text = CQ.I18n.get('digital.attacker.templates.projectrootpage.temporaryredirect.title');
              $(this).parent().css("width","100%");
              $(this).parent().append("<span class='coral-Checkbox-description showhide-target' data-showhide-target-value='true'>" + text + "</span>");
          }
          showHide($(this));
      });
    });

    // listen for toggle change
    $(document).on("change", ".showhide", function (e) {
      showHide($(this));
    });
   
    //USER STORY DIG_ATT-3810
    //Function that will hide or show temporary message
    function showHide(el) {
      var target = el.data("showhideTarget"),
        value = el.prop("checked") ? el.val() : "";
   
      // hide all targets by default
      $(target).not(".hide").addClass("hide");
   
      // show any targets with a matching target value
      $(target).filter("[data-showhide-target-value=\"" + value + "\"]").removeClass("hide");
    }

    $(document).on("DOMNodeRemoved", function(e)
    {
        if($(e.target).find(".Vanityurlclass").val() === temp){
            temp = null;
        }
    });
    
    $(document).on("keydown",".aliasName, .Vanityurlclass", function(e) {
        if(e.which != 13 && e.which != 9){
            $(".cq-dialog-submit").prop("disabled", true);
            $(".foundation-layout-inline2-item.coral-Button.coral-Button--primary").prop("disabled", true);
        } else{
            if($(".cq-dialog-submit").prop("disabled") == true
                    || $(".foundation-layout-inline2-item.coral-Button.coral-Button--primary").prop("disabled") == true){
                e.preventDefault();
            }
        }
    });

    $(document).on("keyup",".aliasName", function(e) {
        var path = $(this).closest("form.foundation-form")
                .attr("action").replace("/_jcr_content", "");
        var aliasValidationSetter = function(data) {
            if (data) {
            	 if (data.validateProperties.length > 0){
                     $('.aliasName').data('alias-validation', 'false');
                 } else {
                     $('.aliasName').data('alias-validation', 'true');
            }}
            //Enabling dialog submission
            $(".cq-dialog-submit").prop("disabled", false);
            $(".foundation-layout-inline2-item.coral-Button.coral-Button--primary").prop("disabled", false);
        };
        checkAliasDuplicate($(this).val(), path,
                            aliasValidationSetter);
    });

    $(document).on("keyup",".Vanityurlclass", function(e) {
        temp = "unclicked";
        var path = $(this).closest("form.foundation-form")
                .attr("action").replace("/_jcr_content", "");
        temp = $(this).val().toString();
        globalThis = this;
        var vanityValidationSetter = function(data) {

            if (data) {
               if (data.validateProperties.length > 0)
               {

                $(globalThis).attr('data-vanity-validation',
                                   'false');
                ns.ui.helpers
                        .prompt({
                            title : Granite.I18n
                                    .get("Invalid Input"),
                            message : "This vanity is already used by below page. Please use another one."
                                      + data.pagePath + ".html",
        
                            actions : [ {
        
                                id : "OK",
                                text : "OK",
                                className : "coral-Button"
                            } ]
                        });

               }else {   
                   $(globalThis)
                   .attr('data-vanity-validation', 'true');
              }} else {   
                $(globalThis)
                        .attr('data-vanity-validation', 'true');
            }
            //Enabling dialog submission
            $(".cq-dialog-submit").prop("disabled", false);
            $(".foundation-layout-inline2-item.coral-Button.coral-Button--primary").prop("disabled", false);
        };

        checkVanityDuplicate($(this).val(), path,
                             vanityValidationSetter);
    });

    function checkVanityDuplicate(value, path, success) {
        $.ajax({
            method : "GET",
            url : "/bin/eon-foundation/duplicateVanityCheck",
            data : {
                validate:"sling:vanityPath",
                value : value,
                pagePath : path
            },
            success : success
        });
    }

    function checkAliasDuplicate(value, path, success){        
        $.ajax({
              method:"GET",
              url:"/bin/eon-foundation/duplicateAliasCheck",
              data: {
                  aliasProp:"sling:alias",
                  value:value, 
                  pagePath:path
              },
              success: success
          });
    } 


})(document, Granite.$, Granite.author);
