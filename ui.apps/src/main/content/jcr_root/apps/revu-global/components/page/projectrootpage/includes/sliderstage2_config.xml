<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    jcr:primaryType="cq:Panel">
    <items jcr:primaryType="cq:WidgetCollection">
        <fields
            jcr:primaryType="cq:Widget"
            collapsed="{Boolean}true"
            collapsible="{Boolean}true"
            title="Slider Stage V2"
            xtype="dialogfieldset">
            <items jcr:primaryType="cq:WidgetCollection">
                <multifield
                    jcr:primaryType="cq:Widget"
                    fieldLabel="revu.global.templates.projectrootpage.dialog.color.sliderstage.variation"
                    name="./sliderStagev2ColorConfig"
                    xtype="multifield">
                    <fieldConfig
                        jcr:primaryType="nt:unstructured"
                        xtype="dke-multifieldpanel">
                        <items jcr:primaryType="cq:WidgetCollection">
                            <combination
                                jcr:primaryType="cq:Widget"
                                anchor="100%"
                                fieldLabel="revu.global.templates.projectrootpage.dialog.color.variation.label"
                                key="variationName"
                                xtype="textfield"/>
                            <bgcolor
                                jcr:primaryType="cq:Widget"
                                anchor="100%"
                                fieldLabel="BackGround Color"
                                key="bgColor"
                                xtype="textfield"/>
                            <headlinecolr
                                jcr:primaryType="cq:Widget"
                                anchor="100%"
                                fieldLabel="Headline Color"
                                key="headlineColor"
                                xtype="textfield"/>
                            <subheadlinecolr
                                jcr:primaryType="cq:Widget"
                                anchor="100%"
                                fieldLabel="Sub Headline Color"
                                key="subHeadlineColr"
                                xtype="textfield"/>
                            <teaseractive
                                jcr:primaryType="cq:Widget"
                                anchor="100%"
                                fieldLabel="Teaser Active Text Color"
                                key="activeColor"
                                xtype="textfield"/>
                            <teaserinactive
                                jcr:primaryType="cq:Widget"
                                anchor="100%"
                                fieldLabel="Teaser InActive Text Color"
                                key="inactiveColor"
                                xtype="textfield"/>
                            <progressbar
                                jcr:primaryType="cq:Widget"
                                anchor="100%"
                                fieldLabel="Progress Bar Color"
                                key="barColor"
                                xtype="textfield"/>
                            <buttonvariation
                                jcr:primaryType="cq:Widget"
                                fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.button.variation.label"
                                key="buttonVariation"
                                options="$PATH.button-options.json"
                                type="select"
                                xtype="selection"/>
                        </items>
                    </fieldConfig>
                </multifield>
            </items>
        </fields>
    </items>
</jcr:root>
