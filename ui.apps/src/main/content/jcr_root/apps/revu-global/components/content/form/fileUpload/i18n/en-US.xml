<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:mix="http://www.jcp.org/jcr/mix/1.0"
    jcr:language="en"
    jcr:mixinTypes="[mix:language]"
    jcr:primaryType="sling:Folder">
    <revu.global.components.form.fileupload.dialog.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Title"/>
    <revu.global.components.form.fileupload.dialog.filename
            jcr:primaryType="sling:MessageEntry"
            sling:message="File Name"/>
    <revu.global.components.form.fileupload.dialog.filetype
            jcr:primaryType="sling:MessageEntry"
            sling:message="File Type"/>
    <revu.global.components.form.fileupload.dialog.buttontitle
            jcr:primaryType="sling:MessageEntry"
            sling:message="Label upload button"/>
    <revu.global.components.form.fileupload.dialog.uploadlabel
            jcr:primaryType="sling:MessageEntry"
            sling:message="Label for button to add an upload slot"/>
    <revu.global.components.form.fileupload.dialog.numberattach
            jcr:primaryType="sling:MessageEntry"
            sling:message="Number of attachments"/>
    <revu.global.components.form.fileupload.dialog.errormessage
            jcr:primaryType="sling:MessageEntry"
            sling:message="Error Message"/>
    
    <revu.global.components.form.fileupload.dialog.uploadfilename
            jcr:primaryType="sling:MessageEntry"
            sling:message="Label for empty file name"/>
</jcr:root>
