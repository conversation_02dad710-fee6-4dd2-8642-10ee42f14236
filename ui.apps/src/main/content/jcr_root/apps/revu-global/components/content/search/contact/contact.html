<div
 data-sly-test="${!wcmmode.edit}"
 class='react-mini-app'
 data-component='ContactSearch'
 data-application-name='dke-contact-search'
 data-sly-use.model="com.eon.dist.dke.aem.core.models.ContactSearchModel"
 data-props='{
 "requiredMessage": "${properties.requiredMessage @context="attribute"}",
 "hintMessage": "${properties.hintMessage @context="attribute"}",
 "requiredErrorMessage": "${properties.requiredErrorMessage @context="attribute"}",
 "errorHeadline": "${properties.errorHeadline @context="attribute"}",
 "errorCopy": "${properties.errorCopy @context="attribute"}",
 "zip": 
{ "label": "${properties.cipLabel @context="attribute"}", "maxLength": ${properties.cipMaxLength}, "minLength": ${properties.cipMinLength @context="attribute"}, "required": ${properties.cipRequired @context="attribute"}, "errorMessage": "${properties.cipErrorMessage @context="attribute"}", "minLengthErorMessage": "${properties.cipMinLengthErorMessage @context="attribute"}", "name": "zip" } 
,
"contactData": ${model.json @context="unsafe"},
 "searchButton": 
{ "label": "${properties.searchButtonLabel}", "version": "${properties.buttonColor @context="attribute"}", "name": "search", "type": "button" } 
}'
></div>

<div data-sly-test="${wcmmode.edit}" data-sly-unwrap data-sly-resource="${@path='par', resourceType='foundation/components/parsys'}"></div>