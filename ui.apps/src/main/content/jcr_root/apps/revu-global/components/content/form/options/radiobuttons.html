<template
	data-sly-template.input="${@ disable = false, helpMessage, requiredMessage, required, options, classes, title, randomnumber}"
	data-sly-set.effectiveTitle="${title ? title : options.title}"
	data-sly-set.effectiveHelpMessage="${helpMessage ? helpMessage : options.helpMessage}"
	data-sly-set.effectiveRequired="${required ? required : options.required}"
	data-sly-set.effectiveRequiredMessage="${requiredMessage ? requiredMessage : options.requiredMessage}">
  	<sly data-sly-use.optionsid="${'optionscript.js' @ arg=options.name, type='id'}"/>
	<div class="form-group">
		<div>
			<div
				class="form-radio__label form-radio-v1__label form-radio-group__titl_wrapper form-input--mandatory"
				style="padding-left: 0;">
				<span class="form-radio-group__title rich-text rich-text-default">${effectiveTitle}</span>
				<span data-sly-test=${required} class="form-input--required-asterisk form-input__field__required-notifier--active">*</span>
			</div>
			<div
				class="form-radio form-radio-v1 form-radio-v1--label form-radio-v1--mandatory ${required ? 'form-radio--mandatory' : ''} form-options-input__bar"
                data-contnr-ref="${optionsid.value}-${randomnumber}">
				<div data-sly-list.optionItem="${options.items}"
                	 class="form-radio-wrapper form-radio-v1-wrapper"
                     disabled="${optionItem.disabled ? 'disabled' : ''}">
					<div class="form-radio-group form-radio-v1-group">
                    <sly data-sly-use.optionsid3="${'optionscript.js' @ arg=optionItem.text, type='id'}"/>
						<label for="${optionsid3.value}" 
							class="form-radio-v1__label"> 
							<input type="radio" 
                            	   id="${optionsid3.value}" 
                                   name="${options.name}"
							       class="form-radio__input form-radio-v1__input" 
								   tabindex="-1" 
								   value="${optionItem.value}"
                                   data-fldcontainer="${optionsid.value}-${optionsid3.value}-${randomnumber}"
                                   checked="${optionItem.selected ? 'checked' : ''}"
                                   disabled="${optionItem.disabled ? 'disabled' : ''}" >
							<div class="form-radio__icon form-radio-v1__icon">
								<span class="inactive"></span> 
								<span class="active"> 
									<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	                              		<g fill="#0091BB">
		                                 	<path d="M12,24 C5.383,24 0,18.617 0,12 C0,5.383 5.383,0 12,0 C18.617,0 24,5.383 24,12 C24,18.617 18.617,24 12,24 Z M12,2 C6.486,2 2,6.486 2,12 C2,17.514 6.486,22 12,22 C17.514,22 22,17.514 22,12 C22,6.486 17.514,2 12,2 Z"></path>
		                                 	<polyline points="10.834 17 5 10.813 5.89 9.873 10.836 15.119 18.512 7 19.4 7.94 10.834 17"></polyline>
	                              		</g>
	                           		</svg>
								</span> 
								<span>${optionItem.text}</span>
							</div>
						</label>
					</div>
				</div>
			</div>
			<div data-sly-test="${effectiveRequired}"
				class="form-input__message-error">
				<p>${effectiveRequiredMessage}</p>
			</div>
		</div>
         <div data-sly-test="${properties.showinfo}" disabled="${optionItem.disabled ? 'disabled' : ''}" class="form-input__info-icon" id="${randomnumber}">
				<svg xmlns="http://www.w3.org/2000/svg" class="form-input__info-icon--${properties.variationName}" width=30 height=30 viewBox="0 0 54 54">
					<path d="M27,16.19a1.75,1.75,0,1,0,0,3.46,1.75,1.75,0,1,0,0-3.46Z"/>
					<polygon class="form-input__info-icon--${properties.variationName}" points="25.54 37.81 28.46 37.81 28.46 22.15 25.54 22.69 25.54 37.81" />
					<path class="form-input__info-icon--${properties.variationName}" d="M27,9.2A17.8,17.8,0,1,0,44.8,27,17.81,17.81,0,0,0,27,9.2Zm0,33.68A15.88,15.88,0,1,1,42.88,27,15.89,15.89,0,0,1,27,42.88Z" />
				</svg>
			 </div>

         <sly data-sly-test="${options.type.value == 'radio'}">
             <div data-sly-test="${properties.showinfo}" class="form-input__info-wrapper">
                <div class="form-input__info-content form-input__info-content_bg--${properties.variationName} form-input__info-content_tclr--${properties.variationName}" id="${randomNumber}" style="top: 55px;">
                    ${properties.information}
                </div>
             </div>
        </sly>    
	</div>
</template>