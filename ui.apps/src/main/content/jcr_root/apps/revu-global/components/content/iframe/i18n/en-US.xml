<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:mix="http://www.jcp.org/jcr/mix/1.0"
    jcr:language="en"
    jcr:mixinTypes="[mix:language]"
    jcr:primaryType="sling:Folder">
    <revu.global.components.iframe.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Iframe" />    
    <revu.global.components.iframe.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="Iframe Component" />
    <revu.global.components.iframe.options.iframeheightresizing.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Height resizing type" />
    <revu.global.components.iframe.options.iframeheightresizing.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="'easyXDM' requires frame source document to send a message via socket (see http://easyxdm.net). 'MyNewsdesk' requires an account (see http://www.mynewsdesk.com)" />
    <revu.global.components.iframe.options.iframeheightresizing.fixed.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Fixed height" />
    <revu.global.components.iframe.options.iframeheightresizing.easyxdm.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Automatic height (easyXDM)" />
    <revu.global.components.iframe.options.iframeheightresizing.mynewsdesk.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Automatic height (MyNewsdesk)" />
 	<revu.global.components.iframe.options.iframeheightresizing.minikwk.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Automatic height (MiniKWK)" />      
    <revu.global.components.iframe.options.target.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="Please enter the URL to the external web application (frame source). For height resizing type 'Automatic height (MyNewsdesk)' enter url to 'hosted_newsroom.js'" />
    <revu.global.components.iframe.options.target.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Url (iframe source)" />
    <revu.global.components.iframe.options.height.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="Used for height resizing type 'Fixed height'. The default value is 600 pixels." />
    <revu.global.components.iframe.options.height.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Height" />    
    <revu.global.components.iframe.options.accessibilityseofieldset.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Accessibility / SEO" />
    <revu.global.components.iframe.alttext.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="Alternative text is important. Describe the iframe content the shortest way possible" />
    <revu.global.components.iframe.alttext.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Alternative text" />
    <revu.global.components.iframe.solutiondetails
        jcr:primaryType="sling:MessageEntry"
        sling:message="Solution details"/>
    <revu.global.components.iframe.twoclicksolution
        jcr:primaryType="sling:MessageEntry"
        sling:message="Two click solution"/>
</jcr:root>
