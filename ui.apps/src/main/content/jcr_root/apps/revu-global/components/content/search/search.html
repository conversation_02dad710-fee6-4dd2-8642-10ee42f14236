<sly data-sly-use.wcmInit="/libs/wcm/foundation/components/page/initwcm.js"
	 data-sly-use.redirect="${'com.eon.dist.dke.aem.core.util.UtilityMethods' @ passedUrl=currentPage.path}"
     data-sly-test="${wcmmode.edit && wcmInit.isTouchAuthoring}">
    <p data-sly-test.compName="${component.title @ i18n}">${'revu.global.components.edittext' @i18n,
        format=[compName]}</p>
</sly>

<div data-sly-use.model="com.eon.dist.dke.aem.core.models.SearchModel" class='react-mini-app' data-component="SearchResults" data-application-name='dke-search-results'
  data-props='{
  	"apiUrl": "${model.apiUrl @context="uri"}",
  	"autocompletePlaceholder": "${properties.searchDescription @context="attribute"}",
	"defaultSearchView": "${properties.defaultSearchView @context="attribute"}",	
    "categories": [],
    "numberOfSuggestions": ${properties.numberOfSuggestions @context="attribute"},
    "numberOfResults": ${properties.numberOfResults @context="attribute"},
    "resultUrlLabel": "Seite aufrufen",
    "submitButtonLabel": "${properties.searchButtonLabel @context="attribute"}",
    "submitButtonVersion": "${properties.buttonColor @context="attribute"}",
    "link": {
      "version": "${properties.linkColor @context="attribute"}"
    },
	"reloadForAnalytics": ${properties.reloadPageForAnalyticsTracking ? true : false @context="attribute"},
	"reloadUrlForAnalytics": "${redirect.externalizedLink}"    
  }'>
</div>