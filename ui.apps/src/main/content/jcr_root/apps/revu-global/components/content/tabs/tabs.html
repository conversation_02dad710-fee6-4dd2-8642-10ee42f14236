<sly data-sly-use.wcmInit="/libs/wcm/foundation/components/page/initwcm.js"
     data-sly-test="${wcmmode.edit && wcmInit.isTouchAuthoring && !properties.title}">
    <p data-sly-test.compName="${component.title @ i18n}">${'revu.global.components.edittext' @i18n,
        format=[compName]}</p>
</sly>
<sly data-sly-use.clientLib="${'/libs/granite/sightly/templates/clientlib.html'}">
    <sly data-sly-call="${clientlib.js @ categories='com.revu.global.tabs'}"/>
</sly>
<sly data-sly-use.tabitems="${'/apps/revu-global/components/common/includes/getChildren.js' @ parsys='par_tabs', node=currentNode}" />
<sly data-sly-use.randomNumber="/apps/revu-global/components/common/includes/randomNumber.js" />
<div class="row tab-component">
  <div class="col-xs-12 col-sm-10 col-md-11 col-sm-offset-1">
    <div class="tab-navigation" data-sly-test="${!wcmmode.edit}" data-tabs-id="tabnav-${randomNumber.number}">
    <div class="tab-navigation__wrapper">
        <div class="tab-navigation__list" role="tablist">
          <sly data-sly-list.tabitem="${tabitems}" data-sly-unwrap>
            <div class="tab-navigation__item ${tabitemList.index == 0? 'current' : ''}" 
                data-tab="tab-${tabitem.name}-${dateid.formattedValue}" 
                data-sly-use.dateid="${'com.eon.dist.dke.aem.core.models.DateFormatterModel' @ date=tabitem.properties.jcr:created, dateFormat='yyMMddHHmmssSS'}"
                id="tab-${tabitem.name}-${dateid.formattedValue}"
                aria-controls="tab-${tabitem.name}-${dateid.formattedValue}"
      			role="tab"
                tabindex="0">
              <button aria-label="${tabitem.properties.title}" role="presentation" tabindex="-1">${tabitem.properties.title}</button>
            </div>
          </sly>
          <div class="tab-navigation__item tab-navigation__item-helper"></div>
        </div>
      </div>
      <div class="paddles">
        <div class="left-paddle paddle hidden" aria-label="Scroll left" tabindex="-1" aria-hidden="true">
          <div class="left-paddle__content" style="cursor:pointer">
            <i class="icomoon-arrow-right"></i>
          </div>
          <div class="left-paddle__gradient"></div>
        </div>
        <div class="right-paddle paddle" aria-label="Scroll right" tabindex="-1" aria-hidden="true">
          <div class="right-paddle__gradient"></div>
          <div class="right-paddle__content" style="cursor:pointer">
            <i class="icomoon-arrow-right"></i>
          </div>
        </div>
      </div>
    </div>
    <div 
    	class="tab-navigation__content" 
    	id="tabnav-${randomNumber.number}" 
      role="tabpanel"
      tabindex="0"
      aria-labelledby="tabnav-${randomNumber.number}">
    	<div data-sly-resource="${@path='par_tabs', resourceType='foundation/components/parsys'}" data-sly-unwrap></div>
	</div>
  </div>
</div>