<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    jcr:primaryType="nt:unstructured"
    jcr:title="revu.global.components.linklist.dialog"
    sling:resourceType="cq/gui/components/authoring/dialog"
	extraClientlibs="[acs-commons.granite.ui.coral2.foundation]">
    <content
        jcr:primaryType="nt:unstructured"
        sling:resourceType="granite/ui/components/coral/foundation/tabs">
        <items jcr:primaryType="nt:unstructured">
            <linkdetails
                jcr:primaryType="nt:unstructured"
                jcr:title="revu.global.components.linklist.dialog.linkdetails"
                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns">
                <items jcr:primaryType="nt:unstructured">
                    <column
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/foundation/container">
                        <items jcr:primaryType="nt:unstructured">
                            <headline
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                fieldLabel="revu.global.components.linklist.dialog.headline"
                                name="./headline"/>
                            <headlineID
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                fieldLabel="revu.global.components.linklist.dialog.headlineID"
                                name="./headlineID"/>
                            <headlineAriaLabel
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                fieldLabel="revu.global.components.linklist.dialog.headlineAriaLabel"
                                name="./headlineAriaLabel"/>
                            <headlineSize
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                fieldLabel="revu.global.components.linklist.headlinesize"
                                name="./headlineSize" 
                                defaultValue="h5">
                                <items jcr:primaryType="nt:unstructured">
                                    <h1
                                        jcr:primaryType="nt:unstructured"
                                        text="H1"
                                        value="h1"
                                    />
                                    <h2
                                            jcr:primaryType="nt:unstructured"
                                            text="H2"
                                            value="h2"
                                    />
                                    <h3
                                            jcr:primaryType="nt:unstructured"
                                            text="H3"
                                            value="h3"
                                    />
                                    <h4
                                            jcr:primaryType="nt:unstructured"
                                            text="H4"
                                            value="h4"
                                    />
                                    <h5
                                            jcr:primaryType="nt:unstructured"
                                            text="H5"
                                            value="h5"
                                    />
                                    <p
                                            jcr:primaryType="nt:unstructured"
                                            text="p"
                                            value="p"
                                    />
                                </items>
                            </headlineSize>
                            <additionalinfo
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textarea"
                                fieldLabel="revu.global.components.linklist.dialog.addtionalinfo"
                                name="./additionalinfo"/>
                            <links
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                fieldLabel="revu.global.components.linklist.dialog.links">
                                <field
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                    acs-commons-nested=""
                                    name="./linklist">
                                    <layout
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/foundation/layouts/fixedcolumns"
                                        method="absolute"/>
                                    <items jcr:primaryType="nt:unstructured">
                                        <column
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/foundation/container">
                                            <items jcr:primaryType="nt:unstructured">
                                                <linktitle
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.components.linklist.dialog.links.title"
                                                    name="./linkTitle"/>
                                                <linkText
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="revu.global.components.linklist.dialog.links.label"
                                                    name="./linkText"/>
                                                <linkDefault
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                    fieldLabel="revu.global.components.linklist.dialog.links.target"
                                                    name="./linkDefault"
                                                    rootPath="/content/revu-global"/>
                                            </items>
                                        </column>
                                    </items>
                                </field>
                            </links>
                        </items>
                    </column>
                </items>
            </linkdetails>
            <color
                jcr:primaryType="nt:unstructured"
                jcr:title="revu.global.templates.projectrootpage.dialog.tab.color.title"
                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns">
                <items jcr:primaryType="nt:unstructured">
                    <column
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/foundation/container">
                        <items jcr:primaryType="nt:unstructured">
                            <colorvariation
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.variation"
                                name="./variationName">
                                <datasource
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="/bin/optionsProvider"
                                    propName="linkColorConfig"/>
                            </colorvariation>
                        </items>
                    </column>
                </items>
            </color>
        </items>
    </content>
</jcr:root>
