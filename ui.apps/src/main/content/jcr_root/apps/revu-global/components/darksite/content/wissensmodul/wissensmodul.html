<sly data-sly-use.wcmInit="/libs/wcm/foundation/components/page/initwcm.js"
     data-sly-test="${wcmmode.edit && wcmInit.isTouchAuthoring && !properties.title}">
    <p data-sly-test.compName="${component.title @ i18n}">${'revu.global.components.edittext' @i18n,
        format=[compName]}</p>
</sly>
<sly data-sly-use.randomNumber="/apps/revu-global/components/common/includes/randomNumber.js" />
<div>
   <div class="row text-component__text-headline darksite_headline">
      <div class="col-xs-12 col-sm-11">
         <section>
            <h2 class="darksite_title">${properties.title}</h2>
            <p class="text-subline darksite_subtitle">${properties.subtitle}</p>
         </section>
      </div>
   </div>
</div>
<div class="row" data-sly-test="${wcmmode.edit}">
   <div class="col-xs-12">
      <div class="content-slider__list"
         data-sly-resource="${@path='par_slider', resourceType='foundation/components/parsys'}"
         ></div>
   </div>
</div>
<div class="content-slider darksite_slider_overflw_hdn" data-role="contentslider" data-sly-test="${!wcmmode.edit}" id="contentslider-${randomNumber.number}">
   <div class="content-slider__controls darksite_controls" data-sly-use.slider="com.eon.dist.dke.aem.core.models.ContentSliderModel">
      <div class="content-slider__controls-prev hidden-xs">
         <div class="prev">
            <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36">
               <path fill="#0091BB" fill-rule="evenodd" d="M18,36 C8.0588745,36 0,27.9411255 0,18 C0,8.0588745 8.0588745,0 18,0 C27.9411255,0 36,8.0588745 36,18 C36,27.9411255 27.9411255,36 18,36 Z M18,35 C27.3888407,35 35,27.3888407 35,18 C35,8.61115925 27.3888407,1 18,1 C8.61115925,1 1,8.61115925 1,18 C1,27.3888407 8.61115925,35 18,35 Z M20.25,12 L21.015,12.874 L16.53,18 L21.015,23.126 L20.25,24 L15,18 L20.25,12 Z"/>
            </svg>
         </div>
      </div>
      <div class="content-slider__pager">
         <div class="content-slider__pager-current"></div>
         <div>&nbsp;/&nbsp;</div>
         <div class="content-slider__pager-total"></div>
      </div>
      <div class="content-slider__controls-next hidden-xs">
         <div class="next">
            <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36">
               <path fill="#0091BB" d="M18,36 C8.0588745,36 0,27.9411255 0,18 C0,8.0588745 8.0588745,0 18,0 C27.9411255,0 36,8.0588745 36,18 C36,27.9411255 27.9411255,36 18,36 Z M18,35 C27.3888407,35 35,27.3888407 35,18 C35,8.61115925 27.3888407,1 18,1 C8.61115925,1 1,8.61115925 1,18 C1,27.3888407 8.61115925,35 18,35 Z M15.874,25 L15,24.126 L20.126,19 L15,13.874 L15.874,13 L21.874,19 L15.874,25 Z"/>
            </svg>
         </div>
      </div>
   </div>
   <ul class="content-slider__list darksite_slider_list darksite_slider_list_hdn" data-sly-list.path="${slider.slides}">
      <sly
         data-sly-resource="${path @ wcmmode='disabled'}"
         data-sly-unwrap />
   </ul>
</div>
