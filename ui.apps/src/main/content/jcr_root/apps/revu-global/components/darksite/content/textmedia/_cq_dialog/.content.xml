<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    jcr:primaryType="nt:unstructured"
    jcr:title="textMedia"
    extraClientlibs="[revu.rte.plugin]"
    sling:resourceType="cq/gui/components/authoring/dialog">
    <content
        jcr:primaryType="nt:unstructured"
        sling:resourceType="granite/ui/components/coral/foundation/container">
        <items jcr:primaryType="nt:unstructured">
            <tabs
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/tabs"
                maximized="{Boolean}true">
                <items jcr:primaryType="nt:unstructured">
                    <image_md_lg
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Image MD | LG"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <resType
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/foundation/form/hidden"
                                                name="./image1/sling:resourceType"
                                                value="foundation/components/image"/>
                                            <file
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                                allowUpload="{Boolean}false"
                                                autoStart="{Boolean}false"
                                                class="cq-droptarget"
                                                fieldLabel="Image asset"
                                                fileNameParameter="./image1/fileName"
                                                fileReferenceParameter="./image1/fileReference"
                                                mimeTypes="[image]"
                                                multiple="{Boolean}false"
                                                name="./image1/file"
                                                title="Upload Image Asset"
                                                uploadUrl="${suffix.path}"
                                                useHTML5="{Boolean}true"/>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </image_md_lg>
                    <image_sm
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Image SM"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <file
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                                allowUpload="{Boolean}false"
                                                class="cq-droptarget"
                                                ddGroups="[media]"
                                                fileNameParameter="./image2/fileName"
                                                fileReferenceParameter="./image2/fileReference"
                                                mimeTypes="[image/gif,image/jpeg,image/png,image/webp,image/tiff]"
                                                multiple="{Boolean}false"
                                                name="./image2/file"
                                                requestSuffix="/image2.img.png"
                                                required="{Boolean}true"
                                                title="Image SM"
                                                uploadUrl="${suffix.path}"
                                                useHTML5="{Boolean}true"/>
                                            <resType
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/foundation/form/hidden"
                                                name="./image2/sling:resourceType"
                                                value="foundation/components/image"/>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </image_sm>
                    <image_xs
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Image XS"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <file
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                                allowUpload="{Boolean}false"
                                                class="cq-droptarget"
                                                ddGroups="[media]"
                                                fileNameParameter="./image3/fileName"
                                                fileReferenceParameter="./image3/fileReference"
                                                mimeTypes="[image/gif,image/jpeg,image/png,image/webp,image/tiff]"
                                                multiple="{Boolean}false"
                                                name="./image3/file"
                                                requestSuffix="/image3.img.png"
                                                required="{Boolean}true"
                                                title="Image XS"
                                                uploadUrl="${suffix.path}"
                                                useHTML5="{Boolean}true"/>
                                            <resType
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/foundation/form/hidden"
                                                name="./image3/sling:resourceType"
                                                value="foundation/components/image"/>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </image_xs>
                    <mainConfig
                        jcr:primaryType="nt:unstructured"
                        jcr:title="revu.global.components.textmedia.dialog.main.config"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <title
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldLabel="revu.global.components.textmedia.dialog.tab3.image.title.config"
                                                name="./title"/>
                                            <alt
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldDescription="(leave empty to use the title defined above)"
                                                fieldLabel="revu.global.components.textmedia.dialog.tab3.image.alt.config"
                                                name="./alt"/>
                                            <caption
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textarea"
                                                fieldLabel="revu.global.components.textmedia.dialog.tab3.caption"
                                                name="./caption"/>
                                            <text
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="cq/gui/components/authoring/dialog/richtext"
                                                allowBlank="{Boolean}false"
                                                fieldLabel="revu.global.components.textmedia.dialog.tab1.item.text"
                                                hideLabel="{Boolean}true"
                                                name="./text"
                                                required="{Boolean}true"
                                                rtelistrevuglobal="{Boolean}true"
                                                useFixedInlineToolbar="{Boolean}true">
                                                <uiSettings jcr:primaryType="nt:unstructured">
                                                    <cui jcr:primaryType="nt:unstructured">
                                                        <inline
                                                            jcr:primaryType="nt:unstructured"
                                                            toolbar="[#styles,format#bold,format#italic,format#underline,subsuperscript#subscript,subsuperscript#superscript,#edit,edit#cut,edit#copy,edit#paste-default,edit#paste-plaintext,edit#paste-wordhtml,#links,links#modifylink,links#unlink,links#anchor,undo#undo,undo#redo,justify#justifyleft,justify#justifycenter,justify#justifyright,#lists,lists#unordered,lists#ordered,lists#outdent,lists#indent,spellcheck#checktext,misctools#specialchars,misctools#sourceedit,#paraformat,image#imageProps,findreplace#find,findreplace#replace,dkestyleformat#dkestyles]">
                                                            <popovers jcr:primaryType="nt:unstructured">
                                                                <styles
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="styles:getStyles:styles-pulldown"
                                                                    ref="styles"/>
                                                                <justify
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[justify#justifyleft,justify#justifycenter,justify#justifyright]"
                                                                    ref="justify"/>
                                                                <lists
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[lists#unordered,lists#ordered,lists#outdent,lists#indent]"
                                                                    ref="lists"/>
                                                                <paraformat
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="paraformat:getFormats:paraformat-pulldown"
                                                                    ref="paraformat"/>
                                                                <edit
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[edit#cut,edit#paste-plaintext,edit#copy,edit#paste-wordhtml,edit#paste-default]"
                                                                    ref="edit"/>
                                                                <table
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[table#table,table#removetable, table#insertrow, table#removerow, table#insertcolumn, table#removecolumn, table#cellprops, table#mergecells, table#splitcell, table#selectrow, table#selectcolumn]"
                                                                    ref="table"/>
                                                                <findreplace
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[findreplace#find,findreplace#replace]"
                                                                    ref="findreplace"/>
                                                                <misctools
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[misctools#specialchars,misctools#sourceedit]"
                                                                    ref="misctools"/>
                                                                <subsuperscript
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[subsuperscript#subscript,subsuperscript#superscript]"
                                                                    ref="subsuperscript"/>
                                                                <undo
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[undo#undo,undo#redo]"
                                                                    ref="undo"/>
                                                                <spellcheck
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[spellcheck#checktext]"
                                                                    ref="spellcheck"/>
                                                            </popovers>
                                                        </inline>
                                                        <tableEditOptions
                                                            jcr:primaryType="nt:unstructured"
                                                            toolbar="[table#insertcolumn-before,table#insertcolumn-after,table#removecolumn,table#insertrow-before,table#insertrow-after,table#removerow,table#mergecells-right,table#mergecells-down,table#mergecells,table#splitcell-horizontal,table#splitcell-vertical,table#selectrow,table#selectcolumn,table#ensureparagraph,table#modifytableandcell,table#removetable,undo#undo,undo#redo,table#exitTableEditing]">
                                                            <popovers jcr:primaryType="nt:unstructured">
                                                                <table
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[table#insertcolumn-before,table#insertcolumn-after,table#removecolumn, table#insertrow-before,table#insertrow-after,table#removerow,table#mergecells-right,table#mergecells-down,table#mergecells,table#splitcell-horizontal,table#splitcell-vertical,table#selectrow,table#selectcolumn,table#removetable, table#insertrow, table#removerow, table#insertcolumn, table#removecolumn, table#cellprops, table#mergecells, table#splitcell, table#selectrow, table#selectcolumn,table#ensureparagraph,table#exitTableEditing]"
                                                                    ref="table"/>
                                                                <undo
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[undo#undo,undo#redo]"
                                                                    ref="undo"/>
                                                            </popovers>
                                                        </tableEditOptions>
                                                        <dialogFullScreen
                                                            jcr:primaryType="nt:unstructured"
                                                            toolbar="[#styles,format#bold,format#italic,format#underline,subsuperscript#subscript,subsuperscript#superscript,#edit,edit#cut,edit#copy,edit#paste-default,edit#paste-plaintext,edit#paste-wordhtml,#links,links#modifylink,links#unlink,links#anchor,undo#undo,undo#redo,justify#justifyleft,justify#justifycenter,justify#justifyright,#lists,lists#unordered,lists#ordered,lists#outdent,lists#indent,spellcheck#checktext,misctools#specialchars,misctools#sourceedit,#paraformat,image#imageProps,findreplace#find,findreplace#replace,dkestyleformat#dkestyles]">
                                                            <popovers jcr:primaryType="nt:unstructured">
                                                                <paraformat
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="paraformat:getFormats:paraformat-pulldown"
                                                                    ref="paraformat"/>
                                                                <styles
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="styles:getStyles:styles-pulldown"
                                                                    ref="styles"/>
                                                                <justify
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[justify#justifyleft,justify#justifycenter,justify#justifyright]"
                                                                    ref="justify"/>
                                                                <edit
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[edit#cut,edit#paste-plaintext,edit#copy,edit#paste-wordhtml,edit#paste-default]"
                                                                    ref="edit"/>
                                                                <table
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[table#table,table#removetable, table#insertrow, table#removerow, table#insertcolumn, table#removecolumn, table#cellprops, table#mergecells, table#splitcell, table#selectrow, table#selectcolumn]"
                                                                    ref="table"/>
                                                                <findreplace
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[findreplace#find,findreplace#replace]"
                                                                    ref="findreplace"/>
                                                                <misctools
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[misctools#specialchars,misctools#sourceedit]"
                                                                    ref="misctools"/>
                                                                <subsuperscript
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[subsuperscript#subscript,subsuperscript#superscript]"
                                                                    ref="subsuperscript"/>
                                                                <undo
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[undo#undo,undo#redo]"
                                                                    ref="undo"/>
                                                                <spellcheck
                                                                    jcr:primaryType="nt:unstructured"
                                                                    items="[spellcheck#checktext]"
                                                                    ref="spellcheck"/>
                                                            </popovers>
                                                        </dialogFullScreen>
                                                    </cui>
                                                </uiSettings>
                                                <rtePlugins jcr:primaryType="nt:unstructured">
                                                    <format
                                                        jcr:primaryType="nt:unstructured"
                                                        features="*"/>
                                                    <justify
                                                        jcr:primaryType="nt:unstructured"
                                                        features="*"/>
                                                    <lists
                                                        jcr:primaryType="nt:unstructured"
                                                        features="*"/>
                                                    <links
                                                        jcr:primaryType="nt:unstructured"
                                                        features="*"/>
                                                    <table
                                                        jcr:primaryType="nt:unstructured"
                                                        features="*"/>
                                                    <spellcheck
                                                        jcr:primaryType="nt:unstructured"
                                                        features="*"/>
                                                    <image
                                                        jcr:primaryType="nt:unstructured"
                                                        features="*"/>
                                                    <edit
                                                        jcr:primaryType="nt:unstructured"
                                                        features="*"/>
                                                    <findreplace
                                                        jcr:primaryType="nt:unstructured"
                                                        features="*"/>
                                                    <styles
                                                        jcr:primaryType="nt:unstructured"
                                                        features="*">
                                                        <styles jcr:primaryType="cq:WidgetCollection">
                                                            <textSmall
                                                                jcr:primaryType="nt:unstructured"
                                                                cssName="text-small"
                                                                text="Text Small"/>
                                                            <textMicro
                                                                jcr:primaryType="nt:unstructured"
                                                                cssName="text-micro"
                                                                text="Text Micro"/>
                                                            <textSubline
                                                                jcr:primaryType="nt:unstructured"
                                                                cssName="text-subline"
                                                                text="Text Subline"/>
                                                        </styles>
                                                    </styles>
                                                    <paraformat
                                                        jcr:primaryType="nt:unstructured"
                                                        features="*">
                                                        <formats jcr:primaryType="cq:WidgetCollection">
                                                            <p
                                                                jcr:primaryType="nt:unstructured"
                                                                description="paragraph"
                                                                tag="p"/>
                                                            <h2
                                                                jcr:primaryType="nt:unstructured"
                                                                description="Heading 2"
                                                                tag="h2"/>
                                                            <h3
                                                                jcr:primaryType="nt:unstructured"
                                                                description="heading 3"
                                                                tag="h3"/>
                                                            <h4
                                                                jcr:primaryType="nt:unstructured"
                                                                description="Heading 4"
                                                                tag="h4"/>
                                                            <h5
                                                                jcr:primaryType="nt:unstructured"
                                                                description="Heading 5"
                                                                tag="h5"/>
                                                        </formats>
                                                    </paraformat>
                                                    <misctools
                                                        jcr:primaryType="nt:unstructured"
                                                        features="*">
                                                        <specialCharsConfig jcr:primaryType="nt:unstructured">
                                                            <chars jcr:primaryType="nt:unstructured">
                                                                <copyright
                                                                    jcr:primaryType="nt:unstructured"
                                                                    entity="&amp;#169"
                                                                    name="copyright"/>
                                                                <emDash
                                                                    jcr:primaryType="nt:unstructured"
                                                                    entity="&amp;#8212"
                                                                    name="emDash"/>
                                                                <nbsp
                                                                    jcr:primaryType="nt:unstructured"
                                                                    entity="&amp;#160"
                                                                    name="nbsp"/>
                                                                <registered
                                                                    jcr:primaryType="nt:unstructured"
                                                                    entity="&amp;#174"
                                                                    name="registered"/>
                                                                <trademark
                                                                    jcr:primaryType="nt:unstructured"
                                                                    entity="&amp;#8482"
                                                                    name="trademark"/>
                                                            </chars>
                                                        </specialCharsConfig>
                                                    </misctools>
                                                    <undo
                                                        jcr:primaryType="nt:unstructured"
                                                        features="*"/>
                                                    <subsuperscript
                                                        jcr:primaryType="nt:unstructured"
                                                        features="*"/>
                                                    <tracklinks
                                                        jcr:primaryType="nt:unstructured"
                                                        features="*"/>
                                                    <dkestyleformat
                                                        jcr:primaryType="nt:unstructured"
                                                        features="*"/>
                                                </rtePlugins>
                                            </text>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </mainConfig>
                </items>
            </tabs>
        </items>
    </content>
</jcr:root>
