<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0"
    jcr:primaryType="cq:Panel">
    <items jcr:primaryType="cq:WidgetCollection">         
          <fields
            jcr:primaryType="cq:Widget"
            collapsed="{Boolean}true"
            collapsible="{Boolean}true"
            title="revu.global.templates.projectrootpage.dialog.color.form.title"
            xtype="dialogfieldset"> 
            <items jcr:primaryType="cq:WidgetCollection">
               <formhint
		            jcr:primaryType="cq:Widget"
		            collapsed="{Boolean}true"
		            collapsible="{Boolean}true"
		            title="revu.global.templates.projectrootpage.dialog.color.formhint.title"
		            xtype="dialogfieldset"> 
		            <items jcr:primaryType="cq:WidgetCollection">
	                    <bgcolor
			                 jcr:primaryType="cq:Widget"		                 							     
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
			                 name="./formhintBgColor"
			                 xtype="textfield"/>
			            <textcolor
			                 jcr:primaryType="cq:Widget"  
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
			                 name="./formhintTextColor"
			                 xtype="textfield"/>             		                 
		            </items>
		        </formhint>
		        <formhinterror
		            jcr:primaryType="cq:Widget"
		            collapsed="{Boolean}true"
		            collapsible="{Boolean}true"
		            title="revu.global.templates.projectrootpage.dialog.color.formhinterror.title"
		            xtype="dialogfieldset"> 
		            <items jcr:primaryType="cq:WidgetCollection">
	                    <bgcolor
			                 jcr:primaryType="cq:Widget"		                 							     
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
			                 name="./formhintErrorBgColor"
			                 xtype="textfield"/>
			            <textcolor
			                 jcr:primaryType="cq:Widget"   
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
			                 name="./formhintErrorTextColor"
			                 xtype="textfield"/>             		                 
		            </items>
		        </formhinterror>
		        <forminput
		            jcr:primaryType="cq:Widget"
		            collapsed="{Boolean}true"
		            collapsible="{Boolean}true"
		            title="revu.global.templates.projectrootpage.dialog.color.forminput.title"
		            xtype="dialogfieldset"> 
		            <items jcr:primaryType="cq:WidgetCollection">
	                    <bgcolor
			                 jcr:primaryType="cq:Widget"		                 							     
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
			                 name="./inputBgColor"
			                 xtype="textfield"/>
			            <labelcolor
			                 jcr:primaryType="cq:Widget"  
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.label"
			                 name="./inputLabel"
			                 xtype="textfield"/>
			            <starcolor
			                 jcr:primaryType="cq:Widget"   
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.starcolor.label"
			                 name="./inputStar"
			                 xtype="textfield"/>
			            <reqtextcolor
			                 jcr:primaryType="cq:Widget"  
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.reqtext.label"
			                 name="./inputReqText"
			                 xtype="textfield"/>
			            <valtextcolor
			                 jcr:primaryType="cq:Widget"  
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.valtext.label"
			                 name="./inputValText"
			                 xtype="textfield"/>
			            <valtextdiscolor
			                 jcr:primaryType="cq:Widget"    
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.valtextdis.label"
			                 name="./inputValTextDis"
			                 xtype="textfield"/>
			            <bottomlinecolor
			                 jcr:primaryType="cq:Widget"   
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomline.label"
			                 name="./inputBottomLine"
			                 xtype="textfield"/>
			            <bottomlinefoccolor
			                 jcr:primaryType="cq:Widget"  
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomlinefoc.label"
			                 name="./inputBottomLineFoc"
			                 xtype="textfield"/>
			            <tickiconcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.tickicon.label"
			                 name="./inputTickIcon"
			                 xtype="textfield"/>
			            <crossiconcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.crossicon.label"
			                 name="./inputCrossIcon"
			                 xtype="textfield"/>
			            <bottomlineerrcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomlineerr.label"
			                 name="./inputBottomLineErr"
			                 xtype="textfield"/>         		                 
		            </items>
		        </forminput>
		        <formdropdown
		            jcr:primaryType="cq:Widget"
		            collapsed="{Boolean}true"
		            collapsible="{Boolean}true"
		            title="revu.global.templates.projectrootpage.dialog.color.formdropdown.title"
		            xtype="dialogfieldset"> 
		            <items jcr:primaryType="cq:WidgetCollection">
	                    <bgcolor
			                 jcr:primaryType="cq:Widget"		                 							     
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
			                 name="./ddBgColor"
			                 xtype="textfield"/>
			            <labelcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.label"
			                 name="./ddLabel"
			                 xtype="textfield"/>
			            <starcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.starcolor.label"
			                 name="./ddStar"
			                 xtype="textfield"/>
			            <reqtextcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.reqtext.label"
			                 name="./ddReqText"
			                 xtype="textfield"/>
			            <valtextcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.valtext.label"
			                 name="./ddValText"
			                 xtype="textfield"/>
			            <valtextdiscolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.valtextdis.label"
			                 name="./ddValTextDis"
			                 xtype="textfield"/>
			            <bottomlinecolor
			                 jcr:primaryType="cq:Widget" 
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomline.label"
			                 name="./ddBottomLine"
			                 xtype="textfield"/>
			            <bottomlinefoccolor
			                 jcr:primaryType="cq:Widget" 
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomlinefoc.label"
			                 name="./ddBottomLineFoc"
			                 xtype="textfield"/>
			            <tickiconcolor
			                 jcr:primaryType="cq:Widget"  
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.tickicon.label"
			                 name="./ddTickIcon"
			                 xtype="textfield"/>
			            <crossiconcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.crossicon.label"
			                 name="./ddCrossIcon"
			                 xtype="textfield"/>
			            <bottomlineerrcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomlineerr.label"
			                 name="./ddBottomLineErr"
			                 xtype="textfield"/>
			            <entrycolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.entry.label"
			                 name="./ddEntry"
			                 xtype="textfield"/>
			            <entryactivecolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.entryactive.label"
			                 name="./ddEntryActive"
			                 xtype="textfield"/>
			            <entryseparatorcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.entryseparator.label"
			                 name="./ddEntrySeparator"
			                 xtype="textfield"/>
			            <iconcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
			                 name="./ddIcon"
			                 xtype="textfield"/>        		                 
		            </items>
		        </formdropdown>
		        <formdatepicker
		            jcr:primaryType="cq:Widget"
		            collapsed="{Boolean}true"
		            collapsible="{Boolean}true"
		            title="revu.global.templates.projectrootpage.dialog.color.formdatepicker.title"
		            xtype="dialogfieldset"> 
		            <items jcr:primaryType="cq:WidgetCollection">
	                    <bgcolor
			                 jcr:primaryType="cq:Widget"		                 							     
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
			                 name="./dpBgColor"
			                 xtype="textfield"/>
			            <labelcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.label"
			                 name="./dpLabel"
			                 xtype="textfield"/>
			            <starcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.starcolor.label"
			                 name="./dpStar"
			                 xtype="textfield"/>
			            <reqtextcolor
			                 jcr:primaryType="cq:Widget"  
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.reqtext.label"
			                 name="./dpReqText"
			                 xtype="textfield"/>
			            <valtextcolor
			                 jcr:primaryType="cq:Widget" 
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.valtext.label"
			                 name="./dpValText"
			                 xtype="textfield"/>
			            <valtextdiscolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.valtextdis.label"
			                 name="./dpValTextDis"
			                 xtype="textfield"/>
			            <bottomlinecolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomline.label"
			                 name="./dpBottomLine"
			                 xtype="textfield"/>
			            <bottomlinefoccolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomlinefoc.label"
			                 name="./dpBottomLineFoc"
			                 xtype="textfield"/>
			            <tickiconcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.tickicon.label"
			                 name="./dpTickIcon"
			                 xtype="textfield"/>
			            <crossiconcolor
			                 jcr:primaryType="cq:Widget" 
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.crossicon.label"
			                 name="./dpCrossIcon"
			                 xtype="textfield"/>
			            <bottomlineerrcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomlineerr.label"
			                 name="./dpBottomLineErr"
			                 xtype="textfield"/>
			            <iconcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
			                 name="./dpIcon"
			                 xtype="textfield"/>
			            <openyeartxtcolor
			                 jcr:primaryType="cq:Widget" 
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.openyeartxt.label"
			                 name="./dpOpenYearTxt"
			                 xtype="textfield"/>
			            <openmonthtxtcolor
			                 jcr:primaryType="cq:Widget"  
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.openmonthtxt.label"
			                 name="./dpOpenMonthTxt"
			                 xtype="textfield"/>
			            <openyeararrowcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.openyeararrow.label"
			                 name="./dpOpenYearArrow"
			                 xtype="textfield"/>
			            <openweekdaystxtcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.openweekdaystxt.label"
			                 name="./dpOpenWeekDaysTxt"
			                 xtype="textfield"/>
			            <opendaystxtcolor
			                 jcr:primaryType="cq:Widget" 
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.opendaystxt.label"
			                 name="./dpOpenDaysTxt"
			                 xtype="textfield"/>
			            <opendaystxtdiscolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.opendaystxtdis.label"
			                 name="./dpOpenDaysTxtDis"
			                 xtype="textfield"/>
			            <opendaysactsetxtcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.opendaysactsetxt.label"
			                 name="./dpOpenDaysActSETxt"
			                 xtype="textfield"/>
			            <opendaysactsebgcolor
			                 jcr:primaryType="cq:Widget" 
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.opendaysactsebg.label"
			                 name="./dpOpenDaysActSEBg"
			                 xtype="textfield"/>
			            <opendaysactperiodtxtcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.opendaysactperiodtxt.label"
			                 name="./dpOpenDaysActPeriodTxt"
			                 xtype="textfield"/>
			            <opendaysactperiodbgcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.opendaysactperiodbg.label"
			                 name="./dpOpenDaysActPeriodBg"
			                 xtype="textfield"/>
			            <calendarlinescolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.calendarLines.label"
			                 name="./dpCalendarLines"
			                 xtype="textfield"/>
			            <calendarlinesdiscolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.calendarLinesdis.label"
			                 name="./dpCalendarLinesDis"
			                 xtype="textfield"/>       		                 
		            </items>
		        </formdatepicker>   
		        <formradio
		            jcr:primaryType="cq:Widget"
		            collapsed="{Boolean}true"
		            collapsible="{Boolean}true"
		            title="revu.global.templates.projectrootpage.dialog.color.formradio.title"
		            xtype="dialogfieldset"> 
		            <items jcr:primaryType="cq:WidgetCollection">
	                    <bgcolor
			                 jcr:primaryType="cq:Widget"		                 							     
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
			                 name="./radioBgColor"
			                 xtype="textfield"/>
			            <labelcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.label"
			                 name="./radioLabel"
			                 xtype="textfield"/>
			            <labeliconcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelicon"
			                 name="./radioLabelIcon"
			                 xtype="textfield"/>
			            <labelactivecolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelctive"
			                 name="./radioLabelActive"
			                 xtype="textfield"/>
			            <labelactiveiconcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelctiveicon"
			                 name="./radioLabelActiveIcon"
			                 xtype="textfield"/>
			            <labeldisabledcolor
			                 jcr:primaryType="cq:Widget" 
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.labeldisabled"
			                 name="./radioLabelDisabled"
			                 xtype="textfield"/>
			            <labelicondisabledcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelicondisabled"
			                 name="./radioLabelIconDisabled"
			                 xtype="textfield"/>
			            <checkiconcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.checkicon"
			                 name="./radioCheckIcon"
			                 xtype="textfield"/>
			            <checkiconcoloractive
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.checkiconActiive"
			                 name="./radioCheckIconActive"
			                 xtype="textfield"/>      		                 
		            </items>
		        </formradio>   
		        <formcheckbox
		            jcr:primaryType="cq:Widget"
		            collapsed="{Boolean}true"
		            collapsible="{Boolean}true"
		            title="revu.global.templates.projectrootpage.dialog.color.formcheckbox.title"
		            xtype="dialogfieldset"> 
		            <items jcr:primaryType="cq:WidgetCollection">
	                    <bgcolor
			                 jcr:primaryType="cq:Widget"		                 							     
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
			                 name="./cbBgColor"
			                 xtype="textfield"/>
			            <labelcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.label"
			                 name="./cbLabel"
			                 xtype="textfield"/>
			            <labeliconcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelicon"
			                 name="./cbLabelIcon"
			                 xtype="textfield"/>
			            <labelactivecolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelctive"
			                 name="./cbLabelActive"
			                 xtype="textfield"/>
			            <labelactiveiconcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelctiveicon"
			                 name="./cbLabelActiveIcon"
			                 xtype="textfield"/>
			            <labeldisabledcolor
			                 jcr:primaryType="cq:Widget" 
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.labeldisabled"
			                 name="./cbLabelDisabled"
			                 xtype="textfield"/>
			            <labelicondisabledcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelicondisabled"
			                 name="./cbLabelIconDisabled"
			                 xtype="textfield"/>
			            <checkiconcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.checkicon"
			                 name="./cbCheckIcon"
			                 xtype="textfield"/>
			            <checkiconcoloractive
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.checkiconActiive"
			                 name="./cbCheckIconActive"
			                 xtype="textfield"/>      		                 
		            </items>
		        </formcheckbox> 
		        <formswitches
		            jcr:primaryType="cq:Widget"
		            collapsed="{Boolean}true"
		            collapsible="{Boolean}true"
		            title="revu.global.templates.projectrootpage.dialog.color.formswitches.title"
		            xtype="dialogfieldset"> 
		            <items jcr:primaryType="cq:WidgetCollection">
	                    <bgcolor
			                 jcr:primaryType="cq:Widget"		                 							     
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
			                 name="./switchBgColor"
			                 xtype="textfield"/>
			            <labelcolor
			                 jcr:primaryType="cq:Widget"   
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.label"
			                 name="./switchLabel"
			                 xtype="textfield"/>
			            <labelactivecolor
			                 jcr:primaryType="cq:Widget" 
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.labelctive"
			                 name="./switchLabelActive"
			                 xtype="textfield"/>
			            <labeldisabledcolor
			                 jcr:primaryType="cq:Widget"  
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.labeldisabled"
			                 name="./switchLabelDisabled"
			                 xtype="textfield"/> 
			            <switchiconoff
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.switchoff.label"
			                 name="./switchIconOff"
			                 xtype="textfield"/>
			            <switchiconon
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.switchon.label"
			                 name="./switchIconOn"
			                 xtype="textfield"/> 		                 
		            </items>
		        </formswitches>	 
		        <formtextarea
		            jcr:primaryType="cq:Widget"
		            collapsed="{Boolean}true"
		            collapsible="{Boolean}true"
		            title="revu.global.templates.projectrootpage.dialog.color.formtextarea.title"
		            xtype="dialogfieldset"> 
		            <items jcr:primaryType="cq:WidgetCollection">
			            <labelcolor
			                 jcr:primaryType="cq:Widget"  
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.label"
			                 name="./taLabel"
			                 xtype="textfield"/>
			            <valtextcolor
			                 jcr:primaryType="cq:Widget"  
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.valtext.label"
			                 name="./taValText"
			                 xtype="textfield"/>
			            <valtextdiscolor
			                 jcr:primaryType="cq:Widget"    
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.valtextdis.label"
			                 name="./taValTextDis"
			                 xtype="textfield"/>
			            <bottomlinecolor
			                 jcr:primaryType="cq:Widget"   
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomline.label"
			                 name="./taBottomLine"
			                 xtype="textfield"/>
			            <bottomlinefoccolor
			                 jcr:primaryType="cq:Widget"  
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomlinefoc.label"
			                 name="./taBottomLineFoc"
			                 xtype="textfield"/>
			            <bottomlineerrcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.bottomlineerr.label"
			                 name="./taBottomLineErr"
			                 xtype="textfield"/>         		                 
		            </items>
		        </formtextarea>       
		        <formpopuphint
		            jcr:primaryType="cq:Widget"
		            collapsed="{Boolean}true"
		            collapsible="{Boolean}true"
		            title="revu.global.templates.projectrootpage.dialog.color.formspopuphint.title"
		            xtype="dialogfieldset"> 
		            <items jcr:primaryType="cq:WidgetCollection">
	                    <bgcolor
			                 jcr:primaryType="cq:Widget"		                 							     
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
			                 name="./popupHintBgColor"
			                 xtype="textfield"/>
			            <textcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
			                 name="./popupHintTextColor"
			                 xtype="textfield"/>
			            <iconcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
			                 name="./popupHintIconColor"
			                 xtype="textfield"/>                 
		            </items>
		        </formpopuphint>     	        
		        <formloader
		            jcr:primaryType="cq:Widget"
		            collapsed="{Boolean}true"
		            collapsible="{Boolean}true"
		            title="revu.global.templates.projectrootpage.dialog.color.formloader.title"
		            xtype="dialogfieldset"> 
		            <items jcr:primaryType="cq:WidgetCollection">
	                    <bgcolor
			                 jcr:primaryType="cq:Widget"		                 							     
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
			                 name="./loaderBgColor"
			                 xtype="textfield"/>
			            <strokecolor
			                 jcr:primaryType="cq:Widget" 
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.stroke.label"
			                 name="./loaderStroke"
			                 xtype="textfield"/>              
		            </items>
		        </formloader>
		        <formareahint
		            jcr:primaryType="cq:Widget"
		            collapsed="{Boolean}true"
		            collapsible="{Boolean}true"
		            title="revu.global.templates.projectrootpage.dialog.color.formsareahint.title"
		            xtype="dialogfieldset"> 
		            <items jcr:primaryType="cq:WidgetCollection">
	                    <bgcolor
			                 jcr:primaryType="cq:Widget"		                 							     
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.background.label"
			                 name="./areaHintBgColor"
			                 xtype="textfield"/>
			            <textcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
			                 name="./areaHintTextColor"
			                 xtype="textfield"/>
			            <iconcolor
			                 jcr:primaryType="cq:Widget"
			                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.icon.label"
			                 name="./areaHintIconColor"
			                 xtype="textfield"/>                 
		            </items>
		        </formareahint>  
	          </items>
           </fields>        
    </items>
</jcr:root>