foundation page
========

this page provides the core template for eon pages, thus enabling a project to just develop the elements which they need and dont exist yet.
the foundation page includes all core HTML, and a empty page with a menu.

It is possible to override all files obviously, but you should not unless you have a real good reason.
Typically only the following will be overriden:
body.html
header.html
footer.html
content.html //contains empty parsys

The rest should be taken care of by components and clientlibraries.

However sometimes also needed to add custom headers and extra javascript / css. For this you may override:

headlibscustom.html  //printed in the bottom of the page head element
bodylibscustom.html  //printed at the bottom of the page


Should never be overriden unless in exceptional circumstances:
page.html
head.html
headlibs.html
headlibswcm.html
bodylibs.html
context.html //contains data attributes for the front end which come from AEM, such as page path



