<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:mix="http://www.jcp.org/jcr/mix/1.0"
    jcr:language="de"
    jcr:mixinTypes="[mix:language]"
    jcr:primaryType="sling:Folder">
    <revu.global.components.iframe.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Iframe" />    
    <revu.global.components.iframe.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="Iframe Komponente" />
    <revu.global.components.iframe.options.iframeheightresizing.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Art der Höhenanpassung" />
    <revu.global.components.iframe.options.iframeheightresizing.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="'easyXDM' setzt vorraus, dass der Iframe-Inhalt eine Nachricht per Socket sendet (siehe http://easyxdm.net). 'MyNewsdesk' benötigt einen Benutzeraccount (siehe http://www.mynewsdesk.com)" />
    <revu.global.components.iframe.options.iframeheightresizing.fixed.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Feste Höhe" />
    <revu.global.components.iframe.options.iframeheightresizing.easyxdm.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Automatische Höhe (easyXDM)" />
    <revu.global.components.iframe.options.iframeheightresizing.mynewsdesk.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Automatische Höhe (MyNewsdesk)" />
  	<revu.global.components.iframe.options.iframeheightresizing.minikwk.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Automatische Höhe (MiniKWK)" />
    <revu.global.components.iframe.options.target.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="URL zur exteren Web-Applikation (frame source). Für die Art der Höhenanpassung 'Automatische Höhe (MyNewsdesk)' bitte Url zu 'hosted_newsroom.js' eingeben." />
    <revu.global.components.iframe.options.target.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Url (iframe source)" />
    <revu.global.components.iframe.options.height.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="Nur verwendet bei Art der Höhenanpassung 'Feste Höhe'. Der Standardwert ist 600 Pixel." />
    <revu.global.components.iframe.options.height.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Höhe" />    
    <revu.global.components.iframe.options.accessibilityseofieldset.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Barrierefreiheit / SEO" />
    <revu.global.components.iframe.alttext.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="Alternativ-text ist wichtig. Beschreibung des Iframe-Inhalts so kurz ie möglich." />
    <revu.global.components.iframe.alttext.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Alternativ-Text" />
    <revu.global.components.iframe.solutiondetails
        jcr:primaryType="sling:MessageEntry"
        sling:message="Zwei-Klick-Lösung Details"/>
    <revu.global.components.iframe.twoclicksolution
        jcr:primaryType="sling:MessageEntry"
        sling:message="Zwei-Klick-Lösung"/>
</jcr:root>
