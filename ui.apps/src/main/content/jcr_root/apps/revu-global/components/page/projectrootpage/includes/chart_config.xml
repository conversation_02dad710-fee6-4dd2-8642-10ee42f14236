<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0"
    jcr:primaryType="cq:Panel">
    <items jcr:primaryType="cq:WidgetCollection">         
          <fields
            jcr:primaryType="cq:Widget"
            collapsed="{Boolean}true"
            collapsible="{Boolean}true"
            title="revu.global.templates.projectrootpage.dialog.color.barchart.title"
            xtype="dialogfieldset"> 
            <items jcr:primaryType="cq:WidgetCollection">    
                <bar1
	                 jcr:primaryType="cq:Widget"	                                  						     
	                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.bar.label"
	                 name="./barChartBarColor1"
	                 xtype="textfield"/>
    	        <bar2
	                 jcr:primaryType="cq:Widget"	                 	                 							     
	                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.bar.label"
	                 name="./barChartBarColor2"
	                 xtype="textfield"/>
                <line
	                 jcr:primaryType="cq:Widget"	                 	                 						     
	                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.line.label"
	                 name="./barChartLineColor"
	                 xtype="textfield"/>
	            <text
	                 jcr:primaryType="cq:Widget"	                 	                 							     
	                 fieldLabel="revu.global.templates.projectrootpage.dialog.color.text.label"
	                 name="./barChartTexteColor"
	                 xtype="textfield"/>
	          </items>
           </fields>        
    </items>
</jcr:root>
