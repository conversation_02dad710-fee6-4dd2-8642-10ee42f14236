<sly data-sly-use.wcmInit="/libs/wcm/foundation/components/page/initwcm.js"
     data-sly-test="${wcmmode.edit && wcmInit.isTouchAuthoring && !properties.headline}">
    <p data-sly-test.compName="${component.title @ i18n}">${'revu.global.components.edittext' @i18n,
        format=[compName]}</p>
    </sly>
	<img data-sly-test='${wcmmode.edit && !properties.headline}' class="cq-image-placeholder" src="/etc/designs/default/0.gif"/>
<sly data-sly-use.randomNumber="/apps/revu-global/components/common/includes/randomNumber.js" />
<div data-mh="standard_${randomNumber.number}" data-sly-unwrap="${!wcmmode.edit}"
    data-sly-use.video="com.eon.dist.dke.aem.core.models.YouTubeModel">
    
	<sly data-sly-use.pageUtils="/apps/revu-global/components/utils/pageUtils.js"/>	
	<div class="row" data-sly-unwrap="${properties.teaserstyle != 'banner'}">
		<div class="${pageUtils.isMarginContent ? 'col-xs-12 col-sm-12 col-lg-6' : 'col-xs-12'}" data-mh="standard_${randomNumber.number}" data-sly-unwrap="${properties.teaserstyle == 'standard' && !pageUtils.isMarginContent}">
			<section data-sly-test='${properties.headline}' class="default-teaser default-teaser--bag ${properties.teaserstyle == 'banner' && 'default-teaser--horizontal-banner text-center'} teaser--${properties.variationName}" data-mh="icon-teaser" 
				data-sly-use.colorObject="${'/apps/revu-global/components/common/includes/currentColor.js' @ itemName='teaserv2ConfigList'}" data-sly-use.imageVerificationModel="com.eon.dist.dke.aem.core.models.ImageVerificationModel">
						<div data-mh="item-teaser">
							<div class="img-wrapper" data-sly-test="${imageVerificationModel.imageExist && properties.teaserstyle == 'standard'}">							
								<sly data-sly-resource="${'image' @ resourceType='revu-global/components/content/adaptiveimage', selectors='teaserstandard', wcmmode='disabled'}"/>			        
						        <a href="#modal-${randomNumber.number}" title="Video" class="btn-round md-trigger" data-target="modal-${randomNumber.number}" data-sly-test="${properties.videoId && inheritedPageProperties.youtubeUrl}">
						        	<i aria-hidden="true"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18"><title>Video</title><path fill="#000" fill-rule="nonzero" d="M12,4.11803399 L12,5.88196601 L15,7.38196601 L15,2.61803399 L12,4.11803399 Z M11,3.5 L16,1 L16,9 L11,6.5 L11,3.5 Z M9.1,10 L1.9,10 C0.825825127,10 0,9.08241653 0,8 L0,2 C0,0.917583475 0.825825127,0 1.9,0 L9.1,0 C10.1741749,0 11,0.917583475 11,2 L11,8 C11,9.08241653 10.1741749,10 9.1,10 Z M9.1,9 C9.597025,9 10,8.55225 10,8 L10,2 C10,1.44775 9.597025,1 9.1,1 L1.9,1 C1.402975,1 1,1.44775 1,2 L1,8 C1,8.55225 1.402975,9 1.9,9 L9.1,9 Z" transform="translate(1 4)"/></svg></i>
						        </a>
						        <div class="md md-hide" id="modal-${randomNumber.number}" data-role="modal" data-modal="modal-${randomNumber.number}" data-sly-test="${properties.videoId && inheritedPageProperties.youtubeUrl}">
					                <div class="md-content">
					                   <div class="${video.youtubeVideo ? 'iframe-video iframe-video--16x9' : ''}">
							                <iframe data-sly-test="${video.youtubeVideo}" src="${video.embedUrl}" frameborder="0" allowfullscreen></iframe>
							               	<video style='margin-bottom: -6px;' data-sly-test="${!video.youtubeVideo}" width="100%" height="100%" controls>
											    <source src="${video.embedUrl}" type="${video.videoType}">
												Your browser does not support the video tag.
											</video>
							            </div>
					                </div>
					                <button class="md-close"><span class="icon-close"></span></button>				        
					            </div>	
					           <div class="md-overlay" data-sly-test="${properties.videoId && inheritedPageProperties.youtubeUrl}"></div>
			                </div>
							<div class="default-teaser__item" 
								 data-sly-use.teaserLink ="${'/apps/revu-global/components/common/includes/multifieldValues.js' @ propertyName='linkList'}"
								 data-sly-use.reference="${'/apps/revu-global/components/content/textmedia/list.js' @ arg=properties.copytext}">

                               

                               <sly data-sly-test="${!properties.contentfragment == true}">	
                                <h5 class="standard-V2_inner-headline" data-sly-element="${properties.headlineSize}">${properties.headline}</h5>
						    	<div class="deco-line"></div>
						    	<p data-sly-test="${properties.date}" data-sly-use.date="${'com.eon.dist.dke.aem.core.models.DateFormatterModel' @ date=properties.date, dateFormat='EEEE, dd. MMMM yyyy'}"><span class="text-small"><strong>${date.formattedValue}</strong></span></p>                
                                </sly>

                                <sly data-sly-test="${!properties.contentfragment == false}">
                                <div data-sly-use.cf="${'com.eon.dist.dke.aem.core.models.ContentFragmentContent' @ cfInput=properties.cfdata}">

                                     <p class="standard-V2_inner-headline"> ${cf.getQuestion} </p>
                                     <div class="deco-line"></div>
                                    <p> ${cf.getAnswer} </p></div>
                                </sly>

                                
						        	${reference.value @context='html'}

						    	<ul class="list-unstyled list-unstyled--marginal" data-sly-test="${teaserLink.valueList}" data-sly-list.link="${teaserLink.valueList}" aria-label="${properties.linklistAriaLabel}">
						    		<li class="list-unstyled--marginal__item" data-sly-test="${link.linkTarget}" data-sly-use.linkUtils="${'/apps/revu-global/components/utils/linkUtils.js' @ path=link.linkTarget}">
						    			<div data-sly-use.pagelink="${'com.eon.dist.dke.aem.core.util.UtilityMethods' @ passedUrl=link.linkTarget}" data-sly-unwrap></div>						    			

<sly data-sly-test.check="${linkUtils.isInternalDamLink}"/>
<sly data-sly-test="${check}">
<a href="${pagelink.externalizedLink}" title="${link.linkTitle}" class="link link__icon link--small link--${colorObject.linkVariation}" target="${(link.linkNewTab || link.linkNewTab[0]) ? '_blank' : '_self'}">
<i aria-hidden="true"><svg width="18" height="18" viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg"><title>${link.linkTitle}</title><path d="M15.5 5.9h-3a.5.5 0 0 0 0 1H15v10H4v-10h2.5a.5.5 0 0 0 0-1h-3a.5.5 0 0 0-.5.5v11a.5.5 0 0 0 .5.5h12a.5.5 0 0 0 .5-.5v-11a.5.5 0 0 0-.5-.5M7.229 9.282L9 11.214V.5a.5.5 0 0 1 1 0v10.714l1.771-1.932a.5.5 0 0 1 .737.676l-2.64 2.879a.496.496 0 0 1-.736 0l-2.64-2.879a.5.5 0 0 1 .737-.676" fill="#000" fill-rule="evenodd"/></svg></i>
${link.linkText @context='text'}<sly data-sly-test="${linkUtils.isInternalDamLink}"> / ${linkUtils.assetFileExtension} (${linkUtils.assetFileSize})</sly>
</a>
</sly>
<sly data-sly-test="${!check}">
<a href="${pagelink.externalizedLink}" title="${link.linkTitle}" class="link link__icon link--small link--${colorObject.linkVariation}" target="${(link.linkNewTab || link.linkNewTab[0]) ? '_blank' : '_self'}">
<i class="icomoon-arrow-right" aria-hidden="true"></i>
${link.linkText @context='text'}<sly data-sly-test="${linkUtils.isInternalDamLink}"> / ${linkUtils.assetFileExtension} (${linkUtils.assetFileSize})</sly>
</a>
</sly>
									</li>					
								</ul>					
						    </div>
					    </div>
			<div class="fixed-bottom hello">
			   <div data-sly-use.colorObject="${'/apps/revu-global/components/common/includes/currentColor.js' @ itemName='teaserv2ConfigList'}" data-sly-use.pageUtils="/apps/revu-global/components/utils/pageUtils.js" data-sly-use.buttonlink="${'com.eon.dist.dke.aem.core.util.UtilityMethods' @ passedUrl=properties.buttonTarget}" data-sly-use.buttondamlink="${'com.eon.dist.dke.aem.core.util.UtilityMethods' @ passedUrl=properties.linkDambutton}"></div>

					 <a data-sly-test="${properties.buttonTarget || properties.popupcheck}"
                     	href="${properties.popupcheck ? 'javascript:void(0)' : buttonlink.externalizedLink}" 
                        title="${properties.buttonLabel}" 
                        class="button button--${colorObject.buttonVariation} ${properties.popupcheck ? 'popup-trigger' : ''}" 
                        data-sly-attribute.data-popup-id="${properties.popupcheck ? properties.popupid : ''}"
                        target="${properties.buttonNewTab ? '_blank' : '_self'}" >
					    <span>${properties.buttonLabel}</span>
					 </a>

             </div>	
			</section>
		</div>
	</div>
</div>
