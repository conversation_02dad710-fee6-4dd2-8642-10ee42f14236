<sly data-sly-use.wcmInit="/libs/wcm/foundation/components/page/initwcm.js"
     data-sly-test="${wcmmode.edit && wcmInit.isTouchAuthoring && !properties.headline}">
    <p data-sly-test.compName="${component.title @ i18n}">${'revu.global.components.edittext' @i18n,
        format=[compName]}</p>
</sly>
<sly data-sly-use.expandableItem="com.eon.dist.dke.aem.core.models.ExpandableItemModel"/>
<img data-sly-test='${wcmmode.edit && !properties.headline}' class="cq-list-placeholder" src="/etc/designs/default/0.gif"/>
<sly data-sly-use.staticlist="list.js"/>
<sly data-sly-test="${staticlist.isMargin}" data-sly-call="${margin @ linklist=staticlist}"/>
<sly data-sly-test="${!staticlist.isMargin}" data-sly-call="${content @ linklist=staticlist}"/>

<template data-sly-template.content="${@ linklist}">
	<div class="row links-component">
        <div class="${expandableItem.leftAligned? 'col-xs-12 col-sm-12 col-md-12' : 'col-xs-12 col-sm-10 col-md-11 col-sm-offset-1'}">
			<h5 data-sly-element="${properties.headlineSize}" data-sly-test="${linklist.headline}"  id="${properties.headlineID}">
	   			${linklist.headline @ context='html'}
  			</h5>
			<p data-sly-test="${linklist.additionalinfo}">${linklist.additionalinfo @ context='html'}</p>
			<ul class="list-unstyled" data-sly-list.link="${linklist.links}" aria-labelledby="${properties.headlineID}">
				<li class="list-unstyled__item--line-bottom" data-sly-test="${link.linkDefault}">
                    <a 
                    	href="${link.linkDefault @context='uri'}" 
                        class="link link__icon link--${properties.variationName}" 
                        target="${link.newTabCheck ? '_blank' : '_self'}"
                        tabindex="0"
                        onkeydown="if (event.key === 'Enter') { this.click(); }"
                        >
						<i class="icomoon-arrow-right" aria-hidden="true"></i>
						${link.linkText @context='html'}
					</a>            
				 </li>
			</ul>    
		</div>
	</div>
</template>

<template data-sly-template.margin="${@ linklist}">    
	<div class="col-xs-12 col-sm-6 col-md-12">
		<section class="links-component marginal">
			<div data-sly-element="${properties.headlineSize}" data-sly-test="${linklist.headline}"  id="${properties.headlineID}">
	   			${linklist.headline @ context='html'}
  			</div>
			<div data-sly-test="${linklist.headline}" class="deco-line"></div>
            <p data-sly-test="${linklist.additionalinfo}">${linklist.additionalinfo @ context='html'}</p>
			<ul class="list-unstyled list-unstyled--marginal" data-sly-list.link="${linklist.links}">
				<li class="list-unstyled--marginal__item" data-sly-test="${link.linkDefault}" aria-label="${link.linkText @context='html'}">
					<a 
                    	href="${link.linkDefault @context='uri'}" 
                        class="link link__icon link--small link--${properties.variationName}" 
                        target="${link.newTabCheck ? '_blank' : '_self'}"
                        tabindex="0"
                        onkeydown="if (event.key === 'Enter') { this.click(); }"
                        >
						<i class="icomoon-arrow-right" aria-hidden="true"></i>
						${link.linkText @context='html'}
					</a>            
				 </li>
			</ul>    
		</section>
	</div>    
</template>
