<sly data-sly-use.clientLibCategoryProvider="com.eon.dist.dke.aem.core.models.ClientLibCategoryProvider"
     data-sly-use.clientLib="${'/libs/granite/sightly/templates/clientlib.html'}"
     data-sly-unwrap>
    <sly data-sly-test="${!wcmmode.disabled}" data-sly-call="${clientLib.all @ categories=clientLibCategoryProvider.authorCategory}" data-sly-unwrap></sly>
    <!-- include the headlibscustom.html -->
    <sly data-sly-include="headlibscustom.html" data-sly-unwrap/>
    <sly data-sly-call="${clientLib.all @ categories=clientLibCategoryProvider.category}" data-sly-unwrap>
	<sly data-sly-call="${clientLib.js @ categories='eon.common.js'}"/>
	<sly data-sly-call="${clientLib.css @ categories='eon.common.css'}"/>
</sly>