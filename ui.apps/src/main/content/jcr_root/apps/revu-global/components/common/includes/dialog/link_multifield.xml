<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0"
    jcr:primaryType="cq:Panel"
    title="revu.global.components.common.includes.dialog.links.title">
    <items jcr:primaryType="cq:WidgetCollection">										
		<linklist
			jcr:primaryType="cq:Widget"
            name="./linkList"
            fieldLabel="revu.global.components.common.includes.dialog.links.title"
            xtype="multifield">
	        <fieldConfig
               jcr:primaryType="nt:unstructured"
               xtype="dke-multifieldpanel">
               <items jcr:primaryType="cq:WidgetCollection">
				<linkTitle
					jcr:primaryType="cq:Widget"
					anchor="100%"
					fieldLabel="revu.global.components.common.includes.dialog.link.title"
					key="linkTitle"
					xtype="textfield" />
				<linkText
					jcr:primaryType="cq:Widget"
					anchor="100%"
					fieldLabel="revu.global.components.common.includes.dialog.link.text"
					key="linkText"
					xtype="textfield" />
				<link
					jcr:primaryType="cq:Widget"
					anchor="100%"
					fieldLabel="revu.global.components.common.includes.dialog.link.target"
					key="linkTarget"					
					xtype="pathfield" />
                <checkbox
                    jcr:primaryType="cq:Widget"
                    fieldDescription="revu.global.components.common.includes.dialog.link.newtabcheck"
                    key="linkNewTab"
                    type="checkbox"
                    xtype="selection"/>
			</items>
		   </fieldConfig>
		</linklist>	 
	</items>
</jcr:root>