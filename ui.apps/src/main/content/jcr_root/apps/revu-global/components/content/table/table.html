<sly data-sly-use.model="com.eon.dist.dke.aem.core.models.table.TableController">
	
	<sly data-sly-use.wcmInit="/libs/wcm/foundation/components/page/initwcm.js"
     data-sly-test="${wcmmode.edit && wcmInit.isTouchAuthoring && !model.hasData}">
    <p data-sly-test.compName="${component.title @ i18n}">${'revu.global.components.edittext' @i18n,
        format=[compName]}</p>
</sly>

	<img data-sly-test='${wcmmode.edit && !model.hasData}' class="cq-image-placeholder" src="/etc/designs/default/0.gif"/>	
	<div data-sly-test='${model.hasData}' data-sly-unwrap>
		<div data-sly-include="tableTag.html"data-sly-unwrap />		
	</div>
</sly>