<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:granite="http://www.adobe.com/jcr/granite/1.0" xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    jcr:primaryType="nt:unstructured"
    sling:resourceType="granite/ui/components/foundation/container">
    <items jcr:primaryType="nt:unstructured">
        <tabs
            jcr:primaryType="nt:unstructured"
            sling:resourceType="granite/ui/components/foundation/container"
            rel="cq-siteadmin-admin-properties-tabs">
            <layout
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/foundation/layouts/tabs"
                type="nav"/>
            <items
                jcr:primaryType="nt:unstructured"
                sling:hideChildren="[advanced,thumbnail,personalization,permissions,blueprint,livecopy]"
                sling:resourceType="granite/ui/components/foundation/container">
                <basic
                    cq:showOnCreate="{Boolean}true"
                    jcr:primaryType="nt:unstructured"
                    jcr:title="revu.global.templates.projectrootpage.basic"
                    sling:resourceType="granite/ui/components/foundation/section">
                    <layout
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/foundation/layouts/fixedcolumns"/>
                    <items jcr:primaryType="nt:unstructured">
                        <columns
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="granite/ui/components/foundation/container">
                            <items jcr:primaryType="nt:unstructured">
                                <title
                                    cq:showOnCreate="{Boolean}true"
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/foundation/form/textfield"
                                    fieldLabel="revu.global.page.dialog.tab.basic.title"
                                    name="./jcr:title"/>
                                <pagetitle
                                    cq:showOnCreate="{Boolean}true"
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/foundation/form/textfield"
                                    fieldLabel="revu.global.page.dialog.tab.basic.pagetitle"
                                    name="./pageTitle"/>
                                <description
                                    cq:showOnCreate="{Boolean}true"
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/foundation/form/textarea"
                                    fieldLabel="revu.global.page.dialog.tab.basic.description"
                                    name="./jcr:description"/>
                                <tags
                                    cq:showOnCreate="{Boolean}true"
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="cq/gui/components/common/tagspicker"
                                    cls="cq-propsdialog-tags"
                                    fieldLabel="revu.global.page.dialog.tab.basic.tags"
                                    name="./cq:tags"/>
                                <hidenav
                                    cq:showOnCreate="{Boolean}true"
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/foundation/form/checkbox"
                                    name="./hideNav"
                                    renderReadOnly="{Boolean}true"
                                    text="revu.global.components.page.basepage.hideNavigation"
                                    value="true"/>
                                <hideinnav
                                    cq:showOnCreate="{Boolean}true"
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/foundation/form/checkbox"
                                    fieldLabel="revu.global.components.page.basepage.hideInNavigation"
                                    name="./hideInNav"
                                    renderReadOnly="{Boolean}true"
                                    text="revu.global.components.page.basepage.hideInNavigation"
                                    value="true"/>
                                <hideinbreadCrumb
                                    cq:showOnCreate="{Boolean}true"
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/foundation/form/checkbox"
                                    name="./hideInBreadCrumb"
                                    renderReadOnly="{Boolean}true"
                                    text="revu.global.components.page.basepage.hideInBreadCrumb"
                                    value="true"/>
                                <hideinsearch
									cq:showOnCreate="{Boolean}true"
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/foundation/form/checkbox"
                                    id="hideInSearch"
                                    name="./hideInSearch"
                                    renderReadOnly="{Boolean}true"
                                    text="revu.global.components.page.basepage.hideininternalsearch"
                                    value="true"/>
                                <hideinexternalsearch
									cq:showOnCreate="{Boolean}true"
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/foundation/form/checkbox"
                                    id="hideInExternalSearch"
                                    name="./hideInExternalSearch"
                                    renderReadOnly="{Boolean}true"
                                    text="revu.global.components.page.basepage.hideInSearch"
                                    value="true"/>
                                <metanavigation
                                    cq:showOnCreate="{Boolean}true"
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/foundation/form/checkbox"
                                    name="./metanavigation"
                                    renderReadOnly="{Boolean}true"
                                    text="Meta Navigation"
                                    value="true"/>
                                <hideinsitemap
                                    cq:showOnCreate="{Boolean}true"
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/foundation/form/checkbox"
                                    name="./hideInSitemap"
                                    renderReadOnly="{Boolean}true"
                                    text="Hide in Sitemap"
                                    value="true"/>
                            </items>
                        </columns>
                    </items>
                </basic>
                <advanced
                    cq:showOnCreate="{Boolean}true"
                    jcr:primaryType="nt:unstructured"
                    jcr:title="revu.global.components.page.basepage.advanced"
                    sling:resourceType="granite/ui/components/foundation/section">
                    <layout
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/foundation/layouts/fixedcolumns"
                        margin="{Boolean}false"/>
                    <items jcr:primaryType="nt:unstructured">
                        <column
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="granite/ui/components/foundation/container">
                            <items jcr:primaryType="nt:unstructured">
                                <section1
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="Settings"
                                    sling:resourceType="granite/ui/components/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <advanced
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="Advanced"
                                            sling:resourceType="granite/ui/components/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <language
                                                    granite:class="language"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/foundation/form/select"
                                                    emptyText="Select"
                                                    fieldLabel="revu.global.page.dialog.tab.advanced.language"
                                                    name="./jcr:language"
                                                    renderReadOnly="{Boolean}true"
                                                    translateOptions="{Boolean}true">
                                                    <granite:data
                                                        jcr:primaryType="nt:unstructured"
                                                        cq-msm-lockable="jcr:language"/>
                                                    <datasource
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="cq/gui/components/common/datasources/languages"
                                                        addNone="{Boolean}true"/>
                                                </language>
                                                <isLanguageRoot
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                    fieldDescription="Must be checked if page is the root of a language copy."
                                                    name="./cq:isLanguageRoot"
                                                    renderReadOnly="{Boolean}true"
                                                    text="Language Root"
                                                    value="true">
                                                    <granite:data
                                                        jcr:primaryType="nt:unstructured"
                                                        cq-msm-lockable="cq:isLanguageRoot"/>
                                                </isLanguageRoot>
                                                <aliasName
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/foundation/form/textfield"
                                                    alias-validation="true"
                                                    class="aliasName"
                                                    fieldLabel="revu.global.components.page.basepage.alias"
                                                    name="./sling:alias"/>
                                                <isDefaultCic
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                    text="Is Default CIC"
                                                    value="true"
                                                    fieldDescription="If it is checked, the cic_service is used for the flyout on the landing page"
                                                    name="./isDefaultCic"/>
                                                <redirect
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                    allowBulkEdit="true"
                                                    cq-msm-lockable="redirectTarget"
                                                    fieldLabel="revu.global.components.page.basepage.redirect"
                                                    name="./redirectTarget"
                                                    readOnlyURITemplate="/libs/wcm/core/content/sites/properties.html{+value}"
                                                    renderReadOnly="{Boolean}true"
                                                    rootPath="/content/revu-global"/>
                                            </items>
                                        </advanced>
                                        <temporaryRedirectFS
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.components.page.basepage.temporaryRedirect302"
                                            sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                            title="revu.global.components.page.basepage.temporaryRedirect302">
                                            <items jcr:primaryType="nt:unstructured">
                                                <redirectOption
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/foundation/form/checkbox"
                                                    name="./sling:temporaryRedirect"
                                                    text="revu.global.components.page.basepage.temporaryRedirect"
                                                    value="{Boolean}true"/>
                                            </items>
                                        </temporaryRedirectFS>
                                        <canonical
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.components.page.basepage.canonicalLink"
                                            sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                            title="revu.global.components.page.basepage.canonicalLink">
                                            <items jcr:primaryType="nt:unstructured">
                                                <canonicalLink
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/foundation/form/pathbrowser"
                                                    allowBulkEdit="true"
                                                    fieldDescription="revu.global.components.page.basepage.canonicalLinkDescription"
                                                    fieldLabel="revu.global.components.page.basepage.canonicalLink"
                                                    name="./canonicalLinksPage"
                                                    readOnlyURITemplate="/libs/wcm/core/content/sites/properties.html{+value}"
                                                    renderReadOnly="{Boolean}true"
                                                    rootPath="/content/revu-global"/>
                                            </items>
                                        </canonical>
                                       <vanityurl
                                            cq:showOnCreate="{Boolean}false"
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="Vanity URL"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                            <items jcr:primaryType="nt:unstructured">
                                                <vanitypath
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                    cq-msm-lockable="sling:vanityPath"
                                                    fieldDescription="Please ensure that vanity URL has following pattern &quot;/content/solution/vanity&quot;, e.g. &quot;/content/eon-com/jobs&quot;"
                                                    fieldLabel="Vanity URL"
                                                    renderReadOnly="{Boolean}true">
                                                    <granite:data
                                                        jcr:primaryType="nt:unstructured"
                                                        cq-msm-lockable="sling:vanityPath"/>
                                                    <field
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                        name="./sling:vanityPath"
                                                        required="{Boolean}true"/>
                                                </vanitypath>
                                                <redirectVanityURL
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                    name="./sling:redirect"
                                                    renderReadOnly="{Boolean}true"
                                                    text="Redirect Vanity URL"
                                                    value="true">
                                                    <granite:data
                                                        jcr:primaryType="nt:unstructured"
                                                        cq-msm-lockable="./sling:redirect"/>
                                                </redirectVanityURL>
                                            </items>
                                        </vanityurl>
                                        <onofftime
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            jcr:title="revu.global.components.page.basepage.onOffTime"
                                            sling:resourceType="granite/ui/components/foundation/form/fieldset"
                                            title="revu.global.components.page.basepage.onOffTime">
                                            <items jcr:primaryType="nt:unstructured">
                                                <ontime
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="/libs/granite/ui/components/coral/foundation/form/datepicker"
                                                    fieldLabel="revu.global.components.page.basepage.onTime"
                                                    name="./onTime"
                                                    text="revu.global.components.page.basepage.onTime"
                                                    type="datetime"
                                                    value="{Boolean}true"/>
                                                <offtime
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="/libs/granite/ui/components/coral/foundation/form/datepicker"
                                                    fieldLabel="revu.global.components.page.basepage.offTime"
                                                    name="./offTime"
                                                    text="revu.global.components.page.basepage.offTime"
                                                    type="datetime"
                                                    value="{Boolean}true"/>
                                                <offtimehint
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="/libs/granite/ui/components/coral/foundation/form/hidden"
                                                    name="./offTime@TypeHint"
                                                    value="Date"/>
                                                <ontimehint
                                                    cq:showOnCreate="{Boolean}true"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="/libs/granite/ui/components/coral/foundation/form/hidden"
                                                    name="./onTime@TypeHint"
                                                    value="Date"/>
                                            </items>
                                        </onofftime>
                                    </items>
                                </section1>
                            </items>
                        </column>
                    </items>
                </advanced>
                <socialsharing
                    cq:showOnCreate="{Boolean}true"
                    jcr:primaryType="nt:unstructured"
                    jcr:title="Social Sharing"
                    sling:resourceType="granite/ui/components/foundation/section">
                    <layout
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/foundation/layouts/fixedcolumns"
                        margin="{Boolean}false"/>
                    <items jcr:primaryType="nt:unstructured">
                        <column
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="granite/ui/components/foundation/container">
                            <items jcr:primaryType="nt:unstructured">
                                <section1
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="Settings"
                                    sling:resourceType="granite/ui/components/foundation/form/fieldset">
                                    <items jcr:primaryType="nt:unstructured">
                                        <title
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/foundation/form/textfield"
                                            fieldLabel="revu.global.components.page.basepage.socialsharing.title"
                                            name="./openGraphTitle"/>
                                        <description
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/foundation/form/textarea"
                                            fieldLabel="revu.global.components.page.basepage.socialsharing.description"
                                            name="./openGraphDescription"/>
                                        <image
                                            cq:showOnCreate="{Boolean}true"
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/foundation/form/pathbrowser"
                                            fieldDescription="revu.global.components.page.basepage.socialsharing.opengraphimagedescription"
                                            fieldLabel="revu.global.components.page.basepage.socialsharing.image"
                                            name="./openGraphImage"/>
                                    </items>
                                </section1>
                            </items>
                        </column>
                    </items>
                </socialsharing>
                <consentoverlay
                    cq:showOnCreate="{Boolean}true"
                    jcr:primaryType="nt:unstructured"
                    jcr:title="Consent Overlay"
                    sling:resourceType="granite/ui/components/foundation/section">
                    <layout
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/foundation/layouts/fixedcolumns"
                        margin="{Boolean}false"/>
                    <items jcr:primaryType="nt:unstructured">
                        <column
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="granite/ui/components/foundation/container">
                            <items jcr:primaryType="nt:unstructured">
                                <consentusercentrics
                                    cq:showOnCreate="{Boolean}true"
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/foundation/form/checkbox"
                                    name="./consentOverlay"
                                    renderReadOnly="{Boolean}true"
                                    text="revu.global.properties.consentoverlay.checkbox"
                                    value="true"/>
                            </items>
                        </column>
                    </items>
                </consentoverlay>
            </items>
        </tabs>
    </items>
</jcr:root>
