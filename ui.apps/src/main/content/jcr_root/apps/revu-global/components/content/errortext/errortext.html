<sly data-sly-use.wcmInit="/libs/wcm/foundation/components/page/initwcm.js"
     data-sly-test="${wcmmode.edit && wcmInit.isTouchAuthoring && !properties.headline}">
    <p data-sly-test.compName="${component.title @ i18n}">${'revu.global.components.edittext' @i18n,
        format=[compName]}</p>
</sly>

<img data-sly-test="${wcmmode.edit && !properties.headline}" class="cq-list-placeholder" src="/etc/designs/default/0.gif"/>
<div data-sly-test="${properties.headline}"  class="row page-headline page-headline__error-site">
    <div class="col-xs-12 col-sm-12 col-md-10 col-md-offset-1">
        <section class="text-center">
            <p><span class="text-large">${properties.headline}</span></p>
            <h1>${properties.subheadline}</h1>
            <div class="row text-component__text-media">
                <div class="col-xs-12 col-sm-11 col-md-12">
                    <div class="row">
                        <section class="text-center">
                            ${properties.text @ context='html'}
                            <br>
                            <p>
                            <sly data-sly-test="${properties.buttonTarget1}">
                            <div data-sly-use.pagelink="${'com.eon.dist.dke.aem.core.util.UtilityMethods' @ passedUrl=properties.buttonTarget1}" data-sly-unwrap></div>
	                            <a href="${pagelink.externalizedLink}" title="${properties.buttonLabel1}"  class="button button--${properties.variationName}" target="${properties.buttonNewTab1 ? '_blank' : '_self'}"><span>${properties.buttonLabel1}</span></a>
	                        </sly>	                        
	                        <sly data-sly-test="${properties.buttonTarget2}">
                            <div data-sly-use.pagelink="${'com.eon.dist.dke.aem.core.util.UtilityMethods' @ passedUrl=properties.buttonTarget2}" data-sly-unwrap></div>
	                            <a href="${pagelink.externalizedLink}" title="${properties.buttonLabel2}"  class="button button--${properties.variationName}" target="${properties.buttonNewTab2 ? '_blank' : '_self'}"><span>${properties.buttonLabel2}</span></a>
	                        </sly>								
						    </p>
                        </section>
                    </div>
                </div>
            </div>        
       </section>
    </div>
</div>