<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0"
    jcr:primaryType="cq:Panel">
    <items jcr:primaryType="cq:WidgetCollection">
        <fields
            jcr:primaryType="cq:Widget"
            collapsed="{Boolean}true"
            collapsible="{Boolean}true"
            title="Contact Modul"
            xtype="dialogfieldset">
            <items jcr:primaryType="cq:WidgetCollection">
		    	<title
	                jcr:primaryType="cq:Widget"	                                  						     
	                fieldLabel="Title"
	                name="./contactModulTitle"
	                xtype="textfield"/>
    	        <titleLine
	                jcr:primaryType="cq:Widget"	                                  						     
	                fieldLabel="Title-Line"
	                name="./contactModulTitleLine"
	                xtype="textfield"/>
                <subtitle
	                jcr:primaryType="cq:Widget"	                                  						     
	                fieldLabel="subtitle"
	                name="./contactModulSubtitle"
	                xtype="textfield"/>
	            <text
	                jcr:primaryType="cq:Widget"	                                  						     
	                fieldLabel="Text"
	                name="./contactModulText"
	                xtype="textfield"/>    
	            <seperator
					jcr:primaryType="cq:Widget"	                                  						     
	                fieldLabel="Seperator"
	                name="./contactModulSeperator"
	                xtype="textfield"/>
	          	<linkvariation
	                jcr:primaryType="cq:Widget"
	                fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.link.variation.label"
	                name="./contactModulLinkVariation"
	                options="$PATH.link-options.json"
	                type="select"
	                xtype="selection"/>
	          </items>
           </fields>        
    </items>
</jcr:root>
