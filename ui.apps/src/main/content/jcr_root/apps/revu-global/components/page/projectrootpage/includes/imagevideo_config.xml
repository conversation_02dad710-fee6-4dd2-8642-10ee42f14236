<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0"
    jcr:primaryType="cq:Panel">
    <items jcr:primaryType="cq:WidgetCollection">         
          <fields
            jcr:primaryType="cq:Widget"
            collapsed="{Boolean}true"
            collapsible="{Boolean}true"
            title="revu.global.templates.projectrootpage.dialog.color.imagevideo.title"
            xtype="dialogfieldset"> 
            <items jcr:primaryType="cq:WidgetCollection">            	
	          	<buttonvariation
		             jcr:primaryType="cq:Widget"
		             fieldLabel="revu.global.templates.projectrootpage.dialog.tab.color.button.variation.label"
		             name="./imageVideoButtonVariation"
		             options="$PATH.button-options.json"
		             type="select"
		             xtype="selection"/>      
	          </items>
           </fields>        
    </items>
</jcr:root>
