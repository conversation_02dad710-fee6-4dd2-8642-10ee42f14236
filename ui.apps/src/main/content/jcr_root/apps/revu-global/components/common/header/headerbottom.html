 <sly data-sly-test="${pageProperties['cq:template'] != '/apps/revu-global-netz/iconnect-customer/templates/installerprojectrootpage' && pageProperties['cq:template'] != '/apps/revu-global-netz/iconnect-customer/templates/installerprojectdoorpage'}">
 <sly data-sly-use.metanavigationmodel="${'com.eon.dist.dke.aem.core.models.OptionalNavigationModel'}" data-sly-test="${!pageProperties['hideNav']}">
    <sly data-sly-test = "${metanavigationmodel.optionalnavigationlevel == 4}">
       	<nav class="header-wrapper-bottom meta_nav_clr">
            <ul class="header-navigation__list" data-sly-list.level1="${metanavigationmodel.optionalheadertop}">
	          <li class="header-navigation__item">
		         <a class = "${level1.item.isCurrentLink ? 'actv' : ''}" data-sly-attribute.href="${level1.item.link}" title="${level1.item.title}">${level1.item.title}</a>
              </li>
            </ul>
        </nav>
    </sly>
 </sly>
 <div class="header-wrapper-bottom" data-sly-use.lib="revu-global/components/common/includes/templateLib.html">
	 <div class="header-navigation" data-sly-test="${!pageProperties['hideNav']}">	
		 <div data-sly-resource="${'topnav' @ resourceType='revu-global/components/common/topnav'}"></div>	
	 </div>
</div>
</sly>
