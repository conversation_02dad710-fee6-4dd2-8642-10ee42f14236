<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:mix="http://www.jcp.org/jcr/mix/1.0"
    jcr:language="en"
    jcr:mixinTypes="[mix:language]"
    jcr:primaryType="sling:Folder">
    <revu.global.components.form.datepicker.dialog.additionalinfo.information.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="Provide description for additional information"/>
    <revu.global.components.form.datepicker.dialog.additionalinfo.information.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Information"/>
    <revu.global.components.form.datepicker.dialog.additionalinfo.showinfo.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="Whether to show user the detail description of the field to fill"/>
    <revu.global.components.form.datepicker.dialog.additionalinfo.showinfo.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Show additional information"/>
    <revu.global.components.form.datepicker.dialog.additionalinfo.title
        jcr:primaryType="sling:MessageEntry"
        sling:message="Additional Infomation"/>
    <revu.global.components.form.dialog.constraintMessage.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="Message displayed as tooltip when submitting the form if the value is incorrect"/>
    <revu.global.components.form.dialog.constraintMessage.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Constraint Message"/>
    <revu.global.components.form.dialog.disabled.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Disable Datepicker"/>
    <revu.global.components.form.dialog.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Input label"/>
    <revu.global.components.form.dialog.maxvalue.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Max Date Value/Relative timespan to"/>
    <revu.global.components.form.dialog.minvalue.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Min Date Value/Relative timespan from"/>
    <revu.global.components.form.dialog.name.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="The name of the entry within the form"/>
    <revu.global.components.form.dialog.name.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Name"/>
    <revu.global.components.form.dialog.required.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="Whether the user must fill in a value before submitting the form"/>
    <revu.global.components.form.dialog.required.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Required"/>
    <revu.global.components.form.dialog.requiredMessage.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="Message displayed as tooltip when submitting the form if the value is left empty"/>
    <revu.global.components.form.dialog.requiredMessage.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Required Message"/>
    <revu.global.components.form.dialog.defaultvalue.description
            jcr:primaryType="sling:MessageEntry"
            sling:message="please enter the date with dd.mm.yyyy format"/>
    <revu.global.components.form.dialog.value.description
        jcr:primaryType="sling:MessageEntry"
        sling:message="please enter the date with dd.mm.yyyy format or timespan in days"/>
    <revu.global.components.form.dialog.value.label
        jcr:primaryType="sling:MessageEntry"
        sling:message="Default Date Value"/>
</jcr:root>
