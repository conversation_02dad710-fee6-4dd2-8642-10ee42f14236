<li class="content-slider__list-item darksite-slider_list_item" data-sly-test="${!wcmmode.edit}">
   <div class="media col-md-6 col-xs-12" style="${!wcmmode.edit ? '' : 'position:relative;' @ context='styleToken'}">
      <sly data-sly-resource="${'image' @ resourceType='revu-global/components/content/adaptiveimage', selectors='contentslider', wcmmode='disabled'}"/>
   </div>
   <div class="content darksite_content col-md-6 col-xs-12">
      <div class="content-wrapper darksite_content-wrapper" data-sly-use.buttonlink="${'com.eon.dist.dke.aem.core.util.UtilityMethods' @ passedUrl=properties.linkTarget}" data-sly-use.colortheme="/apps/revu-global/components/darksite/page/darksitehomepage/colortheme-darksite.js">
         ${properties.text @ context='html'}        
         <a data-sly-test="${properties.linkTarget}" href="${buttonlink.externalizedLink}" title="${properties.linkTitle}" class="button button--${colortheme.contentSlider.buttonVariation}">
         <span>${properties.linkTitle}</span>
         </a>              
      </div>
   </div>
</li>
<div class="row content-slider__list-item" data-sly-test="${wcmmode.edit}">
   <div class="col-md-6">
      <sly data-sly-resource="${'image' @ resourceType='revu-global/components/content/adaptiveimage', selectors='contentslider', wcmmode='disabled'}"/>
   </div>
   <div class="col-md-6">
      <div class="content darksite_content" style="margin:0;">
         <div class="content-wrapper darksite_content-wrapper" data-sly-use.buttonlink="${'com.eon.dist.dke.aem.core.util.UtilityMethods' @ passedUrl=properties.linkTarget}" data-sly-use.colortheme="/apps/revu-global/components/darksite/page/darksitehomepage/colortheme-darksite.js">
            <h4>${properties.headline}</h4>
            ${properties.text @ context='html'}
            <a data-sly-test="${properties.linkTarget}" href="${buttonlink.externalizedLink}" title="${properties.linkTitle}" class="button button--${colortheme.contentSlider.buttonVariation}">
            <span>${properties.linkTitle}</span>
            </a>              
         </div>
      </div>
   </div>
</div>