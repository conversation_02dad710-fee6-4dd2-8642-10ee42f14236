<div class="cta-child-inner" data-sly-use.pageUtils="/apps/revu-global/components/utils/pageUtils.js" data-sly-use.basePage="com.eon.dist.dke.aem.core.models.BaseModel">
    <section data-sly-test="${!basePage.basePageModel.isConsentManagementActive}" data-mh="cta-inner">
        <h3 data-sly-test="${properties.headline}" class="footer_inner-headline">${properties.headline}</h3>
        <p  data-sly-test="${properties.text}">${properties.text}</p>
    </section>
    <section data-sly-test="${basePage.basePageModel.isConsentManagementActive}"  data-mh="cta-inner">
        <h3 data-sly-test="${properties.headline}" class="footer_inner-headline">${properties.headline}</h3>
        <p  data-sly-test="${properties.text}">${properties.text}</p>

        <div data-sly-test="${(properties.ucInfo || properties.infoText) && inheritedPageProperties.omnichannelAppId == null}" class="${pageUtils.projectPage.name}_fotrcht_uc_info_msg" style="display:none;">
        	 <!-- iAdvise -->
            <p class="uc-text-embedding">${properties.infoText}</p>
            <div data-sly-test="${properties.ucInfo && properties.ucAccept}" data-sly-use.colortheme="/apps/revu-global/components/page/basepage/colortheme.js"><a class="button button--${colortheme.footer.ctaButton}" onclick="uc.ucapi.showInfoModal('BykM5Vodi-X'); return false;" role="button"><span>${properties.ucInfo}</span></a></div>
        </div>

        <div data-sly-test="${(properties.ucInfo || properties.infoText) && inheritedPageProperties.omnichannelAppId != null}" class="${pageUtils.projectPage.name}_fotrcht_uc_info_msg" style="display:none;">
        	<!-- Microsoft Omnichannel -->
            <p class="uc-text-embedding">${properties.infoText}</p>
            <div data-sly-test="${properties.ucInfo && properties.ucAccept}" 
            	 data-sly-use.colortheme="/apps/revu-global/components/page/basepage/colortheme.js">
                 <a class="button button--${colortheme.footer.ctaButton}"
                 	onclick="uc.ucapi.showInfoModal('${inheritedPageProperties.onmnichannelCustomTemplateID == null ? '00xb_vkGq' : inheritedPageProperties.onmnichannelCustomTemplateID  @ context='unsafe'}'); return false;" 
                    role="button">
                    <span>${properties.ucInfo}</span>
                 </a>
            </div>
        </div>
    </section>

	<div data-sly-use.colortheme="/apps/revu-global/components/page/basepage/colortheme.js" data-sly-test="${inheritedPageProperties.omnichannelAppId == null}" class="${pageUtils.projectPage.name}_fotrcht_uc_btns" style="display:none;">
    	<!-- iAdvise -->
		<div data-sly-test="${properties.ucInfo && !properties.ucAccept}"><a class="button button--${colortheme.footer.ctaButton}" onclick="uc.ucapi.showInfoModal('BykM5Vodi-X'); return false;" role="button"><span>${properties.ucInfo}</span></a></div>
    	<div data-sly-test="${properties.ucAccept}"><a class="button button--${colortheme.footer.ctaButton}" onclick="uc.ucapi.setConsents([{templateId:'BykM5Vodi-X', status:true}]); window.location.reload();" role="button"><span>${properties.ucAccept}</span></a></div>
    </div>

    <div data-sly-use.colortheme="/apps/revu-global/components/page/basepage/colortheme.js" data-sly-test="${inheritedPageProperties.omnichannelAppId != null}" class="${pageUtils.projectPage.name}_fotrcht_uc_btns" style="display:none;">
    	<!-- Microsoft Omnichannel -->
		<div data-sly-test="${properties.ucInfo && !properties.ucAccept}">
        	<a class="button button--${colortheme.footer.ctaButton}" 
               onclick="uc.ucapi.showInfoModal('${inheritedPageProperties.onmnichannelCustomTemplateID == null ? '00xb_vkGq' : inheritedPageProperties.onmnichannelCustomTemplateID  @ context='unsafe'}'); return false;" 
               role="button">
               <span>${properties.ucInfo}</span>
            </a>
        </div>
    	<div data-sly-test="${properties.ucAccept}">
        	<a class="button button--${colortheme.footer.ctaButton}" 
               onclick="uc.ucapi.setConsents([{templateId:'${inheritedPageProperties.onmnichannelCustomTemplateID == null ? '00xb_vkGq' : inheritedPageProperties.onmnichannelCustomTemplateID  @ context='unsafe'}', status:true}]); window.location.reload();" 
               role="button">
               <span>${properties.ucAccept}</span>
            </a>
        </div>
    </div>
    
    <div data-sly-test="${wcmmode.disabled}" class="${pageUtils.projectPage.name}_chat-footer">
        <div id="${pageUtils.projectPage.name}_chat-footer_Online" style="display: none;" class="omnichat">
            <a href="javascript:void(0)">
                <button class="btn-live-chat">
                    <i>
<div class="circle-18 on">
</div>
</i>
                    <span>Chat starten</span>
                </button>
            </a>
        </div>

        <div id="${pageUtils.projectPage.name}_chat-footer_Offline" style="display: none;">
            <a href="javascript:void(0)">
                <button class="btn-live-chat">
                    <i>
<div class="circle-18 off">
</div>
</i>
                    <span>Chat offline</span>
                </button>
            </a>
        </div>

        <div id="${pageUtils.projectPage.name}_chat-footer_Busy" style="display: none;">
            <a href="javascript:void(0)">
			<button class="btn-live-chat">
                <i>
<div class="circle-18 busy"></div>
</i>
                <span>Chat  bitte warten</span>
                </button>
            </a>
        </div>

    </div>
</div>
<input data-sly-use.pageUtils="/apps/revu-global/components/utils/pageUtils.js" type="hidden" id="uc-whitelisted-pageUtils-projectPage-name" value="${pageUtils.projectPage.name @ context = 'unsafe'}"/>
<input data-sly-test="${inheritedPageProperties.onmnichannelCustomTemplateID != null}" type="hidden" id="onmnichannel-custom-template-id" value="${inheritedPageProperties.onmnichannelCustomTemplateID  @ context='unsafe'}"/>

<!-- Check CSP hash section in readme -->
<script data-sly-test="${inheritedPageProperties.omnichannelAppId == null}" integrity="sha256-AHa6ZVk46/SgZBCmZWZ7ss+EYKO/EV1N8MIEpRDJaog=">
window.addEventListener('load', (event) => {
    if (typeof uc === 'object' && uc !== null) {
    	var projectName = document.getElementById('uc-whitelisted-pageUtils-projectPage-name').value;
    	// iAdvize
    	if (typeof uc.whitelisted === 'object' && uc.whitelisted.value.size > 0 && uc.whitelisted.has('BykM5Vodi-X') === true) {
        	$("."+projectName+"_chat-footer").show();
            $("."+projectName+"_fotrcht_uc_info_msg").hide();
            $("."+projectName+"_fotrcht_uc_btns").hide();
        } else if ( typeof uc.whitelisted === 'object' && uc.whitelisted.has('BykM5Vodi-X') === false) {
        	$("."+projectName+"_chat-footer").hide();
            $("."+projectName+"_fotrcht_uc_info_msg").show();
			$("."+projectName+"_fotrcht_uc_btns").show();       
			//Adjust footer height again
            $.fn.matchHeight._update();
        }

    }
});
</script>
<!-- Check CSP hash section in readme -->
<script data-sly-test="${inheritedPageProperties.omnichannelAppId != null}"
		integrity="sha256-kjHrWGdaYqmZacM3BpwOBIOI1fyDRi3zaeog4X6Z13U=">
window.addEventListener('load', (event) => {
    if (typeof uc === 'object' && uc !== null) {
        var omnichannelCustomTemplateID = document.getElementById('onmnichannel-custom-template-id') ? document.getElementById('onmnichannel-custom-template-id').value : '00xb_vkGq';
        // Microsoft Omnichannel
        if (typeof uc.whitelisted === 'object' && uc.whitelisted.value.size > 0 && uc.whitelisted.has(omnichannelCustomTemplateID) === true) {

            //Load Omnichannel Chat button
            window.addEventListener("lcw:ready", function handleLivechatReadyEvent(){
                $("."+projectName+"_chat-footer").show();
				$('ul.contacts-channel__list > li').each(function (indx, itm) {
                    var chatElemnt = $(itm).children("div."+projectName+"_chat-sidebar").hasClass(projectName+"_chat-sidebar");
                    if (chatElemnt === true) {
                        $(itm).show();
                    }
		    	});

                $(".omnichat").css("display","block");
            });
            $("."+projectName+"_fotrcht_uc_info_msg").hide();
            $("."+projectName+"_fotrcht_uc_btns").hide();
        } else if ( typeof uc.whitelisted === 'object' && uc.whitelisted.has(omnichannelCustomTemplateID) === false) {
        	$("."+projectName+"_chat-footer").hide();
            $(".omnichat").css("display","none");
            $("."+projectName+"_fotrcht_uc_info_msg").show();
			$("."+projectName+"_fotrcht_uc_btns").show();       
			//Adjust footer height again
            $.fn.matchHeight._update();
            $('ul.contacts-channel__list > li').each(function (indx, itm) {
                var chatElemnt = $(itm).children("div."+projectName+"_chat-sidebar").hasClass(projectName+"_chat-sidebar");
                if (chatElemnt === true) {
                    $(itm).hide();
                }
		   	});
        }

    }
});
</script>

