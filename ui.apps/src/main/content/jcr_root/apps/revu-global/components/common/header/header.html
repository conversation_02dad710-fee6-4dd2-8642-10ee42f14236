<header class="header">
	
	<sly data-sly-include="headertop.html" data-sly-unwrap></sly> 
	
	<div class="dividing-line hidden-xs hidden-sm hidden-md ${properties.biggerLogo ? 'dividing-line-mrgn' : ''}"></div>
	
	<div class="container-fluid">
		<div class="header-layer-wrapper">        
	        <sly data-sly-include="contact.html" data-sly-unwrap></sly>               
	        <sly data-sly-include="search.html" data-sly-unwrap></sly>
	    </div>
	    <div class="container" data-sly-use.lib="revu-global/components/common/includes/templateLib.html" data-sly-test="${!properties.disableServicePortal && properties.servicePortalPath}">
			<a class="portal-link pull-right netz-service" href="${properties.servicePortalPath}" title="${properties.servicePortalLabel}" target="${properties.servicePortalTarget ? '_blank': ''}">
				<i>
				  <sly data-sly-call="${lib.inlinesvg @ assetPath=properties.servicePortalIcon, title=properties.servicePortalLabel, width='36', height='36'}"></sly>
				</i>
				<span>${properties.servicePortalLabel}</span>
			</a>
	    </div>
    </div>
    
    <sly data-sly-use.optionalheadermodel="${'com.eon.dist.dke.aem.core.models.OptionalNavigationModel'}">
     <sly data-sly-test = "${optionalheadermodel.optionalnavigationlevel == 3}">
          <sly data-sly-include="headerbottom.html"></sly> 
     </sly>
	 <sly data-sly-test = "${optionalheadermodel.optionalnavigationlevel == 4}">
         <nav class= "visible-xs visible-sm visible-md meta_nav_clr">
              <sly data-sly-include="headerbottom.html"></sly> 
         </nav>
     </sly>
    </sly>
    
    <sly data-sly-test="${!properties.checkBoxLanguage == false}">
    	<sly data-sly-include="languageswitch.html"/>
    </sly>

</header>
